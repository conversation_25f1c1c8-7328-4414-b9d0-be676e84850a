import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../../core/constants/app_colors.dart';
import '../../../../../core/models/patient_model.dart';
import '../../../data/repositories/patients_repository.dart';
import '../../widgets/weight_edit_dialog.dart';

class PatientInfoTab extends StatefulWidget {
  final PatientModel patient;
  final Function(PatientModel)? onPatientUpdated;

  const PatientInfoTab({
    super.key,
    required this.patient,
    this.onPatientUpdated,
  });

  @override
  State<PatientInfoTab> createState() => _PatientInfoTabState();
}

class _PatientInfoTabState extends State<PatientInfoTab> {
  late PatientModel _currentPatient;
  final PatientsRepository _patientsRepository = PatientsRepository();

  @override
  void initState() {
    super.initState();
    _currentPatient = widget.patient;
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            _buildSectionHeader('البيانات الشخصية', Icons.person),
            SizedBox(height: 16.h),

            // Personal Information Cards
            _buildInfoCard('الاسم الكامل', _currentPatient.name, Icons.person),
            _buildInfoCard(
              'العمر',
              '${_currentPatient.age ?? 'غير محدد'} سنة',
              Icons.cake,
            ),
            _buildInfoCard(
              'تاريخ الميلاد',
              _currentPatient.birthDate != null
                  ? DateFormat('dd/MM/yyyy').format(_currentPatient.birthDate!)
                  : 'غير محدد',
              Icons.calendar_today,
            ),
            _buildPhoneInfoCard(
              'رقم الهاتف',
              _currentPatient.phone ?? 'غير محدد',
              Icons.phone,
            ),
            _buildInfoCard(
              'البريد الإلكتروني',
              _currentPatient.email ?? 'غير محدد',
              Icons.email,
            ),
            _buildInfoCard(
              'الجنس',
              _getGenderText(_currentPatient.gender),
              Icons.wc,
            ),

            SizedBox(height: 24.h),

            // Physical Information
            _buildSectionHeader('المعلومات الجسدية', Icons.fitness_center),
            SizedBox(height: 16.h),

            Row(
              children: [
                Expanded(
                  child: _buildInfoCard(
                    'الطول',
                    _currentPatient.height != null
                        ? '${_currentPatient.height} سم'
                        : 'غير محدد',
                    Icons.height,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(child: _buildWeightCard()),
              ],
            ),

            if (_currentPatient.height != null &&
                _currentPatient.weight != null) ...[
              SizedBox(height: 12.h),
              _buildBMICard(),
            ],

            SizedBox(height: 24.h),

            // Account Information
            _buildSectionHeader('معلومات الحساب', Icons.account_circle),
            SizedBox(height: 16.h),

            _buildInfoCard(
              'نوع العضوية',
              _currentPatient.isPremium ? 'عضوية مميزة' : 'عضوية عادية',
              _currentPatient.isPremium ? Icons.star : Icons.person,
            ),
            _buildInfoCard(
              'تاريخ التسجيل',
              DateFormat(
                'dd/MM/yyyy - HH:mm',
              ).format(_currentPatient.createdAt),
              Icons.date_range,
            ),
            _buildInfoCard(
              'آخر تحديث',
              DateFormat(
                'dd/MM/yyyy - HH:mm',
              ).format(_currentPatient.updatedAt),
              Icons.update,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _onRefresh() async {
    // محاكاة تحديث البيانات
    await Future.delayed(const Duration(seconds: 1));
    // يمكن إضافة منطق تحديث البيانات هنا إذا لزم الأمر
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(icon, color: AppColors.primary, size: 20.w),
        ),
        SizedBox(width: 12.w),
        Text(
          title,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoCard(String label, String value, IconData icon) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray200),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(icon, color: AppColors.primary, size: 20.w),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBMICard() {
    final bmi = _calculateBMI();
    final bmiCategory = _getBMICategory(bmi);
    final bmiColor = _getBMIColor(bmi);

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: bmiColor.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: bmiColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(Icons.calculate, color: bmiColor, size: 24.w),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مؤشر كتلة الجسم (BMI)',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 4.h),
                Row(
                  children: [
                    Text(
                      bmi.toStringAsFixed(1),
                      style: TextStyle(
                        fontSize: 20.sp,
                        color: bmiColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      bmiCategory,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: bmiColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getGenderText(String? gender) {
    switch (gender?.toLowerCase()) {
      case 'male':
        return 'ذكر';
      case 'female':
        return 'أنثى';
      default:
        return 'غير محدد';
    }
  }

  double _calculateBMI() {
    if (_currentPatient.height == null || _currentPatient.weight == null)
      return 0.0;
    final heightInMeters = _currentPatient.height! / 100;
    return _currentPatient.weight! / (heightInMeters * heightInMeters);
  }

  String _getBMICategory(double bmi) {
    if (bmi < 18.5) return 'نقص في الوزن';
    if (bmi < 25) return 'وزن طبيعي';
    if (bmi < 30) return 'زيادة في الوزن';
    return 'سمنة';
  }

  Color _getBMIColor(double bmi) {
    if (bmi < 18.5) return AppColors.info;
    if (bmi < 25) return AppColors.success;
    if (bmi < 30) return AppColors.warning;
    return AppColors.error;
  }

  Widget _buildPhoneInfoCard(String label, String value, IconData icon) {
    final hasPhone =
        _currentPatient.phone != null && _currentPatient.phone!.isNotEmpty;

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray200),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(icon, color: AppColors.primary, size: 20.w),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 4.h),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        value,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    if (hasPhone) ...[
                      SizedBox(width: 8.w),
                      // Call button
                      InkWell(
                        onTap: () => _makePhoneCall(_currentPatient.phone!),
                        child: Container(
                          padding: EdgeInsets.all(6.w),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(6.r),
                          ),
                          child: Icon(
                            Icons.phone,
                            size: 16.w,
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                      SizedBox(width: 4.w),
                      // Copy button
                      InkWell(
                        onTap: () => _copyToClipboard(_currentPatient.phone!),
                        child: Container(
                          padding: EdgeInsets.all(6.w),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(6.r),
                          ),
                          child: Icon(
                            Icons.copy,
                            size: 16.w,
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _makePhoneCall(String phoneNumber) async {
    try {
      // تنظيف رقم الهاتف من المسافات والرموز الخاصة
      final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

      // استخدام tel: scheme مع DIAL mode
      final Uri phoneUri = Uri.parse('tel:$cleanNumber');

      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      debugPrint('❌ Error making phone call: $e');
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
  }

  Widget _buildWeightCard() {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray200),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(Icons.monitor_weight, color: AppColors.primary, size: 20.w),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الوزن',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  _currentPatient.weight != null
                      ? '${_currentPatient.weight} كجم'
                      : 'غير محدد',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          // Edit button
          InkWell(
            onTap: _showWeightEditDialog,
            child: Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(Icons.edit, color: AppColors.primary, size: 18.w),
            ),
          ),
        ],
      ),
    );
  }

  void _showWeightEditDialog() {
    showDialog(
      context: context,
      builder:
          (context) => WeightEditDialog(
            currentWeight: _currentPatient.weight,
            onWeightUpdated: _updatePatientWeight,
          ),
    );
  }

  Future<void> _updatePatientWeight(double newWeight) async {
    try {
      final updatedPatient = await _patientsRepository.updatePatientWeight(
        _currentPatient.id,
        newWeight,
      );

      setState(() {
        _currentPatient = updatedPatient;
      });

      // Notify parent widget about the update
      if (widget.onPatientUpdated != null) {
        widget.onPatientUpdated!(updatedPatient);
      }
    } catch (e) {
      rethrow; // Let the dialog handle the error display
    }
  }
}
