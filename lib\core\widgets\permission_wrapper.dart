import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_colors.dart';
import '../services/permissions_notifier.dart';

class PermissionWrapper extends StatelessWidget {
  final Widget child;
  final String permissionKey;
  final String featureName;

  const PermissionWrapper({
    super.key,
    required this.child,
    required this.permissionKey,
    required this.featureName,
  });

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: PermissionsNotifier(),
      builder: (context, _) {
        final notifier = PermissionsNotifier();

        if (notifier.isLoading) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        final hasPermission = notifier.getPermission(permissionKey);

        if (!hasPermission) {
          return Scaffold(
            backgroundColor: AppColors.background,
            appBar: AppBar(
              title: Text(featureName),
              backgroundColor: AppColors.white,
              elevation: 0,
            ),
            body: Center(
              child: Padding(
                padding: EdgeInsets.all(32.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.all(24.w),
                      decoration: BoxDecoration(
                        color: AppColors.error.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.lock_outline,
                        size: 64.w,
                        color: AppColors.error,
                      ),
                    ),
                    SizedBox(height: 32.h),
                    Text(
                      'غير مصرح بالوصول',
                      style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 16.h),
                    Text(
                      'ليس لديك صلاحية للوصول إلى $featureName',
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 32.h),
                    Container(
                      padding: EdgeInsets.all(16.w),
                      decoration: BoxDecoration(
                        color: AppColors.warning.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(
                          color: AppColors.warning.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: AppColors.warning,
                            size: 20.w,
                          ),
                          SizedBox(width: 12.w),
                          Expanded(
                            child: Text(
                              'يرجى التواصل مع مدير النظام لتفعيل هذه الصلاحية',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: AppColors.warning,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        return child;
      },
    );
  }
}
