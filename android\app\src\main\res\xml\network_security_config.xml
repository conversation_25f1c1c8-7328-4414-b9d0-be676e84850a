<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">supabase.co</domain>
        <domain includeSubdomains="true">xwxeauofbzedfzaogzzy.supabase.co</domain>
        <domain includeSubdomains="true">googleapis.com</domain>
        <domain includeSubdomains="true">firebase.googleapis.com</domain>
        <domain includeSubdomains="true">fcm.googleapis.com</domain>
        <domain includeSubdomains="true">cloudfunctions.net</domain>
        <domain includeSubdomains="true">us-central1-deit-rx-30741.cloudfunctions.net</domain>
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">localhost</domain>
    </domain-config>
    
    <!-- Allow all HTTPS traffic -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
