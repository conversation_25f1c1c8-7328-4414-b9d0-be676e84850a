import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'core/network/supabase_client.dart';
import 'core/theme/app_theme.dart';
import 'core/constants/app_strings.dart';
import 'core/bloc/app_bloc_observer.dart';
import 'features/auth/data/repositories/auth_repository.dart';
import 'features/auth/presentation/bloc/auth_bloc.dart';
import 'features/patients/data/repositories/patients_repository.dart';
import 'features/patients/presentation/bloc/patients_bloc.dart';
import 'features/appointments/data/repositories/appointments_repository.dart';
import 'features/appointments/data/repositories/time_slots_repository.dart';
import 'features/appointments/data/repositories/holidays_repository.dart';
import 'features/appointments/data/repositories/clinic_info_repository.dart';
import 'features/appointments/data/repositories/appointment_booking_repository.dart';
import 'features/appointments/presentation/bloc/appointments_bloc.dart';
import 'features/appointments/presentation/bloc/time_slots_bloc.dart';
import 'features/appointments/presentation/bloc/holidays_bloc.dart';
import 'features/appointments/presentation/bloc/clinic_info_bloc.dart';
import 'features/appointments/presentation/bloc/appointment_booking_bloc.dart';
import 'features/patients/data/repositories/weekly_results_repository.dart';
import 'features/patients/presentation/bloc/weekly_results_bloc.dart';
import 'features/patients/data/repositories/lab_tests_repository.dart';
import 'features/patients/presentation/bloc/lab_tests_bloc.dart';
import 'features/patients/data/repositories/medical_info_repository.dart';
import 'features/patients/presentation/bloc/medical_info_bloc.dart';
import 'features/patients/data/repositories/reminders_repository.dart';
import 'features/patients/presentation/bloc/reminders_bloc.dart';
import 'features/products/data/repositories/products_repository.dart';
import 'features/products/presentation/bloc/products_bloc.dart';
import 'features/articles/data/repositories/articles_repository.dart';
import 'features/articles/presentation/bloc/articles_bloc.dart';
import 'features/categories/data/repositories/categories_repository.dart';
import 'features/categories/presentation/bloc/categories_bloc.dart';
import 'features/articles/data/repositories/article_categories_repository.dart';
import 'features/articles/presentation/bloc/article_categories_bloc.dart';
import 'features/auth/presentation/pages/login_page.dart';
import 'features/main/presentation/pages/main_page.dart';
import 'features/splash/presentation/pages/splash_page.dart';
import 'core/services/permissions_notifier.dart';
import 'core/services/notification_automation_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Lock orientation to portrait only
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set up BlocObserver for debugging and error tracking
  Bloc.observer = AppBlocObserver();

  // Initialize Supabase
  try {
    await SupabaseConfig.initialize();
  } catch (e) {
    debugPrint('Supabase initialization error: $e');
  }

  // Initialize Permissions
  await PermissionsNotifier().initialize();

  // ✅ Start notification automation service (using Firebase HTTP v1 API)
  NotificationAutomationService.startAutomation();
  debugPrint('🔔 Notifications are handled by app with Firebase HTTP v1 API');

  runApp(const DietRxApp());
}

class DietRxApp extends StatelessWidget {
  const DietRxApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 813),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (context) => AuthBloc(authRepository: AuthRepository()),
            ),
            BlocProvider(
              create:
                  (context) =>
                      PatientsBloc(patientsRepository: PatientsRepository()),
            ),
            BlocProvider(
              create:
                  (context) => AppointmentsBloc(
                    appointmentsRepository: AppointmentsRepository(),
                  ),
            ),
            BlocProvider(
              create:
                  (context) =>
                      TimeSlotsBloc(timeSlotsRepository: TimeSlotsRepository()),
            ),
            BlocProvider(
              create:
                  (context) =>
                      HolidaysBloc(holidaysRepository: HolidaysRepository()),
            ),
            BlocProvider(
              create:
                  (context) => ClinicInfoBloc(
                    clinicInfoRepository: ClinicInfoRepository(),
                  ),
            ),
            BlocProvider(
              create:
                  (context) => AppointmentBookingBloc(
                    appointmentBookingRepository:
                        AppointmentBookingRepository(),
                    appointmentsRepository: AppointmentsRepository(),
                  ),
            ),
            BlocProvider(
              create:
                  (context) => WeeklyResultsBloc(
                    weeklyResultsRepository: WeeklyResultsRepository(),
                  ),
            ),
            BlocProvider(
              create:
                  (context) =>
                      LabTestsBloc(labTestsRepository: LabTestsRepository()),
            ),
            BlocProvider(
              create:
                  (context) => MedicalInfoBloc(
                    medicalInfoRepository: MedicalInfoRepository(),
                  ),
            ),
            BlocProvider(
              create:
                  (context) =>
                      RemindersBloc(remindersRepository: RemindersRepository()),
            ),
            BlocProvider(
              create:
                  (context) =>
                      ProductsBloc(productsRepository: ProductsRepository()),
            ),
            BlocProvider(
              create:
                  (context) =>
                      ArticlesBloc(articlesRepository: ArticlesRepository()),
            ),
            BlocProvider(
              create:
                  (context) => CategoriesBloc(
                    categoriesRepository: CategoriesRepository(),
                  ),
            ),
            BlocProvider(
              create:
                  (context) => ArticleCategoriesBloc(
                    repository: ArticleCategoriesRepository(),
                  ),
            ),
          ],
          child: MaterialApp(
            title: AppStrings.appName,
            theme: AppTheme.lightTheme,
            debugShowCheckedModeBanner: false,

            // RTL Support for Arabic
            locale: const Locale('ar', 'SA'),
            supportedLocales: const [
              Locale('ar', 'SA'), // Arabic
              Locale('en', 'US'), // English
            ],
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],

            // Force RTL layout
            builder: (context, child) {
              return Directionality(
                textDirection: TextDirection.rtl,
                child: child!,
              );
            },

            home: const SplashPage(),
            routes: {
              '/login': (context) => const LoginPage(),
              '/main': (context) => const MainPage(),
            },
          ),
        );
      },
    );
  }
}
