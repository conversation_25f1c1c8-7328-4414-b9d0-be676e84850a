import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/lab_test_model.dart';
import '../../../../core/widgets/loading_dialog.dart';
import '../bloc/lab_tests_bloc.dart';
import '../bloc/lab_tests_event.dart';
import '../bloc/lab_tests_state.dart';

class LabTestFormDialog extends StatefulWidget {
  final String patientId;
  final LabTestModel? labTest;

  const LabTestFormDialog({
    super.key,
    required this.patientId,
    this.labTest,
  });

  @override
  State<LabTestFormDialog> createState() => _LabTestFormDialogState();
}

class _LabTestFormDialogState extends State<LabTestFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _testNameController;
  late TextEditingController _resultsController;
  late TextEditingController _doctorNotesController;

  String _selectedTestType = 'blood';
  DateTime _selectedDate = DateTime.now();
  bool? _isNormal;
  String? _uploadedImageUrl;
  final ImagePicker _picker = ImagePicker();

  final List<Map<String, String>> _testTypes = [
    {'value': 'blood', 'label': 'فحص دم'},
    {'value': 'urine', 'label': 'فحص بول'},
    {'value': 'x_ray', 'label': 'أشعة سينية'},
    {'value': 'mri', 'label': 'رنين مغناطيسي'},
    {'value': 'ct_scan', 'label': 'أشعة مقطعية'},
    {'value': 'ultrasound', 'label': 'موجات فوق صوتية'},
    {'value': 'other', 'label': 'فحص آخر'},
  ];

  @override
  void initState() {
    super.initState();
    _testNameController = TextEditingController(
      text: widget.labTest?.testName ?? '',
    );
    _resultsController = TextEditingController(
      text: widget.labTest?.results ?? '',
    );
    _doctorNotesController = TextEditingController(
      text: widget.labTest?.doctorNotes ?? '',
    );

    if (widget.labTest != null) {
      _selectedTestType = widget.labTest!.testType ?? 'blood';
      _selectedDate = widget.labTest!.testDate;
      _isNormal = widget.labTest!.isNormal;
      _uploadedImageUrl = widget.labTest!.imageUrl;
    }
  }

  @override
  void dispose() {
    _testNameController.dispose();
    _resultsController.dispose();
    _doctorNotesController.dispose();
    super.dispose();
  }

  void _saveLabTest() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Show loading dialog immediately
    final isUpdate = widget.labTest != null;
    LoadingDialog.show(
      context,
      isUpdate ? 'جاري تحديث الفحص...' : 'جاري إضافة الفحص...'
    );

    final labTest = LabTestModel(
      id: widget.labTest?.id ?? '',
      patientId: widget.patientId,
      testName: _testNameController.text.trim(),
      testDate: _selectedDate,
      testType: _selectedTestType,
      imageUrl: _uploadedImageUrl,
      results: _resultsController.text.trim().isNotEmpty
          ? _resultsController.text.trim()
          : null,
      doctorNotes: _doctorNotesController.text.trim().isNotEmpty
          ? _doctorNotesController.text.trim()
          : null,
      isNormal: _isNormal,
      createdAt: widget.labTest?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
    );

    if (widget.labTest == null) {
      // Add new lab test
      context.read<LabTestsBloc>().add(AddLabTest(labTest: labTest));
    } else {
      // Update existing lab test
      context.read<LabTestsBloc>().add(UpdateLabTest(labTest: labTest));
    }

    Navigator.of(context).pop();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020), // تاريخ بداية مفتوح
      lastDate: DateTime(2030), // تاريخ نهاية مفتوح
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: AppColors.white,
              surface: AppColors.white,
              onSurface: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        // Upload image
        context.read<LabTestsBloc>().add(
          UploadLabTestImage(
            imageFile: image,
            patientId: widget.patientId,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختيار الصورة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showImageSourceDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر مصدر الصورة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('الكاميرا'),
              onTap: () {
                Navigator.of(context).pop();
                _pickImage(ImageSource.camera);
              },
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('المعرض'),
              onTap: () {
                Navigator.of(context).pop();
                _pickImage(ImageSource.gallery);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<LabTestsBloc, LabTestsState>(
      listener: (context, state) {
        if (state is LabTestImageUploaded) {
          setState(() {
            _uploadedImageUrl = state.imageUrl;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم رفع الصورة بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
        } else if (state is LabTestsError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppColors.error,
            ),
          );
        }
      },
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.85,
            maxWidth: MediaQuery.of(context).size.width * 0.9,
          ),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Fixed header
                Container(
                  padding: EdgeInsets.all(24.w),
                  child: Text(
                    widget.labTest == null ? 'إضافة فحص مخبري' : 'تعديل الفحص المخبري',
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),

                // Scrollable content
                Flexible(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Test name
                        SizedBox(
                          height: 80.h,
                          child: TextFormField(
                            controller: _testNameController,
                            decoration: InputDecoration(
                              labelText: 'اسم الفحص *',
                              hintText: 'أدخل اسم الفحص',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              alignLabelWithHint: true,
                            ),
                            maxLines: null,
                            expands: true,
                            textAlignVertical: TextAlignVertical.top,
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return 'يرجى إدخال اسم الفحص';
                              }
                              return null;
                            },
                          ),
                        ),
                        SizedBox(height: 16.h),

                        // Test type
                        DropdownButtonFormField<String>(
                          value: _selectedTestType,
                          decoration: InputDecoration(
                            labelText: 'نوع الفحص *',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                          ),
                          items: _testTypes.map((type) {
                            return DropdownMenuItem<String>(
                              value: type['value'],
                              child: Text(type['label']!),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _selectedTestType = value;
                              });
                            }
                          },
                          validator: (value) {
                            if (value?.isEmpty ?? true) {
                              return 'يرجى اختيار نوع الفحص';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: 16.h),

                        // Date selector
                        InkWell(
                          onTap: _selectDate,
                          child: Container(
                            padding: EdgeInsets.all(16.w),
                            decoration: BoxDecoration(
                              border: Border.all(color: AppColors.gray300),
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.calendar_today, color: AppColors.primary),
                                SizedBox(width: 12.w),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'تاريخ الفحص',
                                        style: TextStyle(
                                          fontSize: 12.sp,
                                          color: AppColors.textSecondary,
                                        ),
                                      ),
                                      SizedBox(height: 4.h),
                                      Text(
                                        DateFormat('dd/MM/yyyy').format(_selectedDate),
                                        style: TextStyle(
                                          fontSize: 16.sp,
                                          color: AppColors.textPrimary,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(width: 8.w),
                                Icon(Icons.arrow_drop_down, color: AppColors.textSecondary),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: 16.h),

                        // Image upload section
                        Container(
                          padding: EdgeInsets.all(16.w),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.gray300),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.image, color: AppColors.primary),
                                  SizedBox(width: 8.w),
                                  Text(
                                    'صورة الفحص',
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.textPrimary,
                                    ),
                                  ),
                                  const Spacer(),
                                  TextButton.icon(
                                    onPressed: _showImageSourceDialog,
                                    icon: const Icon(Icons.add_a_photo),
                                    label: const Text('رفع صورة'),
                                  ),
                                ],
                              ),
                              if (_uploadedImageUrl != null) ...[
                                SizedBox(height: 8.h),
                                Container(
                                  height: 100.h,
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8.r),
                                    border: Border.all(color: AppColors.gray200),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8.r),
                                    child: Image.network(
                                      _uploadedImageUrl!,
                                      fit: BoxFit.cover,
                                      errorBuilder: (context, error, stackTrace) {
                                        return Container(
                                          color: AppColors.gray100,
                                          child: const Icon(Icons.error),
                                        );
                                      },
                                    ),
                                  ),
                                ),
                                SizedBox(height: 8.h),
                                Row(
                                  children: [
                                    TextButton.icon(
                                      onPressed: () {
                                        setState(() {
                                          _uploadedImageUrl = null;
                                        });
                                      },
                                      icon: const Icon(Icons.delete, color: Colors.red),
                                      label: const Text('حذف الصورة', style: TextStyle(color: Colors.red)),
                                    ),
                                  ],
                                ),
                              ],
                            ],
                          ),
                        ),
                        SizedBox(height: 16.h),

                        // Results
                        SizedBox(
                          height: 120.h,
                          child: TextFormField(
                            controller: _resultsController,
                            decoration: InputDecoration(
                              labelText: 'النتائج',
                              hintText: 'أدخل نتائج الفحص',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              alignLabelWithHint: true,
                            ),
                            maxLines: null,
                            expands: true,
                            textAlignVertical: TextAlignVertical.top,
                          ),
                        ),
                        SizedBox(height: 16.h),

                        // Doctor notes
                        SizedBox(
                          height: 120.h,
                          child: TextFormField(
                            controller: _doctorNotesController,
                            decoration: InputDecoration(
                              labelText: 'ملاحظات الطبيب',
                              hintText: 'أدخل ملاحظات الطبيب',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              alignLabelWithHint: true,
                            ),
                            maxLines: null,
                            expands: true,
                            textAlignVertical: TextAlignVertical.top,
                          ),
                        ),
                        SizedBox(height: 16.h),

                        // Normal/Abnormal status
                        Container(
                          padding: EdgeInsets.all(16.w),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.gray300),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'حالة النتيجة',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                              SizedBox(height: 8.h),
                              Row(
                                children: [
                                  Expanded(
                                    child: RadioListTile<bool?>(
                                      title: const Text('طبيعي'),
                                      value: true,
                                      groupValue: _isNormal,
                                      onChanged: (value) {
                                        setState(() {
                                          _isNormal = value;
                                        });
                                      },
                                      activeColor: AppColors.success,
                                    ),
                                  ),
                                  Expanded(
                                    child: RadioListTile<bool?>(
                                      title: const Text('غير طبيعي'),
                                      value: false,
                                      groupValue: _isNormal,
                                      onChanged: (value) {
                                        setState(() {
                                          _isNormal = value;
                                        });
                                      },
                                      activeColor: AppColors.error,
                                    ),
                                  ),
                                ],
                              ),
                              RadioListTile<bool?>(
                                title: const Text('غير محدد'),
                                value: null,
                                groupValue: _isNormal,
                                onChanged: (value) {
                                  setState(() {
                                    _isNormal = value;
                                  });
                                },
                                activeColor: AppColors.gray400,
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 24.h),
                      ],
                    ),
                  ),
                ),

                // Fixed footer with action buttons
                Container(
                  padding: EdgeInsets.all(24.w),
                  child: Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: OutlinedButton.styleFrom(
                            padding: EdgeInsets.symmetric(vertical: 12.h),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                          ),
                          child: Text(
                            'إلغاء',
                            style: TextStyle(fontSize: 16.sp),
                          ),
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _saveLabTest,
                          style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.symmetric(vertical: 12.h),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                          ),
                          child: Text(
                            widget.labTest == null ? 'إضافة' : 'تحديث',
                            style: TextStyle(fontSize: 16.sp),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
