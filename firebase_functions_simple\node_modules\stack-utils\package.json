{"name": "stack-utils", "version": "2.0.6", "description": "Captures and cleans stack traces", "license": "MIT", "repository": "tapjs/stack-utils", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "github.com/jamestalmage"}, "engines": {"node": ">=10"}, "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "tap": {"check-coverage": true}, "files": ["index.js"], "dependencies": {"escape-string-regexp": "^2.0.0"}, "devDependencies": {"bluebird": "^3.7.2", "coveralls": "^3.0.9", "nested-error-stacks": "^2.1.0", "pify": "^4.0.1", "q": "^1.5.1", "source-map-support": "^0.5.20", "tap": "^16.3.0"}}