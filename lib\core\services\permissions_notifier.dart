import 'package:flutter/foundation.dart';
import 'permissions_service.dart';

class PermissionsNotifier extends ChangeNotifier {
  static final PermissionsNotifier _instance = PermissionsNotifier._internal();
  factory PermissionsNotifier() => _instance;
  PermissionsNotifier._internal();

  Map<String, bool> _permissions = {};
  bool _isLoading = true;

  Map<String, bool> get permissions => _permissions;
  bool get isLoading => _isLoading;

  // Initialize permissions
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();
    
    _permissions = await PermissionsService.getAllPermissions();
    _isLoading = false;
    notifyListeners();
  }

  // Update specific permission
  Future<void> updatePermission(String key, bool value) async {
    _permissions[key] = value;
    
    switch (key) {
      case 'appointments':
        await PermissionsService.setAppointmentsPermission(value);
        break;
      case 'patients':
        await PermissionsService.setPatientsPermission(value);
        break;
      case 'products':
        await PermissionsService.setProductsPermission(value);
        break;
      case 'articles':
        await PermissionsService.setArticlesPermission(value);
        break;
    }
    
    notifyListeners();
  }

  // Get specific permission
  bool getPermission(String key) {
    return _permissions[key] ?? true;
  }

  // Check if user is admin
  bool get isAdmin {
    return _permissions.values.every((permission) => permission);
  }

  // Reset all permissions
  Future<void> resetPermissions() async {
    await PermissionsService.resetPermissions();
    _permissions = await PermissionsService.getAllPermissions();
    notifyListeners();
  }

  // Refresh permissions from storage
  Future<void> refresh() async {
    _permissions = await PermissionsService.getAllPermissions();
    notifyListeners();
  }
}
