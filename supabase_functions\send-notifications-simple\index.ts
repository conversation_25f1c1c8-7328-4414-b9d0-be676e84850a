import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    console.log('🔍 Checking for due notifications...')
    
    const startTime = Date.now()
    
    // Get current time in Riyadh timezone
    const now = new Date()
    // Convert to Riyadh time (UTC+3)
    const riyadhTime = new Date(now.getTime() + (3 * 60 * 60 * 1000))
    const currentTime = `${riyadhTime.getHours().toString().padStart(2, '0')}:${riyadhTime.getMinutes().toString().padStart(2, '0')}`
    const currentDayOfWeek = riyadhTime.getDay() === 0 ? 7 : riyadhTime.getDay()
    
    console.log(`Current Riyadh time: ${currentTime}, Day: ${currentDayOfWeek}`)
    
    // Get due notifications
    const { data: notifications, error } = await supabase
      .from('scheduled_notifications')
      .select('*')
      .eq('is_active', true)
      .eq('scheduled_time', currentTime)
    
    if (error) {
      console.error('❌ Error fetching notifications:', error)
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    
    // Filter by day of week
    const dueNotifications = notifications.filter(notification => {
      const daysOfWeek = notification.days_of_week || []
      return daysOfWeek.includes(currentDayOfWeek)
    })
    
    console.log(`📊 Found ${dueNotifications.length} due notifications`)
    
    // Process each notification
    let successCount = 0
    for (const notification of dueNotifications) {
      const success = await processNotification(notification, supabase)
      if (success) successCount++
    }
    
    const endTime = Date.now()
    const duration = endTime - startTime
    
    const result = {
      success: true,
      processed: dueNotifications.length,
      successful: successCount,
      failed: dueNotifications.length - successCount,
      time: currentTime,
      day: currentDayOfWeek,
      duration: `${duration}ms`
    }
    
    if (dueNotifications.length > 0) {
      console.log(`✅ Processed ${dueNotifications.length} notifications in ${duration}ms`)
    } else {
      console.log(`ℹ️ No due notifications found (${duration}ms)`)
    }
    
    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    console.error('❌ Error in scheduled notifications:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

async function processNotification(notification: any, supabase: any): Promise<boolean> {
  try {
    const patientId = notification.patient_id
    
    // Get patient info
    const { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('auth_id, name')
      .eq('id', patientId)
      .single()
    
    if (patientError || !patient) {
      console.error(`❌ Patient not found: ${patientId}`)
      return false
    }
    
    // Get FCM tokens
    const { data: tokens, error: tokensError } = await supabase
      .from('user_fcm_tokens')
      .select('fcm_token')
      .eq('user_id', patient.auth_id)
      .eq('is_active', true)
    
    if (tokensError || !tokens || tokens.length === 0) {
      console.error(`❌ No FCM tokens found for patient: ${patientId} (auth_id: ${patient.auth_id})`)
      return false
    }
    
    console.log(`📱 Found ${tokens.length} FCM token(s) for patient: ${patient.name}`)
    
    // Prepare notification message
    const title = getNotificationTitle(notification.notification_type)
    const body = `مرحباً ${patient.name}، ${notification.body}`
    
    // For now, just log the notification (since we don't have FCM setup)
    // In a real implementation, you'd send via FCM here
    console.log(`📧 Notification for ${patient.name}: ${title} - ${body}`)
    
    // Log all notifications
    let allSuccess = true
    for (const tokenData of tokens) {
      try {
        await supabase.from('notification_logs').insert({
          scheduled_notification_id: notification.id,
          patient_id: patientId,
          fcm_token: tokenData.fcm_token,
          title: title,
          body: body,
          status: 'sent', // In real implementation, this would depend on FCM response
          firebase_response: { messageId: `simulated-${Date.now()}` }
        })
        
        console.log(`✅ Logged notification for token: ${tokenData.fcm_token.substring(0, 20)}...`)
        
      } catch (logError) {
        console.error(`❌ Failed to log notification:`, logError)
        allSuccess = false
      }
    }
    
    return allSuccess
    
  } catch (error) {
    console.error('❌ Error processing notification:', error)
    return false
  }
}

// Helper function to get notification title
function getNotificationTitle(notificationType: string): string {
  switch (notificationType) {
    case 'meal':
      return '🍽️ تذكير الوجبة'
    case 'exercise':
      return '🏃‍♂️ تذكير النشاط البدني'
    case 'medication':
      return '💊 تذكير الدواء'
    case 'water':
      return '💧 تذكير شرب الماء'
    default:
      return '🔔 تذكير من Diet Rx'
  }
}
