// Protocol Buffers - Google's data interchange format
// Copyright 2023 Google Inc.  All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file or at
// https://developers.google.com/open-source/licenses/bsd

syntax = "proto3";

package protobuf_editions_test.proto3;

import "google/protobuf/editions/codegen_tests/proto3_implicit.proto";

message Proto3ImportMessage {
  Proto3Implicit sub_message_field = 1;
}
