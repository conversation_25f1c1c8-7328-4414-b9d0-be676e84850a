@echo off
echo 🚀 نشر Supabase Edge Functions لتطبيق Diet Rx
echo ================================================
echo 💰 مجاني 100%% - بدون بطاقة دفع!
echo.

echo 📁 التحقق من Supabase CLI...
supabase --version
if %errorlevel% neq 0 (
    echo ❌ Supabase CLI غير مثبت
    echo 📦 تثبيت Supabase CLI:
    echo npm install -g supabase
    pause
    exit /b 1
)

echo.
echo 🔗 ربط المشروع...
supabase link --project-ref xwxeauofbzedfzaogzzy

echo.
echo 🔧 إعداد المتغيرات...
supabase secrets set SUPABASE_URL=https://xwxeauofbzedfzaogzzy.supabase.co
supabase secrets set SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh3eGVhdW9mYnplZGZ6YW9nenp5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTE2NTIxNiwiZXhwIjoyMDY0NzQxMjE2fQ.1dP_KuX_KVrx59uPuKnakYIcdxoDAcb3dOhBofyA6VA

echo.
echo 🚀 نشر Edge Function...
supabase functions deploy send-notifications-simple

echo.
echo ✅ تم النشر بنجاح!
echo.
echo 📋 الخطوات التالية:
echo 1. اذهب إلى Supabase Dashboard
echo 2. SQL Editor
echo 3. انسخ والصق محتوى supabase_cron_setup.sql
echo 4. اضغط Run لإعداد Cron Job
echo.
echo 🔗 روابط مفيدة:
echo Dashboard: https://supabase.com/dashboard/project/xwxeauofbzedfzaogzzy
echo Functions: https://supabase.com/dashboard/project/xwxeauofbzedfzaogzzy/functions
echo SQL Editor: https://supabase.com/dashboard/project/xwxeauofbzedfzaogzzy/sql
echo.
echo 🧪 اختبار Function:
echo curl -X POST "https://xwxeauofbzedfzaogzzy.supabase.co/functions/v1/send-notifications-simple" -H "Authorization: Bearer YOUR_ANON_KEY"
echo.
echo 📊 مراقبة Logs:
echo supabase functions logs send-notifications-simple --follow
echo.

pause
