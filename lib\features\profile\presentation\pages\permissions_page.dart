import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/services/permissions_notifier.dart';

class PermissionsPage extends StatefulWidget {
  const PermissionsPage({super.key});

  @override
  State<PermissionsPage> createState() => _PermissionsPageState();
}

class _PermissionsPageState extends State<PermissionsPage> {
  late PermissionsNotifier notifier;

  @override
  void initState() {
    super.initState();
    notifier = PermissionsNotifier();
    notifier.initialize();
  }

  Future<void> _updatePermission(String key, bool value) async {
    await notifier.updatePermission(key, value);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'تم ${value ? 'تفعيل' : 'إلغاء'} صلاحية ${_getPermissionName(key)}',
          ),
          backgroundColor: value ? AppColors.success : AppColors.warning,
        ),
      );
    }
  }

  String _getPermissionName(String key) {
    switch (key) {
      case 'appointments':
        return 'الحجوزات';
      case 'patients':
        return 'المرضى';
      case 'products':
        return 'المنتجات';
      case 'articles':
        return 'المقالات';
      default:
        return key;
    }
  }

  IconData _getPermissionIcon(String key) {
    switch (key) {
      case 'appointments':
        return Icons.calendar_today;
      case 'patients':
        return Icons.people;
      case 'products':
        return Icons.inventory;
      case 'articles':
        return Icons.article;
      default:
        return Icons.settings;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('إدارة الصلاحيات'),
        backgroundColor: AppColors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: ListenableBuilder(
        listenable: notifier,
        builder: (context, _) {
          if (notifier.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(20.w),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.primary,
                        AppColors.primary.withValues(alpha: 0.8),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.admin_panel_settings,
                        size: 48.w,
                        color: AppColors.white,
                      ),
                      SizedBox(height: 12.h),
                      Text(
                        'إدارة صلاحيات النظام',
                        style: TextStyle(
                          fontSize: 20.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.white,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        'تحكم في صلاحيات الوصول لأقسام التطبيق المختلفة',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppColors.white.withValues(alpha: 0.9),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 24.h),

                // Permissions List
                Container(
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(12.r),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.gray300.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children:
                        notifier.permissions.entries.map((entry) {
                          final key = entry.key;
                          final value = entry.value;
                          final isLast =
                              entry == notifier.permissions.entries.last;

                          return Column(
                            children: [
                              ListTile(
                                leading: Container(
                                  padding: EdgeInsets.all(8.w),
                                  decoration: BoxDecoration(
                                    color:
                                        value
                                            ? AppColors.primary.withValues(
                                              alpha: 0.1,
                                            )
                                            : AppColors.gray100,
                                    borderRadius: BorderRadius.circular(8.r),
                                  ),
                                  child: Icon(
                                    _getPermissionIcon(key),
                                    color:
                                        value
                                            ? AppColors.primary
                                            : AppColors.textSecondary,
                                    size: 24.w,
                                  ),
                                ),
                                title: Text(
                                  _getPermissionName(key),
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.textPrimary,
                                  ),
                                ),
                                subtitle: Text(
                                  value
                                      ? 'مفعل - يمكن الوصول'
                                      : 'معطل - لا يمكن الوصول',
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color:
                                        value
                                            ? AppColors.success
                                            : AppColors.error,
                                  ),
                                ),
                                trailing: Switch(
                                  value: value,
                                  onChanged:
                                      (newValue) =>
                                          _updatePermission(key, newValue),
                                  activeColor: AppColors.primary,
                                ),
                              ),
                              if (!isLast)
                                Divider(
                                  height: 1,
                                  color: AppColors.border,
                                  indent: 72.w,
                                ),
                            ],
                          );
                        }).toList(),
                  ),
                ),

                SizedBox(height: 24.h),

                // Reset Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      await notifier.resetPermissions();
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('تم إعادة تعيين جميع الصلاحيات'),
                            backgroundColor: AppColors.success,
                          ),
                        );
                      }
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('إعادة تعيين جميع الصلاحيات'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.warning,
                      foregroundColor: AppColors.white,
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
