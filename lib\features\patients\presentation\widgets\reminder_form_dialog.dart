import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/reminder_model.dart';
import '../../../../core/widgets/loading_dialog.dart';
import '../bloc/reminders_bloc.dart';
import '../bloc/reminders_event.dart';

class ReminderFormDialog extends StatefulWidget {
  final String patientId;
  final String reminderType;
  final ReminderModel? reminder;

  const ReminderFormDialog({
    super.key,
    required this.patientId,
    required this.reminderType,
    this.reminder,
  });

  @override
  State<ReminderFormDialog> createState() => _ReminderFormDialogState();
}

class _ReminderFormDialogState extends State<ReminderFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;

  TimeOfDay _selectedTime = TimeOfDay.now();
  List<int> _selectedDays = [];
  bool _isActive = true;

  final List<Map<String, dynamic>> _daysOfWeek = [
    {'value': 1, 'label': 'الاثنين', 'short': 'ن'},
    {'value': 2, 'label': 'الثلاثاء', 'short': 'ث'},
    {'value': 3, 'label': 'الأربعاء', 'short': 'ر'},
    {'value': 4, 'label': 'الخميس', 'short': 'خ'},
    {'value': 5, 'label': 'الجمعة', 'short': 'ج'},
    {'value': 6, 'label': 'السبت', 'short': 'س'},
    {'value': 7, 'label': 'الأحد', 'short': 'ح'},
  ];

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(
      text: widget.reminder?.title ?? '',
    );
    _descriptionController = TextEditingController(
      text: widget.reminder?.description ?? '',
    );

    if (widget.reminder != null) {
      _selectedTime = _parseTime(widget.reminder!.reminderTime);
      _selectedDays = List<int>.from(widget.reminder!.daysOfWeek);
      _isActive = widget.reminder!.isActive;
    } else {
      // Set default title based on reminder type
      _titleController.text = _getDefaultTitle(widget.reminderType);
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  TimeOfDay _parseTime(String timeString) {
    try {
      final parts = timeString.split(':');
      return TimeOfDay(
        hour: int.parse(parts[0]),
        minute: int.parse(parts[1]),
      );
    } catch (e) {
      return TimeOfDay.now();
    }
  }

  String _formatTime(TimeOfDay time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  String _getDefaultTitle(String reminderType) {
    switch (reminderType) {
      case 'meal':
        return 'تذكير وجبة';
      case 'exercise':
        return 'تذكير نشاط بدني';
      case 'medication':
        return 'تذكير دواء';
      case 'water':
        return 'تذكير شرب الماء';
      default:
        return 'تذكير';
    }
  }

  String _getReminderTypeTitle() {
    switch (widget.reminderType) {
      case 'meal':
        return 'تذكير وجبة';
      case 'exercise':
        return 'تذكير نشاط بدني';
      case 'medication':
        return 'تذكير دواء';
      case 'water':
        return 'تذكير شرب الماء';
      default:
        return 'تذكير';
    }
  }

  void _saveReminder() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedDays.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار يوم واحد على الأقل'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    // Show loading dialog immediately
    final isUpdate = widget.reminder != null;
    LoadingDialog.show(
      context,
      isUpdate ? 'جاري تحديث التذكير...' : 'جاري إضافة التذكير...'
    );

    final reminder = ReminderModel(
      id: widget.reminder?.id ?? '',
      patientId: widget.patientId,
      reminderType: widget.reminderType,
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim().isNotEmpty
          ? _descriptionController.text.trim()
          : null,
      reminderTime: _formatTime(_selectedTime),
      daysOfWeek: _selectedDays,
      isActive: _isActive,
      createdAt: widget.reminder?.createdAt ?? DateTime.now(),
      updatedAt: DateTime.now(),
    );

    if (widget.reminder == null) {
      // Add new reminder
      context.read<RemindersBloc>().add(AddReminder(reminder: reminder));
    } else {
      // Update existing reminder
      context.read<RemindersBloc>().add(UpdateReminder(reminder: reminder));
    }

    Navigator.of(context).pop();
  }

  Future<void> _selectTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: AppColors.white,
              surface: AppColors.white,
              onSurface: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedTime) {
      setState(() {
        _selectedTime = picked;
      });
    }
  }

  String _formatTimeDisplay(TimeOfDay time) {
    final hour = time.hour;
    final minute = time.minute;
    final period = hour >= 12 ? 'م' : 'ص';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);

    return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.85,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Fixed header
              Container(
                padding: EdgeInsets.all(24.w),
                child: Text(
                  widget.reminder == null
                      ? 'إضافة ${_getReminderTypeTitle()}'
                      : 'تعديل ${_getReminderTypeTitle()}',
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),

              // Scrollable content
              Flexible(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title
                      SizedBox(
                        height: 80.h,
                        child: TextFormField(
                          controller: _titleController,
                          decoration: InputDecoration(
                            labelText: 'عنوان التذكير *',
                            hintText: 'أدخل عنوان التذكير',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            alignLabelWithHint: true,
                          ),
                          maxLines: null,
                          expands: true,
                          textAlignVertical: TextAlignVertical.top,
                          validator: (value) {
                            if (value?.isEmpty ?? true) {
                              return 'يرجى إدخال عنوان التذكير';
                            }
                            return null;
                          },
                        ),
                      ),
                      SizedBox(height: 16.h),

                      // Description
                      SizedBox(
                        height: 120.h,
                        child: TextFormField(
                          controller: _descriptionController,
                          decoration: InputDecoration(
                            labelText: 'وصف التذكير',
                            hintText: 'أدخل وصف التذكير (اختياري)',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            alignLabelWithHint: true,
                          ),
                          maxLines: null,
                          expands: true,
                          textAlignVertical: TextAlignVertical.top,
                        ),
                      ),
                      SizedBox(height: 16.h),

                      // Time selector
                      InkWell(
                        onTap: _selectTime,
                        child: Container(
                          padding: EdgeInsets.all(16.w),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.gray300),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.access_time, color: AppColors.primary),
                              SizedBox(width: 12.w),
                              Expanded(
                                child: Text(
                                  'وقت التذكير: ${_formatTimeDisplay(_selectedTime)}',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: AppColors.textPrimary,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              SizedBox(width: 8.w),
                              Icon(Icons.arrow_drop_down, color: AppColors.textSecondary),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 16.h),

                      // Days of week selector
                      Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.gray300),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'أيام الأسبوع *',
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            SizedBox(height: 12.h),
                            Wrap(
                              spacing: 8.w,
                              runSpacing: 8.h,
                              children: _daysOfWeek.map((day) {
                                final isSelected = _selectedDays.contains(day['value']);
                                return GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      if (isSelected) {
                                        _selectedDays.remove(day['value']);
                                      } else {
                                        _selectedDays.add(day['value']);
                                      }
                                    });
                                  },
                                  child: Container(
                                    width: 40.w,
                                    height: 40.h,
                                    decoration: BoxDecoration(
                                      color: isSelected ? AppColors.primary : AppColors.gray100,
                                      borderRadius: BorderRadius.circular(20.r),
                                      border: Border.all(
                                        color: isSelected ? AppColors.primary : AppColors.gray300,
                                      ),
                                    ),
                                    child: Center(
                                      child: Text(
                                        day['short'],
                                        style: TextStyle(
                                          fontSize: 14.sp,
                                          fontWeight: FontWeight.bold,
                                          color: isSelected ? AppColors.white : AppColors.textSecondary,
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                            SizedBox(height: 8.h),
                            Row(
                              children: [
                                TextButton(
                                  onPressed: () {
                                    setState(() {
                                      _selectedDays = [1, 2, 3, 4, 5, 6, 7];
                                    });
                                  },
                                  child: const Text('تحديد الكل'),
                                ),
                                TextButton(
                                  onPressed: () {
                                    setState(() {
                                      _selectedDays.clear();
                                    });
                                  },
                                  child: const Text('إلغاء التحديد'),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 16.h),

                      // Active status
                      Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.gray300),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              _isActive ? Icons.notifications_active : Icons.notifications_off,
                              color: _isActive ? AppColors.success : AppColors.error,
                            ),
                            SizedBox(width: 12.w),
                            Expanded(
                              child: Text(
                                'حالة التذكير',
                                style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ),
                            Switch(
                              value: _isActive,
                              onChanged: (value) {
                                setState(() {
                                  _isActive = value;
                                });
                              },
                              activeColor: AppColors.success,
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 24.h),
                    ],
                  ),
                ),
              ),

              // Fixed footer with action buttons
              Container(
                padding: EdgeInsets.all(24.w),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        child: Text(
                          'إلغاء',
                          style: TextStyle(fontSize: 16.sp),
                        ),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _saveReminder,
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        child: Text(
                          widget.reminder == null ? 'إضافة' : 'تحديث',
                          style: TextStyle(fontSize: 16.sp),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
