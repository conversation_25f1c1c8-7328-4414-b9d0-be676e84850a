import 'package:equatable/equatable.dart';

class PatientModel extends Equatable {
  final String id;
  final String? authId; // UUID من Supabase Authentication
  final String name;
  final String? email;
  final String? phone;
  final int? age;
  final DateTime? birthDate;
  final String? gender;
  final double? height;
  final double? weight;
  final bool isPremium;
  final String? medicalConditions;
  final String? allergies;
  final String? medications;
  final String? supplements;
  final String? physicalActivity;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const PatientModel({
    required this.id,
    this.authId,
    required this.name,
    this.email,
    this.phone,
    this.age,
    this.birthDate,
    this.gender,
    this.height,
    this.weight,
    this.isPremium = false,
    this.medicalConditions,
    this.allergies,
    this.medications,
    this.supplements,
    this.physicalActivity,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PatientModel.fromJson(Map<String, dynamic> json) {
    return PatientModel(
      id: json['id'] as String,
      authId: json['auth_id'] as String?,
      name: json['name'] as String,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      age: json['age'] as int?,
      birthDate: json['birth_date'] != null ? DateTime.parse(json['birth_date'] as String) : null,
      gender: json['gender'] as String?,
      height: json['height'] != null ? (json['height'] as num).toDouble() : null,
      weight: json['weight'] != null ? (json['weight'] as num).toDouble() : null,
      isPremium: json['is_premium'] as bool? ?? false,
      medicalConditions: json['medical_conditions'] as String?,
      allergies: json['allergies'] as String?,
      medications: json['medications'] as String?,
      supplements: json['supplements'] as String?,
      physicalActivity: json['physical_activity'] as String?,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'auth_id': authId,
      'name': name,
      'email': email,
      'phone': phone,
      'age': age,
      'birth_date': birthDate?.toIso8601String().split('T')[0],
      'gender': gender,
      'height': height,
      'weight': weight,
      'is_premium': isPremium,
      'medical_conditions': medicalConditions,
      'allergies': allergies,
      'medications': medications,
      'supplements': supplements,
      'physical_activity': physicalActivity,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  PatientModel copyWith({
    String? id,
    String? authId,
    String? name,
    String? email,
    String? phone,
    int? age,
    DateTime? birthDate,
    String? gender,
    double? height,
    double? weight,
    bool? isPremium,
    String? medicalConditions,
    String? allergies,
    String? medications,
    String? supplements,
    String? physicalActivity,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PatientModel(
      id: id ?? this.id,
      authId: authId ?? this.authId,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      age: age ?? this.age,
      birthDate: birthDate ?? this.birthDate,
      gender: gender ?? this.gender,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      isPremium: isPremium ?? this.isPremium,
      medicalConditions: medicalConditions ?? this.medicalConditions,
      allergies: allergies ?? this.allergies,
      medications: medications ?? this.medications,
      supplements: supplements ?? this.supplements,
      physicalActivity: physicalActivity ?? this.physicalActivity,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        authId,
        name,
        email,
        phone,
        age,
        birthDate,
        gender,
        height,
        weight,
        isPremium,
        medicalConditions,
        allergies,
        medications,
        supplements,
        physicalActivity,
        notes,
        createdAt,
        updatedAt,
      ];
}
