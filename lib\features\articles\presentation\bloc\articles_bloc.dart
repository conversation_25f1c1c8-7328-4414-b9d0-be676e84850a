import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/repositories/articles_repository.dart';
import 'articles_event.dart';
import 'articles_state.dart';

class ArticlesBloc extends Bloc<ArticlesEvent, ArticlesState> {
  final ArticlesRepository _articlesRepository;

  ArticlesBloc({required ArticlesRepository articlesRepository})
    : _articlesRepository = articlesRepository,
      super(ArticlesInitial()) {
    on<LoadAllArticles>(_onLoadAllArticles);
    on<LoadPublishedArticles>(_onLoadPublishedArticles);
    on<LoadDraftArticles>(_onLoadDraftArticles);
    on<LoadArticlesByCategory>(_onLoadArticlesByCategory);
    on<LoadArticlesByCategoryId>(_onLoadArticlesByCategoryId);
    on<SearchArticles>(_onSearchArticles);
    on<CreateArticleWithImage>(_onCreateArticleWithImage);
    on<CreateArticleWithFiles>(_onCreateArticleWithFiles);
    on<UploadArticleImage>(_onUploadArticleImage);
    on<UpdateArticleWithImage>(_onUpdateArticleWithImage);
    on<UpdateArticleWithFiles>(_onUpdateArticleWithFiles);
    on<CreateArticle>(_onCreateArticle);
    on<UpdateArticle>(_onUpdateArticle);
    on<DeleteArticle>(_onDeleteArticle);
    on<PublishArticle>(_onPublishArticle);
    on<UnpublishArticle>(_onUnpublishArticle);
    on<RefreshArticles>(_onRefreshArticles);
    on<LoadFeaturedArticles>(_onLoadFeaturedArticles);
  }

  Future<void> _onLoadAllArticles(
    LoadAllArticles event,
    Emitter<ArticlesState> emit,
  ) async {
    emit(ArticlesLoading());
    try {
      debugPrint('🔄 ArticlesBloc: Loading all articles...');
      final allArticles = await _articlesRepository.getAllArticles();
      debugPrint(
        '✅ ArticlesBloc: Loaded ${allArticles.length} articles successfully',
      );

      debugPrint('🔄 ArticlesBloc: Loading published articles...');
      final publishedArticles =
          await _articlesRepository.getPublishedArticles();
      debugPrint(
        '✅ ArticlesBloc: Loaded ${publishedArticles.length} published articles',
      );

      debugPrint('🔄 ArticlesBloc: Loading draft articles...');
      final draftArticles = await _articlesRepository.getDraftArticles();
      debugPrint(
        '✅ ArticlesBloc: Loaded ${draftArticles.length} draft articles',
      );

      debugPrint('🔄 ArticlesBloc: Loading featured articles...');
      final featuredArticles = await _articlesRepository.getFeaturedArticles();
      debugPrint(
        '✅ ArticlesBloc: Loaded ${featuredArticles.length} featured articles',
      );

      debugPrint('🔄 ArticlesBloc: Loading article categories...');
      final categories = await _articlesRepository.getCategories();
      debugPrint('✅ ArticlesBloc: Loaded ${categories.length} categories');

      emit(
        ArticlesLoaded(
          articles: allArticles,
          filteredArticles: allArticles,
          publishedArticles: publishedArticles,
          draftArticles: draftArticles,
          featuredArticles: featuredArticles,
          categories: ['الكل', ...categories],
        ),
      );
    } catch (e) {
      debugPrint('❌ ArticlesBloc: Error loading articles: $e');
      emit(ArticlesError(message: e.toString()));
    }
  }

  Future<void> _onLoadPublishedArticles(
    LoadPublishedArticles event,
    Emitter<ArticlesState> emit,
  ) async {
    emit(ArticlesLoading());
    try {
      final publishedArticles =
          await _articlesRepository.getPublishedArticles();
      final draftArticles = await _articlesRepository.getDraftArticles();
      final featuredArticles = await _articlesRepository.getFeaturedArticles();
      final categories = await _articlesRepository.getCategories();

      emit(
        ArticlesLoaded(
          articles: publishedArticles,
          filteredArticles: publishedArticles,
          publishedArticles: publishedArticles,
          draftArticles: draftArticles,
          featuredArticles: featuredArticles,
          categories: ['الكل', ...categories],
        ),
      );
    } catch (e) {
      emit(ArticlesError(message: e.toString()));
    }
  }

  Future<void> _onLoadDraftArticles(
    LoadDraftArticles event,
    Emitter<ArticlesState> emit,
  ) async {
    emit(ArticlesLoading());
    try {
      final draftArticles = await _articlesRepository.getDraftArticles();
      final publishedArticles =
          await _articlesRepository.getPublishedArticles();
      final featuredArticles = await _articlesRepository.getFeaturedArticles();
      final categories = await _articlesRepository.getCategories();

      emit(
        ArticlesLoaded(
          articles: draftArticles,
          filteredArticles: draftArticles,
          publishedArticles: publishedArticles,
          draftArticles: draftArticles,
          featuredArticles: featuredArticles,
          categories: ['الكل', ...categories],
        ),
      );
    } catch (e) {
      emit(ArticlesError(message: e.toString()));
    }
  }

  Future<void> _onLoadArticlesByCategory(
    LoadArticlesByCategory event,
    Emitter<ArticlesState> emit,
  ) async {
    if (state is ArticlesLoaded) {
      final currentState = state as ArticlesLoaded;

      if (event.category == 'الكل') {
        emit(
          currentState.copyWith(
            articles: currentState.publishedArticles,
            selectedCategory: event.category,
          ),
        );
      } else {
        try {
          final filteredArticles = await _articlesRepository
              .getArticlesByCategory(event.category);
          emit(
            currentState.copyWith(
              articles: filteredArticles,
              selectedCategory: event.category,
            ),
          );
        } catch (e) {
          emit(ArticlesError(message: e.toString()));
        }
      }
    } else {
      add(LoadAllArticles());
    }
  }

  Future<void> _onLoadArticlesByCategoryId(
    LoadArticlesByCategoryId event,
    Emitter<ArticlesState> emit,
  ) async {
    if (state is ArticlesLoaded) {
      final currentState = state as ArticlesLoaded;

      try {
        final filteredArticles = await _articlesRepository
            .getArticlesByCategoryId(event.categoryId);
        emit(
          currentState.copyWith(
            articles: filteredArticles,
            selectedCategory: event.categoryId,
          ),
        );
      } catch (e) {
        emit(ArticlesError(message: e.toString()));
      }
    } else {
      add(LoadAllArticles());
    }
  }

  Future<void> _onSearchArticles(
    SearchArticles event,
    Emitter<ArticlesState> emit,
  ) async {
    if (state is ArticlesLoaded) {
      final currentState = state as ArticlesLoaded;

      if (event.query.isEmpty) {
        emit(
          currentState.copyWith(
            filteredArticles: currentState.articles,
            searchQuery: event.query,
            isSearching: false,
          ),
        );
      } else {
        try {
          debugPrint(
            '🔍 ArticlesBloc: Searching articles with query: ${event.query}',
          );

          final searchResults = await _articlesRepository.searchArticles(
            event.query,
          );

          emit(
            currentState.copyWith(
              filteredArticles: searchResults,
              searchQuery: event.query,
              isSearching: true,
            ),
          );

          debugPrint('✅ ArticlesBloc: Found ${searchResults.length} articles');
        } catch (e) {
          debugPrint('❌ ArticlesBloc: Error searching articles: $e');
          emit(ArticlesError(message: e.toString()));
        }
      }
    }
  }

  Future<void> _onCreateArticle(
    CreateArticle event,
    Emitter<ArticlesState> emit,
  ) async {
    try {
      final createdArticle = await _articlesRepository.createArticle(
        event.article,
      );
      emit(ArticleCreated(article: createdArticle));

      // Refresh the list
      add(LoadAllArticles());
    } catch (e) {
      emit(ArticlesError(message: e.toString()));
    }
  }

  Future<void> _onUpdateArticle(
    UpdateArticle event,
    Emitter<ArticlesState> emit,
  ) async {
    try {
      final updatedArticle = await _articlesRepository.updateArticle(
        event.article,
      );
      emit(ArticleUpdated(article: updatedArticle));

      // Refresh the list
      add(LoadAllArticles());
    } catch (e) {
      emit(ArticlesError(message: e.toString()));
    }
  }

  Future<void> _onDeleteArticle(
    DeleteArticle event,
    Emitter<ArticlesState> emit,
  ) async {
    try {
      debugPrint('🔄 ArticlesBloc: Deleting article: ${event.articleId}');

      await _articlesRepository.deleteArticle(event.articleId);
      emit(ArticleDeleted(articleId: event.articleId));

      debugPrint('✅ ArticlesBloc: Article deleted successfully');

      // Refresh the list
      add(LoadAllArticles());
    } catch (e) {
      debugPrint('❌ ArticlesBloc: Error deleting article: $e');
      emit(ArticlesError(message: e.toString()));
    }
  }

  Future<void> _onPublishArticle(
    PublishArticle event,
    Emitter<ArticlesState> emit,
  ) async {
    try {
      final publishedArticle = await _articlesRepository.publishArticle(
        event.articleId,
      );
      emit(ArticlePublished(article: publishedArticle));

      // Refresh the list
      add(LoadAllArticles());
    } catch (e) {
      emit(ArticlesError(message: e.toString()));
    }
  }

  Future<void> _onUnpublishArticle(
    UnpublishArticle event,
    Emitter<ArticlesState> emit,
  ) async {
    try {
      final unpublishedArticle = await _articlesRepository.unpublishArticle(
        event.articleId,
      );
      emit(ArticleUnpublished(article: unpublishedArticle));

      // Refresh the list
      add(LoadAllArticles());
    } catch (e) {
      emit(ArticlesError(message: e.toString()));
    }
  }

  Future<void> _onRefreshArticles(
    RefreshArticles event,
    Emitter<ArticlesState> emit,
  ) async {
    add(LoadAllArticles());
  }

  Future<void> _onLoadFeaturedArticles(
    LoadFeaturedArticles event,
    Emitter<ArticlesState> emit,
  ) async {
    emit(ArticlesLoading());
    try {
      final featuredArticles = await _articlesRepository.getFeaturedArticles();
      final publishedArticles =
          await _articlesRepository.getPublishedArticles();
      final draftArticles = await _articlesRepository.getDraftArticles();
      final categories = await _articlesRepository.getCategories();

      emit(
        ArticlesLoaded(
          articles: featuredArticles,
          filteredArticles: featuredArticles,
          publishedArticles: publishedArticles,
          draftArticles: draftArticles,
          featuredArticles: featuredArticles,
          categories: ['الكل', ...categories],
        ),
      );
    } catch (e) {
      emit(ArticlesError(message: e.toString()));
    }
  }

  Future<void> _onCreateArticleWithImage(
    CreateArticleWithImage event,
    Emitter<ArticlesState> emit,
  ) async {
    emit(ArticlesLoading());
    try {
      debugPrint('🔄 ArticlesBloc: Creating article with image...');

      // Use the new method that handles both article creation and image upload
      final createdArticle = await _articlesRepository.createArticleWithImage(
        event.article,
        event.image,
      );

      debugPrint(
        '✅ ArticlesBloc: Article created successfully: ${createdArticle.id}',
      );

      // Reload all articles
      add(LoadAllArticles());
    } catch (e) {
      debugPrint('❌ ArticlesBloc: Error creating article with image: $e');
      emit(ArticlesError(message: e.toString()));
    }
  }

  Future<void> _onUploadArticleImage(
    UploadArticleImage event,
    Emitter<ArticlesState> emit,
  ) async {
    try {
      // Upload the image
      await _articlesRepository.uploadArticleImage(
        event.image,
        event.articleId,
      );

      // Reload all articles
      add(LoadAllArticles());
    } catch (e) {
      emit(ArticlesError(message: e.toString()));
    }
  }

  Future<void> _onUpdateArticleWithImage(
    UpdateArticleWithImage event,
    Emitter<ArticlesState> emit,
  ) async {
    emit(ArticlesLoading());
    try {
      debugPrint('🔄 ArticlesBloc: Updating article with image...');
      debugPrint('🗑️ Delete old image: ${event.deleteOldImage}');
      debugPrint('📷 Old image URL: ${event.oldImageUrl}');

      // Use the new method that handles both article update and image upload/deletion
      final updatedArticle = await _articlesRepository.updateArticleWithImage(
        event.article,
        event.image,
        deleteOldImage: event.deleteOldImage,
        oldImageUrl: event.oldImageUrl,
      );

      debugPrint(
        '✅ ArticlesBloc: Article updated successfully: ${updatedArticle.id}',
      );

      // Reload all articles
      add(LoadAllArticles());
    } catch (e) {
      debugPrint('❌ ArticlesBloc: Error updating article with image: $e');
      emit(ArticlesError(message: e.toString()));
    }
  }

  Future<void> _onCreateArticleWithFiles(
    CreateArticleWithFiles event,
    Emitter<ArticlesState> emit,
  ) async {
    emit(ArticlesLoading());
    try {
      debugPrint('🔄 ArticlesBloc: Creating article with files...');

      // Use the new method that handles both article creation and file uploads
      final createdArticle = await _articlesRepository.createArticleWithFiles(
        event.article,
        event.image,
        event.pdf,
      );

      debugPrint(
        '✅ ArticlesBloc: Article created successfully: ${createdArticle.id}',
      );

      // Reload all articles
      add(LoadAllArticles());
    } catch (e) {
      debugPrint('❌ ArticlesBloc: Error creating article with files: $e');
      emit(ArticlesError(message: e.toString()));
    }
  }

  Future<void> _onUpdateArticleWithFiles(
    UpdateArticleWithFiles event,
    Emitter<ArticlesState> emit,
  ) async {
    emit(ArticlesLoading());
    try {
      debugPrint('🔄 ArticlesBloc: Updating article with files...');
      debugPrint('🗑️ Delete old image: ${event.deleteOldImage}');
      debugPrint('📷 Old image URL: ${event.oldImageUrl}');
      debugPrint('🗑️ Delete old PDF: ${event.deleteOldPdf}');
      debugPrint('📄 Old PDF URL: ${event.oldPdfUrl}');

      // Use the new method that handles both article update and file upload/deletion
      final updatedArticle = await _articlesRepository.updateArticleWithFiles(
        event.article,
        event.image,
        event.pdf,
        deleteOldImage: event.deleteOldImage,
        oldImageUrl: event.oldImageUrl,
        deleteOldPdf: event.deleteOldPdf,
        oldPdfUrl: event.oldPdfUrl,
      );

      debugPrint(
        '✅ ArticlesBloc: Article updated successfully: ${updatedArticle.id}',
      );

      // Reload all articles
      add(LoadAllArticles());
    } catch (e) {
      debugPrint('❌ ArticlesBloc: Error updating article with files: $e');
      emit(ArticlesError(message: e.toString()));
    }
  }
}
