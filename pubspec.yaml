name: deit_rx_user
description: "تطبيق لوحة تحكم عيادة التغذية والحمية الغذائية"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.8
  flutter_screenutil: ^5.9.3

  # State Management
  flutter_bloc: ^8.1.6
  equatable: ^2.0.5

  # Backend & Database
  supabase_flutter: ^2.8.0

  # Network & HTTP
  dio: ^5.7.0
  connectivity_plus: ^6.1.0

  # Local Storage
  shared_preferences: ^2.3.3

  # Image Handling
  image_picker: ^1.1.2
  cached_network_image: ^3.4.1

  # File Handling
  file_picker: ^8.1.2

  # Date & Time
  intl: ^0.20.2

  # Utils
  uuid: ^4.5.1
  logger: ^2.4.0
  url_launcher: ^6.3.1

  # Validation
  formz: ^0.7.0

  # Crypto
  crypto: ^3.0.5

  # Localization
  flutter_localizations:
    sdk: flutter

  # Firebase
  firebase_core: ^3.14.0

  # HTTP & API
  http: ^1.2.2
  googleapis_auth: ^1.6.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  # App icon generator
  flutter_launcher_icons: ^0.13.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/key/

# App icon configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: false
  image_path: "assets/images/logo.jpeg"
  min_sdk_android: 21

  # Android adaptive icon (for Android 8.0+)
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/images/logo.jpeg"

  # Generate different densities
  generate: true

  # Background color for adaptive icon
  background_color: "#ffffff"
