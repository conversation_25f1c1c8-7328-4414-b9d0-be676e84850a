# 🚀 دليل نشر Supabase Edge Functions - Diet Rx

## 💰 المميزات المجانية

### ✅ **مجاني 100% بدون بطاقة دفع:**
- **500,000 Edge Function calls شهرياً** (استخدامك: 120,000)
- **500MB Database storage**
- **1GB File storage** 
- **2GB Bandwidth**
- **<PERSON>ron Jobs مجانية**

## 📋 المتطلبات

### 1. تثبيت Supabase CLI
```bash
npm install -g supabase
```

### 2. تسجيل الدخول
```bash
supabase login
```

## 🔧 خطوات النشر

### 1. ربط المشروع
```bash
# في مجلد المشروع
supabase link --project-ref xwxeauofbzedfzaogzzy
```

### 2. نشر Edge Function
```bash
# نشر الدالة البسيطة
supabase functions deploy send-notifications-simple

# أو نشر الدالة الكاملة (عند اكتمالها)
supabase functions deploy send-notifications
```

### 3. إعداد Environment Variables
```bash
# إعداد متغيرات البيئة
supabase secrets set SUPABASE_URL=https://xwxeauofbzedfzaogzzy.supabase.co
supabase secrets set SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 4. إعداد Cron Job
1. اذهب إلى [Supabase Dashboard](https://supabase.com/dashboard/project/xwxeauofbzedfzaogzzy)
2. SQL Editor
3. انسخ والصق محتوى `supabase_cron_setup.sql`
4. اضغط Run

## 🧪 الاختبار

### 1. اختبار Function يدوياً
```bash
# اختبار محلي
supabase functions serve send-notifications-simple

# اختبار مباشر
curl -X POST \
  'https://xwxeauofbzedfzaogzzy.supabase.co/functions/v1/send-notifications-simple' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -H 'Content-Type: application/json'
```

### 2. مراقبة Logs
```bash
# مراقبة logs مباشرة
supabase functions logs send-notifications-simple --follow
```

### 3. التحقق من Cron Jobs
```sql
-- في SQL Editor
SELECT * FROM cron.job WHERE jobname = 'send-scheduled-notifications';

-- مراقبة التنفيذ
SELECT * FROM cron.job_run_details 
WHERE jobname = 'send-scheduled-notifications' 
ORDER BY start_time DESC 
LIMIT 10;
```

## 📊 المراقبة والإحصائيات

### 1. Dashboard
- [Functions Dashboard](https://supabase.com/dashboard/project/xwxeauofbzedfzaogzzy/functions)
- مراقبة الاستدعاءات والأخطاء
- إحصائيات الاستخدام

### 2. Database Monitoring
```sql
-- عدد الإشعارات المرسلة اليوم
SELECT COUNT(*) as notifications_sent_today
FROM notification_logs 
WHERE DATE(sent_at) = CURRENT_DATE;

-- إحصائيات الإشعارات الأسبوعية
SELECT 
  DATE(sent_at) as date,
  COUNT(*) as count,
  COUNT(CASE WHEN status = 'sent' THEN 1 END) as successful,
  COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed
FROM notification_logs 
WHERE sent_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(sent_at)
ORDER BY date DESC;
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. Function لا تعمل
```bash
# تحقق من الـ logs
supabase functions logs send-notifications-simple

# تحقق من الـ secrets
supabase secrets list
```

#### 2. Cron Job لا يعمل
```sql
-- تحقق من حالة المهمة
SELECT * FROM cron.job WHERE jobname = 'send-scheduled-notifications';

-- تحقق من أخطاء التنفيذ
SELECT * FROM cron.job_run_details 
WHERE jobname = 'send-scheduled-notifications' 
AND status = 'failed'
ORDER BY start_time DESC;
```

#### 3. لا توجد إشعارات
```sql
-- تحقق من الإشعارات المجدولة
SELECT * FROM scheduled_notifications 
WHERE is_active = true 
AND scheduled_time = TO_CHAR(NOW(), 'HH24:MI');

-- تحقق من FCM tokens
SELECT COUNT(*) FROM user_fcm_tokens WHERE is_active = true;
```

## 🔄 التحديثات

### تحديث Function:
```bash
# عدل الكود في supabase_functions/send-notifications-simple/index.ts
# ثم نشر التحديث
supabase functions deploy send-notifications-simple
```

### تحديث Cron Schedule:
```sql
-- تغيير التوقيت (مثال: كل 5 دقائق)
SELECT cron.alter_job('send-scheduled-notifications', schedule := '*/5 * * * *');
```

## 🛡️ الأمان

### 1. حماية المفاتيح
- استخدم Service Role Key في Secrets فقط
- لا تشارك المفاتيح في الكود

### 2. مراقبة الاستخدام
```sql
-- مراقبة استخدام Functions
SELECT 
  DATE(created_at) as date,
  COUNT(*) as function_calls
FROM edge_logs 
WHERE function_name = 'send-notifications-simple'
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

## ✅ التحقق من النجاح

### 1. Functions تعمل:
- تحقق من Dashboard
- مراقبة Logs

### 2. Cron Jobs تعمل:
```sql
SELECT * FROM cron.job_run_details 
WHERE jobname = 'send-scheduled-notifications' 
ORDER BY start_time DESC 
LIMIT 5;
```

### 3. Notifications تُرسل:
```sql
SELECT COUNT(*) FROM notification_logs 
WHERE DATE(sent_at) = CURRENT_DATE;
```

---

## 🎉 تهانينا!

الآن نظام الإشعارات يعمل مجاناً 100% بدون حاجة لبطاقة دفع! 🚀

**التكلفة: 0 ريال إلى الأبد!** 💰
