import { database, Change } from 'firebase-functions/v1';
import { app } from 'firebase-admin';
/** Create a DataSnapshot. */
export declare function makeDataSnapshot(
/** Value of data for the snapshot. */
val: string | number | boolean | any[] | object | null, 
/** Full path of the reference (e.g. 'users/alovelace'). */
refPath: string, 
/** The Firebase app that the database belongs to.
 * The databaseURL supplied when initializing the app will be used for creating this snapshot.
 * You do not need to supply this parameter if you supplied Firebase config values when initializing
 * firebase-functions-test.
 */
firebaseApp?: app.App, 
/**
 * The RTDB instance to use when creating snapshot. This will override the `firebaseApp` parameter.
 * If omitted the default RTDB instance is used.
 */
instance?: string): database.DataSnapshot;
/** Fetch an example data snapshot already populated with data. Can be passed into a wrapped
 * database onCreate or onDelete function.
 */
export declare function exampleDataSnapshot(refPath?: string): database.DataSnapshot;
/** Fetch an example Change object of data snapshots already populated with data.
 * Can be passed into a wrapped database onUpdate or onWrite function.
 */
export declare function exampleDataSnapshotChange(beforeRefPath?: string, afterRefPath?: string): Change<database.DataSnapshot>;
