{"name": "limiter", "description": "A generic rate limiter for node.js. Useful for API clients, web crawling, or other tasks that need to be throttled", "version": "1.1.5", "author": "<PERSON> <<EMAIL>>", "scripts": {"test": "vows --spec"}, "dependencies": {}, "devDependencies": {"assert": "1.3.0", "vows": "0.8.1"}, "keywords": ["rate", "limiting", "throttling"], "repository": "git://github.com/jhurliman/node-rate-limiter", "bugs": {"url": "http://github.com/jhurliman/node-rate-limiter/issues"}, "directories": {"lib": "./lib/"}, "main": "./index.js", "types": "./index.d.ts", "licenses": [{"type": "MIT", "url": "http://github.com/jhurliman/node-rate-limiter/raw/master/LICENSE.txt"}]}