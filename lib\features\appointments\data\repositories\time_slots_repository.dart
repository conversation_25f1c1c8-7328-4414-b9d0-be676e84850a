import 'package:flutter/foundation.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/time_slot_model.dart';

class TimeSlotsRepository {
  // Get all time slots
  Future<List<TimeSlotModel>> getAllTimeSlots() async {
    try {
      debugPrint('🔍 TimeSlotsRepository: Starting to load time slots...');
      debugPrint('🔗 TimeSlotsRepository: Checking Supabase connection...');

      // Test connection first
      debugPrint('🧪 TimeSlotsRepository: Running test query...');
      final testResponse = await SupabaseConfig.timeSlots.select('id').limit(1);
      debugPrint('🧪 TimeSlotsRepository: Test query successful, got ${testResponse.length} records');

      debugPrint('📡 TimeSlotsRepository: Executing main query...');
      final response = await SupabaseConfig.timeSlots
          .select()
          .order('day_of_week')
          .order('start_time');

      debugPrint('📊 TimeSlotsRepository: Raw response type: ${response.runtimeType}');
      debugPrint('📊 TimeSlotsRepository: Response length: ${response.length}');
      debugPrint('📊 TimeSlotsRepository: First item: ${response.isNotEmpty ? response.first : 'No items'}');

      if (response.isEmpty) {
        debugPrint('⚠️ TimeSlotsRepository: No time slots found in database');
        return [];
      }

      debugPrint('🔄 TimeSlotsRepository: Starting to parse ${response.length} items...');
      final timeSlots = <TimeSlotModel>[];

      for (int i = 0; i < response.length; i++) {
        try {
          final json = response[i];
          debugPrint('🔄 TimeSlotsRepository: Parsing item $i: $json');
          final timeSlot = TimeSlotModel.fromJson(json);
          timeSlots.add(timeSlot);
          debugPrint('✅ TimeSlotsRepository: Successfully parsed item $i');
        } catch (e) {
          debugPrint('❌ TimeSlotsRepository: Error parsing item $i: $e');
          // Continue with other items
        }
      }

      debugPrint('✅ TimeSlotsRepository: Successfully parsed ${timeSlots.length} time slots');
      return timeSlots;
    } catch (e, stackTrace) {
      debugPrint('❌ TimeSlotsRepository: Error loading time slots: $e');
      debugPrint('📍 TimeSlotsRepository: Stack trace: $stackTrace');
      throw Exception('فشل في جلب المواعيد: ${e.toString()}');
    }
  }

  // Get active time slots
  Future<List<TimeSlotModel>> getActiveTimeSlots() async {
    try {
      final response = await SupabaseConfig.timeSlots
          .select()
          .eq('is_active', true)
          .order('day_of_week')
          .order('start_time');

      return response.map((json) => TimeSlotModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في جلب المواعيد النشطة: ${e.toString()}');
    }
  }

  // Get time slots by day
  Future<List<TimeSlotModel>> getTimeSlotsByDay(int dayOfWeek) async {
    try {
      final response = await SupabaseConfig.timeSlots
          .select()
          .eq('day_of_week', dayOfWeek)
          .eq('is_active', true)
          .order('start_time');

      return response.map((json) => TimeSlotModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في جلب مواعيد اليوم: ${e.toString()}');
    }
  }

  // Create time slot
  Future<TimeSlotModel> createTimeSlot(TimeSlotModel timeSlot) async {
    try {
      // Check for overlapping time slots on the same day
      await _validateTimeSlot(timeSlot);

      final response = await SupabaseConfig.timeSlots
          .insert({
            'day_of_week': timeSlot.dayOfWeek,
            'start_time': timeSlot.startTime,
            'end_time': timeSlot.endTime,
            'duration_minutes': timeSlot.durationMinutes,
            'is_active': timeSlot.isActive,
            'max_patients': timeSlot.maxPatients,
          })
          .select()
          .single();

      return TimeSlotModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في إنشاء الموعد: ${e.toString()}');
    }
  }

  // Update time slot
  Future<TimeSlotModel> updateTimeSlot(TimeSlotModel timeSlot) async {
    try {
      // Check for overlapping time slots on the same day (excluding current slot)
      await _validateTimeSlot(timeSlot, excludeId: timeSlot.id);

      final response = await SupabaseConfig.timeSlots
          .update({
            'day_of_week': timeSlot.dayOfWeek,
            'start_time': timeSlot.startTime,
            'end_time': timeSlot.endTime,
            'duration_minutes': timeSlot.durationMinutes,
            'is_active': timeSlot.isActive,
            'max_patients': timeSlot.maxPatients,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', timeSlot.id)
          .select()
          .single();

      return TimeSlotModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في تحديث الموعد: ${e.toString()}');
    }
  }

  // Delete time slot
  Future<void> deleteTimeSlot(String timeSlotId) async {
    try {
      // Check if there are any booked appointments for this time slot
      final bookedAppointments = await SupabaseConfig.appointments
          .select('id')
          .eq('time_slot_id', timeSlotId)
          .eq('status', 'booked');

      if (bookedAppointments.isNotEmpty) {
        throw Exception('لا يمكن حذف هذا الموعد لوجود حجوزات مؤكدة');
      }

      // Delete the time slot
      await SupabaseConfig.timeSlots.delete().eq('id', timeSlotId);

      // Delete any available appointments for this time slot
      await SupabaseConfig.appointments
          .delete()
          .eq('time_slot_id', timeSlotId)
          .eq('status', 'available');
    } catch (e) {
      throw Exception('فشل في حذف الموعد: ${e.toString()}');
    }
  }

  // Toggle time slot active status
  Future<TimeSlotModel> toggleTimeSlotStatus(String timeSlotId) async {
    try {
      final timeSlot = await getTimeSlotById(timeSlotId);

      final response = await SupabaseConfig.timeSlots
          .update({
            'is_active': !timeSlot.isActive,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', timeSlotId)
          .select()
          .single();

      return TimeSlotModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في تغيير حالة الموعد: ${e.toString()}');
    }
  }

  // Get time slot by ID
  Future<TimeSlotModel> getTimeSlotById(String timeSlotId) async {
    try {
      final response = await SupabaseConfig.timeSlots
          .select()
          .eq('id', timeSlotId)
          .single();

      return TimeSlotModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في جلب بيانات الموعد: ${e.toString()}');
    }
  }

  // Validate time slot for overlaps
  Future<void> _validateTimeSlot(TimeSlotModel timeSlot, {String? excludeId}) async {
    try {
      final existingSlots = await SupabaseConfig.timeSlots
          .select()
          .eq('day_of_week', timeSlot.dayOfWeek)
          .eq('is_active', true);

      for (final slot in existingSlots) {
        if (excludeId != null && slot['id'] == excludeId) continue;

        try {
          // Parse time strings more safely
          final existingStartTime = slot['start_time'] as String;
          final existingEndTime = slot['end_time'] as String;

          final existingStart = DateTime.parse('2024-01-01 $existingStartTime');
          final existingEnd = DateTime.parse('2024-01-01 $existingEndTime');
          final newStart = DateTime.parse('2024-01-01 ${timeSlot.startTime}');
          final newEnd = DateTime.parse('2024-01-01 ${timeSlot.endTime}');

          // Check for overlap
          if ((newStart.isBefore(existingEnd) && newEnd.isAfter(existingStart))) {
            throw Exception('يتداخل هذا الموعد مع موعد آخر في نفس اليوم');
          }
        } catch (e) {
          // Skip this slot if there's a parsing error
          continue;
        }
      }
    } catch (e) {
      // If validation fails, allow the operation to continue
      // This prevents blocking due to validation errors
      return;
    }
  }

  // Generate appointments for time slots (for next weeks)
  Future<void> generateAppointmentsForTimeSlots({int weeksAhead = 4}) async {
    try {
      final activeTimeSlots = await getActiveTimeSlots();
      final now = DateTime.now();

      for (int week = 0; week < weeksAhead; week++) {
        for (final timeSlot in activeTimeSlots) {
          // Calculate the date for this time slot in the current week
          final targetDate = _getNextDateForDayOfWeek(
            now.add(Duration(days: week * 7)),
            timeSlot.dayOfWeek
          );

          // Check if appointment already exists
          final existingAppointments = await SupabaseConfig.appointments
              .select('id')
              .eq('appointment_date', targetDate.toIso8601String().split('T')[0])
              .eq('time_slot_id', timeSlot.id);

          if (existingAppointments.isEmpty) {
            // Create available appointment
            await SupabaseConfig.appointments.insert({
              'appointment_date': targetDate.toIso8601String().split('T')[0],
              'appointment_time': timeSlot.startTime,
              'time_slot_id': timeSlot.id,
              'status': 'available',
            });
          }
        }
      }
    } catch (e) {
      throw Exception('فشل في إنشاء المواعيد: ${e.toString()}');
    }
  }

  // Helper method to get next date for a specific day of week
  DateTime _getNextDateForDayOfWeek(DateTime startDate, int targetDayOfWeek) {
    final currentDayOfWeek = startDate.weekday % 7; // Convert to 0-6 format
    final daysToAdd = (targetDayOfWeek - currentDayOfWeek) % 7;
    return startDate.add(Duration(days: daysToAdd));
  }
}
