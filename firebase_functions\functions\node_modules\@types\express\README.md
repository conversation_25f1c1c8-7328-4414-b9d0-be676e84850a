# Installation
> `npm install --save @types/express`

# Summary
This package contains type definitions for Express (http://expressjs.com).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express.

### Additional Details
 * Last updated: Tue, 03 Mar 2020 18:55:02 GMT
 * Dependencies: [@types/body-parser](https://npmjs.com/package/@types/body-parser), [@types/serve-static](https://npmjs.com/package/@types/serve-static), [@types/express-serve-static-core](https://npmjs.com/package/@types/express-serve-static-core)
 * Global values: none

# Credits
These definitions were written by [<PERSON>](https://github.com/b<PERSON><PERSON><PERSON>), [China Medical University Hospital](https://github.com/CMUH), and [Puneet Arora](https://github.com/puneetar).
