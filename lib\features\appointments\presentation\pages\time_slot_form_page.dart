import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../../core/widgets/loading_dialog.dart';
import '../bloc/time_slots_bloc.dart';
import '../bloc/time_slots_event.dart';
import '../bloc/time_slots_state.dart';

class TimeSlotFormPage extends StatefulWidget {
  final TimeSlotModel? timeSlot;
  final bool isEditing;
  final int? preSelectedDay;

  const TimeSlotFormPage({
    super.key,
    this.timeSlot,
    this.isEditing = false,
    this.preSelectedDay,
  });

  @override
  State<TimeSlotFormPage> createState() => _TimeSlotFormPageState();
}

class _TimeSlotFormPageState extends State<TimeSlotFormPage> {
  final _formKey = GlobalKey<FormState>();

  int _selectedDayOfWeek = 0;
  TimeOfDay _startTime = const TimeOfDay(hour: 9, minute: 0);
  TimeOfDay _endTime = const TimeOfDay(hour: 17, minute: 0);
  int _durationMinutes = 30;
  bool _isActive = true;
  int _maxPatients = 1;

  final List<String> _dayNames = [
    'الأحد',    // 0
    'الإثنين',  // 1
    'الثلاثاء', // 2
    'الأربعاء', // 3
    'الخميس',   // 4
    'الجمعة',   // 5
    'السبت',    // 6
  ];

  @override
  void initState() {
    super.initState();

    if (widget.isEditing && widget.timeSlot != null) {
      _selectedDayOfWeek = widget.timeSlot!.dayOfWeek;
      _startTime = _parseTimeString(widget.timeSlot!.startTime);
      _endTime = _parseTimeString(widget.timeSlot!.endTime);
      _durationMinutes = widget.timeSlot!.durationMinutes;
      _isActive = widget.timeSlot!.isActive;
      _maxPatients = widget.timeSlot!.maxPatients;
    } else if (widget.preSelectedDay != null) {
      _selectedDayOfWeek = widget.preSelectedDay!;
    }
  }

  TimeOfDay _parseTimeString(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }

  String _formatTimeOfDay(TimeOfDay time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  String _formatTimeOfDayWithAmPm(TimeOfDay time) {
    if (time.hour == 0) {
      return '12:${time.minute.toString().padLeft(2, '0')} ص';
    } else if (time.hour < 12) {
      return '${time.hour}:${time.minute.toString().padLeft(2, '0')} ص';
    } else if (time.hour == 12) {
      return '12:${time.minute.toString().padLeft(2, '0')} م';
    } else {
      return '${time.hour - 12}:${time.minute.toString().padLeft(2, '0')} م';
    }
  }

  Future<void> _selectTime(BuildContext context, bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStartTime ? _startTime : _endTime,
      builder: (context, child) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        if (isStartTime) {
          _startTime = picked;
          // Ensure end time is after start time
          if (_endTime.hour < _startTime.hour ||
              (_endTime.hour == _startTime.hour && _endTime.minute <= _startTime.minute)) {
            _endTime = TimeOfDay(
              hour: _startTime.hour + 1,
              minute: _startTime.minute,
            );
          }
        } else {
          _endTime = picked;
        }
      });
    }
  }

  void _saveTimeSlot() {
    if (_formKey.currentState!.validate()) {
      // Validate time range
      final startMinutes = _startTime.hour * 60 + _startTime.minute;
      final endMinutes = _endTime.hour * 60 + _endTime.minute;

      if (endMinutes <= startMinutes) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('وقت النهاية يجب أن يكون بعد وقت البداية'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }

      // Validate duration
      if (_durationMinutes <= 0 || _durationMinutes > 480) { // Max 8 hours
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('مدة الموعد يجب أن تكون بين 1 دقيقة و 8 ساعات'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }

      try {
        final timeSlot = TimeSlotModel(
          id: widget.timeSlot?.id ?? '',
          dayOfWeek: _selectedDayOfWeek,
          startTime: _formatTimeOfDay(_startTime),
          endTime: _formatTimeOfDay(_endTime),
          durationMinutes: _durationMinutes,
          isActive: _isActive,
          maxPatients: _maxPatients,
          createdAt: widget.timeSlot?.createdAt ?? DateTime.now(),
          updatedAt: DateTime.now(),
        );

        if (widget.isEditing) {
          context.read<TimeSlotsBloc>().add(UpdateTimeSlot(timeSlot: timeSlot));
        } else {
          context.read<TimeSlotsBloc>().add(CreateTimeSlot(timeSlot: timeSlot));
        }
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ البيانات: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<TimeSlotsBloc, TimeSlotsState>(
      listener: (context, state) {
        if (state is TimeSlotsLoading) {
          LoadingDialog.show(
            context,
            widget.isEditing ? 'جاري تحديث الموعد...' : 'جاري حفظ الموعد...'
          );
        } else if (state is TimeSlotCreated || state is TimeSlotUpdated) {
          LoadingDialog.hide(context);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(widget.isEditing ? 'تم تحديث الموعد بنجاح' : 'تم حفظ الموعد بنجاح'),
              backgroundColor: AppColors.success,
            ),
          );
          Navigator.of(context).pop();
        } else if (state is TimeSlotsError) {
          LoadingDialog.hide(context);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ: ${state.message}'),
              backgroundColor: AppColors.error,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          title: Text(widget.isEditing ? 'تعديل الموعد الثابت' : 'إضافة موعد ثابت'),
          backgroundColor: AppColors.white,
          elevation: 0,
          actions: [
            TextButton(
              onPressed: _saveTimeSlot,
              child: Text(
                widget.isEditing ? 'تحديث' : 'حفظ',
                style: TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                  fontSize: 16.sp,
                ),
              ),
            ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(20.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Day Selection
                _buildSectionTitle('اليوم *'),
                SizedBox(height: 8.h),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.gray300),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<int>(
                      value: _selectedDayOfWeek,
                      isExpanded: true,
                      items: List.generate(7, (index) {
                        return DropdownMenuItem<int>(
                          value: index,
                          child: Text(_dayNames[index]),
                        );
                      }),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedDayOfWeek = value;
                          });
                        }
                      },
                    ),
                  ),
                ),

                SizedBox(height: 20.h),

                // Time Range
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildSectionTitle('وقت البداية *'),
                          SizedBox(height: 8.h),
                          GestureDetector(
                            onTap: () => _selectTime(context, true),
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(16.w),
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.gray300),
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.access_time, color: AppColors.primary),
                                  SizedBox(width: 12.w),
                                  Text(
                                    _formatTimeOfDayWithAmPm(_startTime),
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildSectionTitle('وقت النهاية *'),
                          SizedBox(height: 8.h),
                          GestureDetector(
                            onTap: () => _selectTime(context, false),
                            child: Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(16.w),
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.gray300),
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.access_time, color: AppColors.primary),
                                  SizedBox(width: 12.w),
                                  Text(
                                    _formatTimeOfDayWithAmPm(_endTime),
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 20.h),

                // Duration
                _buildSectionTitle('مدة الموعد (بالدقائق) *'),
                SizedBox(height: 8.h),
                TextFormField(
                  initialValue: _durationMinutes.toString(),
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: '30',
                    prefixIcon: const Icon(Icons.timer),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'يرجى إدخال مدة الموعد';
                    }
                    final duration = int.tryParse(value!);
                    if (duration == null || duration <= 0) {
                      return 'يرجى إدخال رقم صحيح أكبر من صفر';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    final duration = int.tryParse(value);
                    if (duration != null) {
                      _durationMinutes = duration;
                    }
                  },
                ),

                SizedBox(height: 20.h),

                // Max Patients
                _buildSectionTitle('عدد المرضى المسموح *'),
                SizedBox(height: 8.h),
                TextFormField(
                  initialValue: _maxPatients.toString(),
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: '1',
                    prefixIcon: const Icon(Icons.people),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'يرجى إدخال عدد المرضى';
                    }
                    final count = int.tryParse(value!);
                    if (count == null || count <= 0) {
                      return 'يرجى إدخال رقم صحيح أكبر من صفر';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    final count = int.tryParse(value);
                    if (count != null) {
                      _maxPatients = count;
                    }
                  },
                ),

                SizedBox(height: 20.h),

                // Active Status
                Row(
                  children: [
                    Switch(
                      value: _isActive,
                      onChanged: (value) {
                        setState(() {
                          _isActive = value;
                        });
                      },
                    ),
                    SizedBox(width: 12.w),
                    Text(
                      'موعد نشط',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 40.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: title.contains('*') ? title.replaceAll('*', '') : title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          if (title.contains('*'))
            TextSpan(
              text: ' *',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.error,
              ),
            ),
        ],
      ),
    );
  }
}
