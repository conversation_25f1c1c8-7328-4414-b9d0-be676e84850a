import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

class AppointmentModel extends Equatable {
  final String id;
  final String? patientId;
  final DateTime appointmentDate;
  final String? timeSlotId;
  final String status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const AppointmentModel({
    required this.id,
    this.patientId,
    required this.appointmentDate,
    this.timeSlotId,
    this.status = 'available',
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  // Check if appointment is booked
  bool get isBooked =>
      status == 'booked' || status == 'confirmed' || status == 'scheduled';

  // Check if appointment is available
  bool get isAvailable => status == 'available';

  // Check if appointment is completed
  bool get isCompleted => status == 'completed';

  // Check if appointment is cancelled
  bool get isCancelled => status == 'cancelled';

  factory AppointmentModel.fromJson(Map<String, dynamic> json) {
    try {
      debugPrint('🔄 AppointmentModel.fromJson: Parsing JSON: $json');

      final id = json['id'] as String;
      final patientId = json['patient_id'] as String?;
      final appointmentDate = DateTime.parse(
        json['appointment_date'] as String,
      );
      final timeSlotId = json['time_slot_id'] as String?;
      final status = json['status'] as String? ?? 'available';
      final notes = json['notes'] as String?;
      final createdAt = DateTime.parse(json['created_at'] as String);
      final updatedAt = DateTime.parse(json['updated_at'] as String);

      debugPrint(
        '✅ AppointmentModel.fromJson: Successfully parsed appointment $id',
      );

      return AppointmentModel(
        id: id,
        patientId: patientId,
        appointmentDate: appointmentDate,
        timeSlotId: timeSlotId,
        status: status,
        notes: notes,
        createdAt: createdAt,
        updatedAt: updatedAt,
      );
    } catch (e, stackTrace) {
      debugPrint('❌ AppointmentModel.fromJson: Error parsing JSON: $e');
      debugPrint('📍 AppointmentModel.fromJson: JSON was: $json');
      debugPrint('📍 AppointmentModel.fromJson: Stack trace: $stackTrace');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'patient_id': patientId,
      'appointment_date': appointmentDate.toIso8601String().split('T')[0],
      'time_slot_id': timeSlotId,
      'status': status,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };

    // فقط أضف id إذا لم يكن فارغاً (للتحديث)
    if (id.isNotEmpty) {
      json['id'] = id;
    }

    debugPrint('🔄 AppointmentModel.toJson: Generated JSON: $json');
    return json;
  }

  AppointmentModel copyWith({
    String? id,
    String? patientId,
    DateTime? appointmentDate,
    String? timeSlotId,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AppointmentModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      appointmentDate: appointmentDate ?? this.appointmentDate,
      timeSlotId: timeSlotId ?? this.timeSlotId,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
    id,
    patientId,
    appointmentDate,
    timeSlotId,
    status,
    notes,
    createdAt,
    updatedAt,
  ];
}
