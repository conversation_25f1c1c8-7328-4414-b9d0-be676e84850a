import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';
import 'firebase_messaging_service.dart';

class NotificationSchedulerService {
  static final _supabase = Supabase.instance.client;

  // Helper method to validate UUID format
  static bool _isValidUUID(String uuid) {
    final uuidRegex = RegExp(
      r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$',
    );
    return uuidRegex.hasMatch(uuid);
  }

  // Save FCM token for a user
  static Future<bool> saveFCMToken({
    required String userId,
    required String fcmToken,
    Map<String, dynamic>? deviceInfo,
  }) async {
    try {
      // First, deactivate old tokens for this user
      await _supabase
          .from('user_fcm_tokens')
          .update({'is_active': false})
          .eq('user_id', userId);

      // Insert new token
      await _supabase.from('user_fcm_tokens').upsert({
        'user_id': userId,
        'fcm_token': fcmToken,
        'device_info': deviceInfo,
        'is_active': true,
        'updated_at': DateTime.now().toIso8601String(),
      });

      print('✅ FCM token saved for user: $userId');
      return true;
    } catch (e) {
      print('❌ Error saving FCM token: $e');
      return false;
    }
  }

  // Get active FCM tokens for a user (using auth_id)
  static Future<List<String>> getUserFCMTokens(String userId) async {
    try {
      final response = await _supabase
          .from('user_fcm_tokens')
          .select('fcm_token')
          .eq('user_id', userId)
          .eq('is_active', true);

      return (response as List)
          .map((token) => token['fcm_token'] as String)
          .toList();
    } catch (e) {
      print('❌ Error getting FCM tokens: $e');
      return [];
    }
  }

  // Get FCM tokens for a patient using patient_id (converts to auth_id first)
  static Future<List<String>> getPatientFCMTokens(String patientId) async {
    try {
      // First, get the auth_id for this patient
      final patientResponse =
          await _supabase
              .from('patients')
              .select('auth_id')
              .eq('id', patientId)
              .maybeSingle();

      if (patientResponse == null || patientResponse['auth_id'] == null) {
        print('⚠️ No auth_id found for patient: $patientId');
        return [];
      }

      final authId = patientResponse['auth_id'] as String;
      print('🔍 Found auth_id for patient $patientId: $authId');

      // Now get FCM tokens using auth_id
      return await getUserFCMTokens(authId);
    } catch (e) {
      print('❌ Error getting patient FCM tokens: $e');
      return [];
    }
  }

  // Create scheduled notification
  static Future<String?> createScheduledNotification({
    required String patientId,
    required String reminderId,
    required String notificationType,
    required String title,
    required String body,
    required String scheduledTime, // Format: "HH:mm"
    required List<int> daysOfWeek, // [1,2,3,4,5,6,7]
  }) async {
    try {
      // Validate and fix reminderId if it's not a valid UUID
      String validReminderId = reminderId;
      if (!_isValidUUID(reminderId)) {
        print(
          '⚠️ Invalid UUID for reminderId: $reminderId, generating new UUID',
        );
        validReminderId = const Uuid().v4();
      }

      // Note: patientId can be a regular number (from patients table)
      // We'll use it as-is since scheduled_notifications table should accept it

      final response =
          await _supabase
              .from('scheduled_notifications')
              .insert({
                'patient_id': patientId,
                'reminder_id': validReminderId,
                'notification_type': notificationType,
                'title': title,
                'body': body,
                'scheduled_time': scheduledTime,
                'days_of_week': daysOfWeek,
                'is_active': true,
              })
              .select('id')
              .single();

      final notificationId = response['id'] as String;
      print('✅ Scheduled notification created: $notificationId');
      return notificationId;
    } catch (e) {
      print('❌ Error creating scheduled notification: $e');
      return null;
    }
  }

  // Update scheduled notification
  static Future<bool> updateScheduledNotification({
    required String notificationId,
    String? title,
    String? body,
    String? scheduledTime,
    List<int>? daysOfWeek,
    bool? isActive,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (title != null) updateData['title'] = title;
      if (body != null) updateData['body'] = body;
      if (scheduledTime != null) updateData['scheduled_time'] = scheduledTime;
      if (daysOfWeek != null) updateData['days_of_week'] = daysOfWeek;
      if (isActive != null) updateData['is_active'] = isActive;

      await _supabase
          .from('scheduled_notifications')
          .update(updateData)
          .eq('id', notificationId);

      print('✅ Scheduled notification updated: $notificationId');
      return true;
    } catch (e) {
      print('❌ Error updating scheduled notification: $e');
      return false;
    }
  }

  // Delete scheduled notification
  static Future<bool> deleteScheduledNotification(String notificationId) async {
    try {
      await _supabase
          .from('scheduled_notifications')
          .update({'is_active': false})
          .eq('id', notificationId);

      print('✅ Scheduled notification deleted: $notificationId');
      return true;
    } catch (e) {
      print('❌ Error deleting scheduled notification: $e');
      return false;
    }
  }

  // Get scheduled notifications for a patient
  static Future<List<Map<String, dynamic>>> getPatientScheduledNotifications(
    String patientId,
  ) async {
    try {
      final response = await _supabase
          .from('scheduled_notifications')
          .select('*')
          .eq('patient_id', patientId)
          .eq('is_active', true)
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('❌ Error getting scheduled notifications: $e');
      return [];
    }
  }

  // Get notification by reminder ID
  static Future<Map<String, dynamic>?> getNotificationByReminderId(
    String reminderId,
  ) async {
    try {
      final response =
          await _supabase
              .from('scheduled_notifications')
              .select('*')
              .eq('reminder_id', reminderId)
              .eq('is_active', true)
              .maybeSingle();

      return response;
    } catch (e) {
      print('❌ Error getting notification by reminder ID: $e');
      return null;
    }
  }

  // Send notification now (for testing or immediate sending)
  static Future<bool> sendNotificationNow({
    required String patientId,
    required String notificationType,
    required String title,
    required String body,
    Map<String, String>? additionalData,
  }) async {
    try {
      // Get patient's FCM tokens
      final fcmTokens = await getPatientFCMTokens(patientId);

      if (fcmTokens.isEmpty) {
        print('❌ No FCM tokens found for patient: $patientId');
        return false;
      }

      // Get patient name (you might need to adjust this based on your patient table structure)
      final patientResponse =
          await _supabase
              .from('patients')
              .select('name')
              .eq('id', patientId)
              .single();

      final patientName = patientResponse['name'] as String;

      // Send to all tokens
      bool allSent = true;
      for (final token in fcmTokens) {
        final success =
            await FirebaseMessagingService.sendPersonalizedNotification(
              fcmToken: token,
              patientName: patientName,
              notificationType: notificationType,
              content: body,
              additionalData: additionalData,
            );

        // Log the notification
        await _logNotification(
          patientId: patientId,
          fcmToken: token,
          title: title,
          body: body,
          status: success ? 'sent' : 'failed',
        );

        if (!success) allSent = false;
      }

      return allSent;
    } catch (e) {
      print('❌ Error sending notification now: $e');
      return false;
    }
  }

  // Log notification to database
  static Future<void> _logNotification({
    required String patientId,
    required String fcmToken,
    required String title,
    required String body,
    required String status,
    String? scheduledNotificationId,
    Map<String, dynamic>? firebaseResponse,
    String? errorMessage,
  }) async {
    try {
      await _supabase.from('notification_logs').insert({
        'scheduled_notification_id': scheduledNotificationId,
        'patient_id': patientId,
        'fcm_token': fcmToken,
        'title': title,
        'body': body,
        'status': status,
        'firebase_response': firebaseResponse,
        'error_message': errorMessage,
      });
    } catch (e) {
      print('❌ Error logging notification: $e');
    }
  }

  // Get notifications that should be sent now
  static Future<List<Map<String, dynamic>>> getNotificationsDueNow() async {
    try {
      final now = DateTime.now();
      final currentTime =
          '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
      final currentDayOfWeek = now.weekday; // 1 = Monday, 7 = Sunday

      final response = await _supabase
          .from('scheduled_notifications')
          .select('*')
          .eq('is_active', true)
          .eq('scheduled_time', currentTime);

      // Filter by day of week
      final dueNotifications =
          (response as List).where((notification) {
            final daysOfWeek = List<int>.from(notification['days_of_week']);
            return daysOfWeek.contains(currentDayOfWeek);
          }).toList();

      return List<Map<String, dynamic>>.from(dueNotifications);
    } catch (e) {
      print('❌ Error getting due notifications: $e');
      return [];
    }
  }

  // Process and send all due notifications
  static Future<void> processDueNotifications() async {
    try {
      final dueNotifications = await getNotificationsDueNow();

      for (final notification in dueNotifications) {
        await _sendScheduledNotification(notification);
      }

      if (dueNotifications.isNotEmpty) {
        print('✅ Processed ${dueNotifications.length} due notifications');
      }
    } catch (e) {
      print('❌ Error processing due notifications: $e');
    }
  }

  // Send a specific scheduled notification
  static Future<void> _sendScheduledNotification(
    Map<String, dynamic> notification,
  ) async {
    try {
      final patientId = notification['patient_id'] as String;
      final title = notification['title'] as String;
      final body = notification['body'] as String;
      final notificationType = notification['notification_type'] as String;
      final notificationId = notification['id'] as String;

      // Get patient's FCM tokens
      final fcmTokens = await getPatientFCMTokens(patientId);

      if (fcmTokens.isEmpty) {
        print('❌ No FCM tokens found for patient: $patientId');
        return;
      }

      // Get patient name
      final patientResponse =
          await _supabase
              .from('patients')
              .select('name')
              .eq('id', patientId)
              .single();

      final patientName = patientResponse['name'] as String;

      // Send to all tokens
      for (final token in fcmTokens) {
        final success =
            await FirebaseMessagingService.sendPersonalizedNotification(
              fcmToken: token,
              patientName: patientName,
              notificationType: notificationType,
              content: body,
              additionalData: {
                'scheduled_notification_id': notificationId,
                'reminder_type': notificationType,
              },
            );

        // Log the notification
        await _logNotification(
          scheduledNotificationId: notificationId,
          patientId: patientId,
          fcmToken: token,
          title: title,
          body: body,
          status: success ? 'sent' : 'failed',
        );
      }
    } catch (e) {
      print('❌ Error sending scheduled notification: $e');
    }
  }
}
