"use strict";
/**
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BundleDescriptor = exports.StreamDescriptor = exports.PageDescriptor = exports.LongrunningDescriptor = void 0;
var longRunningDescriptor_1 = require("./longRunningCalls/longRunningDescriptor");
Object.defineProperty(exports, "LongrunningDescriptor", { enumerable: true, get: function () { return longRunningDescriptor_1.LongRunningDescriptor; } });
var pageDescriptor_1 = require("./paginationCalls/pageDescriptor");
Object.defineProperty(exports, "PageDescriptor", { enumerable: true, get: function () { return pageDescriptor_1.PageDescriptor; } });
var streamDescriptor_1 = require("./streamingCalls/streamDescriptor");
Object.defineProperty(exports, "StreamDescriptor", { enumerable: true, get: function () { return streamDescriptor_1.StreamDescriptor; } });
var bundleDescriptor_1 = require("./bundlingCalls/bundleDescriptor");
Object.defineProperty(exports, "BundleDescriptor", { enumerable: true, get: function () { return bundleDescriptor_1.BundleDescriptor; } });
//# sourceMappingURL=descriptor.js.map