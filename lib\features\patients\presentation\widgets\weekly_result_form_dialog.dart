import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/weekly_result_model.dart';
import '../../../../core/widgets/loading_dialog.dart';
import '../bloc/weekly_results_bloc.dart';
import '../bloc/weekly_results_event.dart';

class WeeklyResultFormDialog extends StatefulWidget {
  final String patientId;
  final WeeklyResultModel? weeklyResult;

  const WeeklyResultFormDialog({
    super.key,
    required this.patientId,
    this.weeklyResult,
  });

  @override
  State<WeeklyResultFormDialog> createState() => _WeeklyResultFormDialogState();
}

class _WeeklyResultFormDialogState extends State<WeeklyResultFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _weightController;
  late TextEditingController _bodyFatController;
  late TextEditingController _visceralFatController;
  late TextEditingController _waterPercentageController;
  late TextEditingController _muscleMassController;
  late TextEditingController _notesController;

  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _weightController = TextEditingController(
      text: widget.weeklyResult?.weight?.toString() ?? '',
    );
    _bodyFatController = TextEditingController(
      text: widget.weeklyResult?.bodyFat?.toString() ?? '',
    );
    _visceralFatController = TextEditingController(
      text: widget.weeklyResult?.visceralFat?.toString() ?? '',
    );
    _waterPercentageController = TextEditingController(
      text: widget.weeklyResult?.waterPercentage?.toString() ?? '',
    );
    _muscleMassController = TextEditingController(
      text: widget.weeklyResult?.muscleMass?.toString() ?? '',
    );
    _notesController = TextEditingController(
      text: widget.weeklyResult?.notes ?? '',
    );

    if (widget.weeklyResult != null) {
      _selectedDate = widget.weeklyResult!.recordedDate;
    }
  }

  @override
  void dispose() {
    _weightController.dispose();
    _bodyFatController.dispose();
    _visceralFatController.dispose();
    _waterPercentageController.dispose();
    _muscleMassController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  void _saveWeeklyResult() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Show loading dialog immediately
    final isUpdate = widget.weeklyResult != null;
    LoadingDialog.show(
      context,
      isUpdate ? 'جاري تحديث النتيجة...' : 'جاري إضافة النتيجة...'
    );

    final weeklyResult = WeeklyResultModel(
      id: widget.weeklyResult?.id ?? '',
      patientId: widget.patientId,
      recordedDate: _selectedDate,
      weight: _weightController.text.isNotEmpty
          ? double.tryParse(_weightController.text)
          : null,
      bodyFat: _bodyFatController.text.isNotEmpty
          ? double.tryParse(_bodyFatController.text)
          : null,
      visceralFat: _visceralFatController.text.isNotEmpty
          ? double.tryParse(_visceralFatController.text)
          : null,
      waterPercentage: _waterPercentageController.text.isNotEmpty
          ? double.tryParse(_waterPercentageController.text)
          : null,
      muscleMass: _muscleMassController.text.isNotEmpty
          ? double.tryParse(_muscleMassController.text)
          : null,
      notes: _notesController.text.trim().isNotEmpty
          ? _notesController.text.trim()
          : null,
      createdAt: widget.weeklyResult?.createdAt ?? DateTime.now(),
    );

    if (widget.weeklyResult == null) {
      // Add new weekly result
      context.read<WeeklyResultsBloc>().add(AddWeeklyResult(weeklyResult: weeklyResult));
    } else {
      // Update existing weekly result
      context.read<WeeklyResultsBloc>().add(UpdateWeeklyResult(weeklyResult: weeklyResult));
    }

    Navigator.of(context).pop();
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020), // تاريخ بداية مفتوح
      lastDate: DateTime(2030), // تاريخ نهاية مفتوح
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: AppColors.white,
              surface: AppColors.white,
              onSurface: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.85,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Fixed header
              Container(
                padding: EdgeInsets.all(24.w),
                child: Text(
                  widget.weeklyResult == null ? 'إضافة نتيجة أسبوعية' : 'تعديل النتيجة الأسبوعية',
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),

              // Scrollable content
              Flexible(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Date selector
                      InkWell(
                        onTap: _selectDate,
                        child: Container(
                          padding: EdgeInsets.all(16.w),
                          decoration: BoxDecoration(
                            border: Border.all(color: AppColors.gray300),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.calendar_today, color: AppColors.primary),
                              SizedBox(width: 12.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'تاريخ القياس',
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                        color: AppColors.textSecondary,
                                      ),
                                    ),
                                    SizedBox(height: 4.h),
                                    Text(
                                      DateFormat('dd/MM/yyyy').format(_selectedDate),
                                      style: TextStyle(
                                        fontSize: 16.sp,
                                        color: AppColors.textPrimary,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Icon(Icons.arrow_drop_down, color: AppColors.textSecondary),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 16.h),

                      // Weight
                      SizedBox(
                        height: 80.h,
                        child: TextFormField(
                          controller: _weightController,
                          decoration: InputDecoration(
                            labelText: 'الوزن (كجم)',
                            hintText: 'أدخل الوزن',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            alignLabelWithHint: true,
                          ),
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          maxLines: null,
                          expands: true,
                          textAlignVertical: TextAlignVertical.top,
                        ),
                      ),
                      SizedBox(height: 16.h),

                      // Body Fat
                      SizedBox(
                        height: 80.h,
                        child: TextFormField(
                          controller: _bodyFatController,
                          decoration: InputDecoration(
                            labelText: 'نسبة الدهون (%)',
                            hintText: 'أدخل نسبة الدهون',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            alignLabelWithHint: true,
                          ),
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          maxLines: null,
                          expands: true,
                          textAlignVertical: TextAlignVertical.top,
                        ),
                      ),
                      SizedBox(height: 16.h),

                      // Visceral Fat
                      SizedBox(
                        height: 80.h,
                        child: TextFormField(
                          controller: _visceralFatController,
                          decoration: InputDecoration(
                            labelText: 'الدهون الحشوية',
                            hintText: 'أدخل مستوى الدهون الحشوية',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            alignLabelWithHint: true,
                          ),
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          maxLines: null,
                          expands: true,
                          textAlignVertical: TextAlignVertical.top,
                        ),
                      ),
                      SizedBox(height: 16.h),

                      // Water Percentage
                      SizedBox(
                        height: 80.h,
                        child: TextFormField(
                          controller: _waterPercentageController,
                          decoration: InputDecoration(
                            labelText: 'نسبة السوائل (%)',
                            hintText: 'أدخل نسبة السوائل',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            alignLabelWithHint: true,
                          ),
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          maxLines: null,
                          expands: true,
                          textAlignVertical: TextAlignVertical.top,
                        ),
                      ),
                      SizedBox(height: 16.h),

                      // Muscle Mass
                      SizedBox(
                        height: 80.h,
                        child: TextFormField(
                          controller: _muscleMassController,
                          decoration: InputDecoration(
                            labelText: 'كتلة العضلات (كجم)',
                            hintText: 'أدخل كتلة العضلات',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            alignLabelWithHint: true,
                          ),
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          maxLines: null,
                          expands: true,
                          textAlignVertical: TextAlignVertical.top,
                        ),
                      ),
                      SizedBox(height: 16.h),

                      // Notes
                      SizedBox(
                        height: 120.h,
                        child: TextFormField(
                          controller: _notesController,
                          decoration: InputDecoration(
                            labelText: 'ملاحظات (اختياري)',
                            hintText: 'أدخل أي ملاحظات إضافية',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            alignLabelWithHint: true,
                          ),
                          maxLines: null,
                          expands: true,
                          textAlignVertical: TextAlignVertical.top,
                        ),
                      ),
                      SizedBox(height: 24.h),
                    ],
                  ),
                ),
              ),

              // Fixed footer with action buttons
              Container(
                padding: EdgeInsets.all(24.w),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        child: Text(
                          'إلغاء',
                          style: TextStyle(fontSize: 16.sp),
                        ),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _saveWeeklyResult,
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        child: Text(
                          widget.weeklyResult == null ? 'إضافة' : 'تحديث',
                          style: TextStyle(fontSize: 16.sp),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
