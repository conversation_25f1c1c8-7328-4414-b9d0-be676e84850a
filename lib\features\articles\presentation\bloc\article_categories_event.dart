import 'package:equatable/equatable.dart';
import '../../../../core/models/article_category_model.dart';

abstract class ArticleCategoriesEvent extends Equatable {
  const ArticleCategoriesEvent();

  @override
  List<Object?> get props => [];
}

class LoadAllArticleCategories extends ArticleCategoriesEvent {}

class LoadActiveArticleCategories extends ArticleCategoriesEvent {}

class SearchArticleCategories extends ArticleCategoriesEvent {
  final String query;

  const SearchArticleCategories({required this.query});

  @override
  List<Object?> get props => [query];
}

class CreateArticleCategory extends ArticleCategoriesEvent {
  final ArticleCategoryModel category;

  const CreateArticleCategory({required this.category});

  @override
  List<Object?> get props => [category];
}

class UpdateArticleCategory extends ArticleCategoriesEvent {
  final ArticleCategoryModel category;

  const UpdateArticleCategory({required this.category});

  @override
  List<Object?> get props => [category];
}

class DeleteArticleCategory extends ArticleCategoriesEvent {
  final String categoryId;

  const DeleteArticleCategory({required this.categoryId});

  @override
  List<Object?> get props => [categoryId];
}

class ToggleArticleCategoryStatus extends ArticleCategoriesEvent {
  final String categoryId;

  const ToggleArticleCategoryStatus({required this.categoryId});

  @override
  List<Object?> get props => [categoryId];
}
