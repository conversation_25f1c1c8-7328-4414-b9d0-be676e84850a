import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/widgets/loading_dialog.dart';
import '../bloc/time_slots_bloc.dart';
import '../bloc/time_slots_event.dart';
import '../bloc/time_slots_state.dart';
import 'time_slot_form_page.dart';

class TimeSlotsPage extends StatefulWidget {
  const TimeSlotsPage({super.key});

  @override
  State<TimeSlotsPage> createState() => _TimeSlotsPageState();
}

class _TimeSlotsPageState extends State<TimeSlotsPage>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // Load data when widget is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<TimeSlotsBloc>().add(LoadAllTimeSlots());
      }
    });
  }

  void _showAddTimeSlotForm({int? selectedDay}) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => BlocProvider.value(
              value: context.read<TimeSlotsBloc>(),
              child: TimeSlotFormPage(preSelectedDay: selectedDay),
            ),
      ),
    );
  }

  void _showEditTimeSlotForm(TimeSlotModel timeSlot) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => BlocProvider.value(
              value: context.read<TimeSlotsBloc>(),
              child: TimeSlotFormPage(timeSlot: timeSlot, isEditing: true),
            ),
      ),
    );
  }

  void _deleteTimeSlot(String timeSlotId) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: const Text(
              'هل أنت متأكد من حذف هذا الموعد؟\n\nسيتم حذف جميع المواعيد المتاحة المرتبطة به.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.read<TimeSlotsBloc>().add(
                    DeleteTimeSlot(timeSlotId: timeSlotId),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                ),
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }

  Future<void> _onRefreshTimeSlots() async {
    // تحديث بيانات المواعيد الثابتة
    context.read<TimeSlotsBloc>().add(LoadAllTimeSlots());
    // انتظار قصير لإظهار مؤشر التحديث
    await Future.delayed(const Duration(milliseconds: 500));
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Scaffold(
      backgroundColor: AppColors.background,
      body: BlocConsumer<TimeSlotsBloc, TimeSlotsState>(
        listener: (context, state) {
          if (state is TimeSlotsLoading) {
            LoadingDialog.show(context, 'جاري التحميل...');
          } else if (state is TimeSlotsLoaded) {
            LoadingDialog.hide(context);
          } else if (state is TimeSlotsError) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          } else if (state is TimeSlotCreated) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إنشاء الموعد بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is TimeSlotUpdated) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم تحديث الموعد بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is TimeSlotDeleted) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حذف الموعد بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is AppointmentsGenerated) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'تم إنشاء المواعيد لـ ${state.weeksGenerated} أسابيع قادمة',
                ),
                backgroundColor: AppColors.success,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is TimeSlotsLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is TimeSlotsLoaded) {
            return _buildTimeSlotsView(state);
          }

          if (state is TimeSlotsError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64.w, color: AppColors.error),
                  SizedBox(height: 16.h),
                  Text(
                    'حدث خطأ في تحميل البيانات',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    state.message,
                    style: TextStyle(fontSize: 14.sp, color: AppColors.error),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16.h),
                  ElevatedButton(
                    onPressed:
                        () => context.read<TimeSlotsBloc>().add(
                          LoadAllTimeSlots(),
                        ),
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          // Initial state or unknown state
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.schedule, size: 64.w, color: AppColors.gray400),
                SizedBox(height: 16.h),
                Text(
                  'جاري تحميل المواعيد الثابتة...',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
                SizedBox(height: 16.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton(
                      onPressed:
                          () => context.read<TimeSlotsBloc>().add(
                            LoadAllTimeSlots(),
                          ),
                      child: const Text('تحميل البيانات'),
                    ),
                    SizedBox(width: 12.w),
                    OutlinedButton(
                      onPressed: () async {
                        final scaffoldMessenger = ScaffoldMessenger.of(context);
                        try {
                          debugPrint(
                            '🧪 Manual test: Testing Supabase connection...',
                          );
                          final response = await SupabaseConfig.timeSlots
                              .select()
                              .limit(1);
                          debugPrint(
                            '🧪 Manual test: Success! Got ${response.length} records',
                          );
                          if (mounted) {
                            scaffoldMessenger.showSnackBar(
                              SnackBar(
                                content: Text(
                                  'اختبار الاتصال نجح! وجد ${response.length} سجل',
                                ),
                                backgroundColor: AppColors.success,
                              ),
                            );
                          }
                        } catch (e) {
                          debugPrint('🧪 Manual test: Error: $e');
                          if (mounted) {
                            scaffoldMessenger.showSnackBar(
                              SnackBar(
                                content: Text('خطأ في الاتصال: $e'),
                                backgroundColor: AppColors.error,
                              ),
                            );
                          }
                        }
                      },
                      child: const Text('اختبار الاتصال'),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildTimeSlotsView(TimeSlotsLoaded state) {
    if (state.timeSlots.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.schedule, size: 64.w, color: AppColors.gray400),
            SizedBox(height: 16.h),
            Text(
              'لا توجد مواعيد ثابتة',
              style: TextStyle(fontSize: 18.sp, color: AppColors.textSecondary),
            ),
            SizedBox(height: 8.h),
            Text(
              'اضغط على + لإضافة موعد جديد',
              style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _onRefreshTimeSlots,
      child: ListView.builder(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16.w),
        itemCount: 7, // 7 days of the week
        itemBuilder: (context, index) {
          final daySlots = state.timeSlotsByDay[index] ?? [];
          return _buildDaySection(index, daySlots);
        },
      ),
    );
  }

  Widget _buildDaySection(int dayOfWeek, List<TimeSlotModel> timeSlots) {
    const dayNames = [
      'الأحد', // 0
      'الإثنين', // 1
      'الثلاثاء', // 2
      'الأربعاء', // 3
      'الخميس', // 4
      'الجمعة', // 5
      'السبت', // 6
    ];

    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.2),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Day header
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color:
                  timeSlots.isNotEmpty ? AppColors.primary : AppColors.gray100,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            child: Row(
              children: [
                Text(
                  dayNames[dayOfWeek],
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color:
                        timeSlots.isNotEmpty
                            ? AppColors.white
                            : AppColors.textSecondary,
                  ),
                ),
                const Spacer(),
                Text(
                  '${timeSlots.length} موعد',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color:
                        timeSlots.isNotEmpty
                            ? AppColors.white
                            : AppColors.textSecondary,
                  ),
                ),
                SizedBox(width: 8.w),
                GestureDetector(
                  onTap: () => _showAddTimeSlotForm(selectedDay: dayOfWeek),
                  child: Container(
                    padding: EdgeInsets.all(4.w),
                    decoration: BoxDecoration(
                      color: (timeSlots.isNotEmpty
                              ? AppColors.white
                              : AppColors.primary)
                          .withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Icon(
                      Icons.add,
                      size: 16.w,
                      color:
                          timeSlots.isNotEmpty
                              ? AppColors.white
                              : AppColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Time slots
          if (timeSlots.isNotEmpty)
            ...timeSlots.map((timeSlot) => _buildTimeSlotCard(timeSlot))
          else
            Padding(
              padding: EdgeInsets.all(16.w),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'لا توجد مواعيد في هذا اليوم',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                  SizedBox(width: 8.w),
                  TextButton.icon(
                    onPressed:
                        () => _showAddTimeSlotForm(selectedDay: dayOfWeek),
                    icon: Icon(Icons.add, size: 16.w),
                    label: const Text('إضافة موعد'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.primary,
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 4.h,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTimeSlotCard(TimeSlotModel timeSlot) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: timeSlot.isActive ? AppColors.white : AppColors.gray50,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color:
              timeSlot.isActive
                  ? AppColors.primary.withValues(alpha: 0.2)
                  : AppColors.gray300,
        ),
      ),
      child: Row(
        children: [
          // Time range
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  timeSlot.timeRange,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color:
                        timeSlot.isActive
                            ? AppColors.textPrimary
                            : AppColors.textSecondary,
                  ),
                ),
                Text(
                  '${timeSlot.durationMinutes} دقيقة',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          // Status
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: timeSlot.isActive ? AppColors.success : AppColors.gray400,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Text(
              timeSlot.isActive ? 'نشط' : 'غير نشط',
              style: TextStyle(
                fontSize: 10.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.white,
              ),
            ),
          ),

          SizedBox(width: 8.w),

          // Actions
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'edit') {
                _showEditTimeSlotForm(timeSlot);
              } else if (value == 'toggle') {
                context.read<TimeSlotsBloc>().add(
                  ToggleTimeSlotStatus(timeSlotId: timeSlot.id),
                );
              } else if (value == 'delete') {
                _deleteTimeSlot(timeSlot.id);
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, color: AppColors.primary),
                        SizedBox(width: 8),
                        Text('تعديل'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'toggle',
                    child: Row(
                      children: [
                        Icon(
                          timeSlot.isActive
                              ? Icons.visibility_off
                              : Icons.visibility,
                          color: AppColors.warning,
                        ),
                        const SizedBox(width: 8),
                        Text(timeSlot.isActive ? 'إلغاء التفعيل' : 'تفعيل'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: AppColors.error),
                        SizedBox(width: 8),
                        Text('حذف'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
    );
  }
}
