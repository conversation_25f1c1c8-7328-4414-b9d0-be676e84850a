{"version": 3, "file": "fallback.js", "sourceRoot": "", "sources": ["../../src/fallback.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAsWH,kBAOC;AA0BD,sCA0BC;AA9ZD,0CAA0C;AAC1C,uCAAuC;AA+Z/B,4BAAQ;AA9ZhB,6BAA6B;AAC7B,iDAAiD;AA6BzC,sCAAa;AA5BrB,qCAAgC;AAChC,6DAQ6B;AAC7B,yDAA2D;AAI3D,mDAAgE;AAEhE,+CAA+C;AAC/C,yDAA4C;AAC5C,+DAA0D;AAC1D,0DAAsD;AACtD,iCAAwC;AAExC,mDAAmD;AAiVzB,8BAAS;AAhVnC,sDAAsD;AAgVjB,wCAAc;AA/UnD,yDAAyD;AA+UjD,4CAAgB;AA5UxB,+CAA4C;AAApC,4GAAA,YAAY,OAAA;AAEpB,6BAKe;AAJb,mGAAA,YAAY,OAAA;AACZ,wGAAA,iBAAiB,OAAA;AACjB,mGAAA,YAAY,OAAA;AACZ,mHAAA,4BAA4B,OAAA;AAEjB,QAAA,OAAO,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC,OAAO,GAAG,WAAW,CAAC;AAE3E,2CAKsB;AAJpB,8GAAA,gBAAgB,OAAA;AAChB,mHAAA,qBAAqB,OAAA;AACrB,4GAAA,cAAc,OAAA;AACd,8GAAA,gBAAgB,OAAA;AAGlB,wDAAsD;AAA9C,uGAAA,UAAU,OAAA;AAElB,uDAAoD;AAA5C,oHAAA,gBAAgB,OAAA;AACxB,2CAAuC;AAA/B,uGAAA,SAAS,OAAA;AACjB,qDAAkD;AAA1C,kHAAA,eAAe,OAAA;AAEvB,+BAAgC;AAAxB,gGAAA,QAAQ,OAAA;AAEH,QAAA,sBAAsB,GAAG;IACpC,QAAQ,EAAE,KAAK;IACf,KAAK,EAAE,MAAM;IACb,KAAK,EAAE,MAAM;IACb,QAAQ,EAAE,IAAI;IACd,MAAM,EAAE,IAAI;CACb,CAAC;AAEF,MAAM,qBAAqB,GAAG,mBAAmB,CAAC;AAalD,MAAa,UAAU;IASrB;;;OAGG;IACH,MAAM,CAAC,eAAe;QACpB,UAAU,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAChC,CAAC;IAED;;;;;;;OAOG;IAEH,YACE,UAMI,EAAE;;QAEN,IAAI,CAAC,IAAA,2BAAQ,GAAE,EAAE,CAAC;YAChB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;gBAClB,MAAM,IAAI,KAAK,CACb,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;oBACrB,qJAAqJ,CACxJ,CAAC;YACJ,CAAC;YACD,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAoB,CAAC;QAC3C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI;gBACN,OAAO,CAAC,IAAmB;oBAC5B,IAAI,gCAAU,CAAC,OAA4B,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;QAChD,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC;QACzD,IAAI,CAAC,SAAS,GAAI,OAA6B,CAAC,SAAS,CAAC;QAC1D,IAAI,CAAC,YAAY,GAAG,MAAC,OAA6B,CAAC,YAAY,mCAAI,KAAK,CAAC;IAC3E,CAAC;IAED;;;;;OAKG;IACH,SAAS,CAAC,UAAc;QACtB,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACtD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,aAAa,CAAC,IAAyB,EAAE,WAAW,GAAG,KAAK;QAC1D,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QACzD,MAAM,MAAM,GAAG,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YAC3B,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC1C,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,OAAyB;QACxD,MAAM,OAAO,GAAsC,EAAE,CAAC;QACtD,KAAK,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YACzE,MAAM,wBAAwB,GAAG,IAAA,uBAAgB,EAAC,UAAU,CAAC,CAAC;YAC9D,OAAO,CAAC,wBAAwB,CAAC,GAAG,YAAY,CAAC;QACnD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;;;;;;;OAWG;IACH,iBAAiB,CACf,WAAmB,EACnB,YAA8B,EAC9B,eAAiC,EACjC,OAA4B;QAE5B,SAAS,aAAa,CAAC,OAAW,EAAE,WAAgC;YAClE,MAAM,QAAQ,GAAwB,EAAE,CAAC;YACzC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,GAAG,EAAE,CAAC;YACf,CAAC;YACD,iDAAiD;YACjD,2CAA2C;YAC3C,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;gBAC1B,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;oBACzC,CAAC,CAAE,OAAO,CAAC,GAAG,CAAc;oBAC5B,CAAC,CAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAc,CAAC;YACnC,CAAC;YAED,qEAAqE;YACrE,MAAM,cAAc,GAAa,EAAE,CAAC;YACpC,IACE,QAAQ,CAAC,qBAAqB,CAAC;gBAE7B,QAAQ,CAAC,qBAAqB,CAC/B,CAAC,CAAC,CAAC,EACJ,CAAC;gBACD,cAAc,CAAC,IAAI,CACjB,GAAI,QAAQ,CAAC,qBAAqB,CAAc,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAC/D,CAAC;YACJ,CAAC;YACD,cAAc,CAAC,IAAI,CAAC,YAAY,eAAO,EAAE,CAAC,CAAC;YAC3C,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAE7D,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,OAAO,QAAQ,CAAC;YAClB,CAAC;YACD,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;gBAC9B,IAAI,GAAG,CAAC,WAAW,EAAE,KAAK,qBAAqB,EAAE,CAAC;oBAChD,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;oBAC/B,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;wBACzB,IAAI,QAAQ,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;4BAChC,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;wBACxB,CAAC;6BAAM,CAAC;4BACN,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gCAE/B,QAAQ,CAAC,GAAG,CAGb,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;4BACnB,CAAC;iCAAM,CAAC;gCACN,MAAM,IAAI,KAAK,CACb,qBAAqB,KAAK,wBAAwB,CACnD,CAAC;4BACJ,CAAC;wBACH,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAa,CAAC;oBACtC,CAAC;gBACH,CAAC;YACH,CAAC;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,OAAO,GAAG,CAAC,iBAAiB,CAC1B,WAAW,EACX,YAAY,EACZ,eAAe,EACf,eAAM,EACN,EAAC,eAAe,EAAE,aAAa,EAAC,CACjC,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACH,KAAK,CAAC,UAAU,CACd,OAAyB,EACzB,IAAuB;IACvB,4EAA4E;IAC5E,6DAA6D;IAC7D,iBAA2B;QAE3B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,IAAI,IAAI,WAAW,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBAC1C,IAAI,CAAC,UAAU,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAe,CAAC;YAChE,CAAC;iBAAM,IAAI,IAAI,CAAC,IAAI,IAAI,mBAAmB,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBACzD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC;YAC9B,CAAC;QACH,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,IAAI,CAAC,cAAc,GAAG,gBAAgB,CAAC;QACzC,CAAC;QACD,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,MAAM,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;YACxD,IAAI,gBAAgB,IAAI,IAAI,CAAC,cAAc,KAAK,gBAAgB,EAAE,CAAC;gBACjE,MAAM,IAAI,KAAK,CACb,mCAAmC,IAAI,CAAC,cAAc,kEAAkE,gBAAgB,KAAK;oBAC3I,0FAA0F,CAC7F,CAAC;YACJ,CAAC;QACH,CAAC;QACD,OAAO,CAAC,UAAU,EAAE,CAAC;QACrB,MAAM,OAAO,GAAG,UAAU,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAEtD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,OAAO,CAAC;QAE1C,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACnC,IACE,CAAC,WAAW;YACZ,OAAO,CAAC,OAAO;YACf,OAAO,CAAC,OAAO,CAAC,2BAA2B,CAAC,EAC5C,CAAC;YACD,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;QAC7D,CAAC;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CACb,iDAAiD,OAAO,CAAC,IAAI,GAAG,CACjE,CAAC;QACJ,CAAC;QAED,IAAI,WAAW,CAAC;QAChB,MAAM,KAAK,GAAG,WAAY,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QACjD,IAAI,KAAK,EAAE,CAAC;YACV,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACvB,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC,CAAC;QACD,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC;QAC1B,CAAC;aAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YACxB,WAAW,GAAG,GAAG,CAAC;QACpB,CAAC;QAED,MAAM,OAAO,GAAG,YAAY,CAAC,aAAa,CAAC;QAC3C,MAAM,OAAO,GAAG,YAAY,CAAC,cAAc,CAAC;QAC5C,MAAM,WAAW,GAAG,IAAA,yCAAmB,EACrC,OAAO,EACP,QAAQ,EACR,WAAW,EACX,WAAW,EACX,IAAI,CAAC,UAAU,EACf,OAAO,EACP,OAAO,EACP,IAAI,CAAC,YAAY,CAClB,CAAC;QAEF,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;;;;;;;;OASG;IACH,MAAM,CAAC,wBAAwB,CAAC,OAAgC;QAC9D,OAAO,GAAG,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;;AA7QH,gCA8QC;AAzQgB,qBAAU,GAAG,IAAI,GAAG,EAAyB,CAAC;AA2Q/D;;;;;GAKG;AACH,SAAgB,GAAG,CAAC,OAA0B;IAC5C,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAC,MAAM,EAAE,EAAE,EAAC,EAAE,OAAO,CAAC,CAAC;IAC/C,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;QACtB,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;IACrD,CAAC;IACD,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;IACxC,OAAO,IAAI,0CAAuB,CAAC,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AACjE,CAAC;AAGD;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,SAAgB,aAAa,CAC3B,IAAkC,EAClC,QAA0B,EAC1B,UAAuB;AACvB,6DAA6D;AAC7D,SAA4B,CAAC,iCAAiC;;IAE9D,IACE,UAAU;QACV,WAAW,IAAI,UAAU;QACxB,UAA+B,CAAC,IAAI,KAAK,sBAAU,CAAC,gBAAgB,EACrE,CAAC;QACD,OAAO,GAAG,EAAE;YACV,MAAM,IAAI,KAAK,CACb,sFAAsF,CACvF,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IACD,IAAI,UAAU,IAAI,WAAW,IAAI,UAAU,IAAI,CAAC,IAAA,2BAAQ,GAAE,EAAE,CAAC;QAC3D,OAAO,GAAG,EAAE;YACV,MAAM,IAAI,KAAK,CACb,wEAAwE,CACzE,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IACD,OAAO,IAAA,6BAAc,EAAC,IAAI,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;AACpD,CAAC;AAGD,wDAAsD;AAEtD,uCAAgC;AAAxB,gGAAA,IAAI,OAAA;AACZ,8DAAoE;AAA5D,wGAAA,SAAS,OAAA;AAAE,wGAAA,SAAS,OAAA;AAC5B,6CAA0C;AAAlC,0GAAA,WAAW,OAAA;AAEnB,4EAA4E;AAC5E,8EAA8E;AAC9E,8DAA8D;AAC9D,uEAAuE;AACvE,qCAAqC;AACrC,2EAA2E;AAC3E,qBAAqB;AACrB,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;AACxB,4BAAQ"}