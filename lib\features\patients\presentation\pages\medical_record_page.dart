import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/patient_model.dart';
import 'tabs/patient_info_tab.dart';
import 'tabs/weekly_results_tab.dart';
import 'tabs/medical_info_tab.dart';
import 'tabs/lab_tests_tab.dart';
import 'tabs/reminders_tab.dart';
import 'tabs/appointment_booking_tab.dart';

class MedicalRecordPage extends StatefulWidget {
  final PatientModel patient;
  final int initialTabIndex;

  const MedicalRecordPage({
    super.key,
    required this.patient,
    this.initialTabIndex = 0, // التاب الافتراضي هو الأول
  });

  @override
  State<MedicalRecordPage> createState() => _MedicalRecordPageState();
}

class _MedicalRecordPageState extends State<MedicalRecordPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late PatientModel _currentPatient;

  @override
  void initState() {
    super.initState();
    _currentPatient = widget.patient;
    _tabController = TabController(
      length: 6,
      vsync: this,
      initialIndex: widget.initialTabIndex, // ✅ استخدام التاب المحدد
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _updatePatient(PatientModel updatedPatient) {
    setState(() {
      _currentPatient = updatedPatient;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text('السجل الطبي - ${_currentPatient.name}'),
        backgroundColor: AppColors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
          isScrollable: true,
          tabAlignment: TabAlignment.start,
          tabs: const [
            Tab(text: 'معلومات المريض'),
            Tab(text: 'النتائج الأسبوعية'),
            Tab(text: 'المعلومات الطبية'),
            Tab(text: 'الفحوصات المخبرية'),
            Tab(text: 'التذكيرات'),
            Tab(text: 'حجز استشارة'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Patient header card
          _buildPatientHeaderCard(),

          // TabBarView
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Tab 1: Patient Info (Read-only)
                PatientInfoTab(
                  patient: _currentPatient,
                  onPatientUpdated: _updatePatient,
                  key: ValueKey(
                    _currentPatient.id,
                  ), // Force rebuild when patient updates
                ),

                // Tab 2: Weekly Results (CRUD)
                WeeklyResultsTab(patientId: _currentPatient.id),

                // Tab 3: Medical Info (CRUD)
                MedicalInfoTab(patientId: _currentPatient.id),

                // Tab 4: Lab Tests (CRUD with image upload)
                LabTestsTab(patientId: _currentPatient.id),

                // Tab 5: Reminders (CRUD)
                RemindersTab(patientId: _currentPatient.id),

                // Tab 6: Appointment Booking
                AppointmentBookingTab(patient: _currentPatient),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPatientHeaderCard() {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        children: [
          // Patient avatar
          Container(
            width: 60.w,
            height: 60.w,
            decoration: BoxDecoration(
              color: AppColors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(30.r),
            ),
            child: Icon(Icons.person, size: 32.w, color: AppColors.white),
          ),
          SizedBox(width: 16.w),

          // Patient info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _currentPatient.name,
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.white,
                  ),
                ),
                SizedBox(height: 4.h),
                Row(
                  children: [
                    Icon(
                      Icons.badge,
                      size: 16.w,
                      color: AppColors.white.withValues(alpha: 0.9),
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      'ID: ${_currentPatient.id}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Premium badge
          if (_currentPatient.isPremium)
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
              decoration: BoxDecoration(
                color: AppColors.warning,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.star, size: 16.w, color: AppColors.white),
                  SizedBox(width: 4.w),
                  Text(
                    'مميز',
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.white,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}
