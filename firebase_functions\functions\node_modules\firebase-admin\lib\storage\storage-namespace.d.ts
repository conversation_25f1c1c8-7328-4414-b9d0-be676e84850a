/*! firebase-admin v12.7.0 */
/*!
 * Copyright 2021 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { App } from '../app';
import { Storage as TStorage } from './storage';
/**
 * Gets the {@link firebase-admin.storage#Storage} service for the
 * default app or a given app.
 *
 * `admin.storage()` can be called with no arguments to access the default
 * app's `Storage` service or as `admin.storage(app)` to access the
 * `Storage` service associated with a specific app.
 *
 * @example
 * ```javascript
 * // Get the Storage service for the default app
 * var defaultStorage = admin.storage();
 * ```
 *
 * @example
 * ```javascript
 * // Get the Storage service for a given app
 * var otherStorage = admin.storage(otherApp);
 * ```
 */
export declare function storage(app?: App): storage.Storage;
export declare namespace storage {
    /**
     * Type alias to {@link firebase-admin.storage#Storage}.
     */
    type Storage = TStorage;
}
