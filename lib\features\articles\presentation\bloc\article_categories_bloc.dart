import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/repositories/article_categories_repository.dart';
import 'article_categories_event.dart';
import 'article_categories_state.dart';

class ArticleCategoriesBloc extends Bloc<ArticleCategoriesEvent, ArticleCategoriesState> {
  final ArticleCategoriesRepository _repository;

  ArticleCategoriesBloc({required ArticleCategoriesRepository repository})
      : _repository = repository,
        super(ArticleCategoriesInitial()) {
    on<LoadAllArticleCategories>(_onLoadAllCategories);
    on<LoadActiveArticleCategories>(_onLoadActiveCategories);
    on<SearchArticleCategories>(_onSearchCategories);
    on<CreateArticleCategory>(_onCreateCategory);
    on<UpdateArticleCategory>(_onUpdateCategory);
    on<DeleteArticleCategory>(_onDeleteCategory);
    on<ToggleArticleCategoryStatus>(_onToggleCategoryStatus);
  }

  Future<void> _onLoadAllCategories(
    LoadAllArticleCategories event,
    Emitter<ArticleCategoriesState> emit,
  ) async {
    emit(ArticleCategoriesLoading());
    try {
      final categories = await _repository.getAllCategories();
      emit(ArticleCategoriesLoaded(
        categories: categories,
        filteredCategories: categories,
      ));
    } catch (e) {
      emit(ArticleCategoriesError(message: e.toString()));
    }
  }

  Future<void> _onLoadActiveCategories(
    LoadActiveArticleCategories event,
    Emitter<ArticleCategoriesState> emit,
  ) async {
    emit(ArticleCategoriesLoading());
    try {
      final categories = await _repository.getAllCategories();
      final activeCategories = categories.where((cat) => cat.isActive).toList();
      emit(ArticleCategoriesLoaded(
        categories: activeCategories,
        filteredCategories: activeCategories,
      ));
    } catch (e) {
      emit(ArticleCategoriesError(message: e.toString()));
    }
  }

  Future<void> _onSearchCategories(
    SearchArticleCategories event,
    Emitter<ArticleCategoriesState> emit,
  ) async {
    if (state is ArticleCategoriesLoaded) {
      final currentState = state as ArticleCategoriesLoaded;
      
      if (event.query.isEmpty) {
        emit(currentState.copyWith(
          filteredCategories: currentState.categories,
          searchQuery: event.query,
        ));
      } else {
        try {
          final filteredCategories = await _repository.searchCategories(event.query);
          emit(currentState.copyWith(
            filteredCategories: filteredCategories,
            searchQuery: event.query,
          ));
        } catch (e) {
          emit(ArticleCategoriesError(message: e.toString()));
        }
      }
    }
  }

  Future<void> _onCreateCategory(
    CreateArticleCategory event,
    Emitter<ArticleCategoriesState> emit,
  ) async {
    try {
      final category = await _repository.createCategory(event.category);
      emit(ArticleCategoryCreated(category: category));
      add(LoadAllArticleCategories());
    } catch (e) {
      emit(ArticleCategoriesError(message: e.toString()));
    }
  }

  Future<void> _onUpdateCategory(
    UpdateArticleCategory event,
    Emitter<ArticleCategoriesState> emit,
  ) async {
    try {
      final category = await _repository.updateCategory(event.category);
      emit(ArticleCategoryUpdated(category: category));
      add(LoadAllArticleCategories());
    } catch (e) {
      emit(ArticleCategoriesError(message: e.toString()));
    }
  }

  Future<void> _onDeleteCategory(
    DeleteArticleCategory event,
    Emitter<ArticleCategoriesState> emit,
  ) async {
    try {
      await _repository.deleteCategory(event.categoryId);
      emit(ArticleCategoryDeleted(categoryId: event.categoryId));
      add(LoadAllArticleCategories());
    } catch (e) {
      emit(ArticleCategoriesError(message: e.toString()));
    }
  }

  Future<void> _onToggleCategoryStatus(
    ToggleArticleCategoryStatus event,
    Emitter<ArticleCategoriesState> emit,
  ) async {
    try {
      final category = await _repository.toggleCategoryStatus(event.categoryId);
      emit(ArticleCategoryUpdated(category: category));
      add(LoadAllArticleCategories());
    } catch (e) {
      emit(ArticleCategoriesError(message: e.toString()));
    }
  }
}
