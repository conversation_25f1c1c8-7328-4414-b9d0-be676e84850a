import 'package:flutter/foundation.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/holiday_model.dart';

class HolidaysRepository {
  // Get all holidays
  Future<List<HolidayModel>> getAllHolidays() async {
    try {
      debugPrint('🔍 HolidaysRepository: Loading all holidays...');
      final response = await SupabaseConfig.holidays
          .select()
          .order('holiday_date', ascending: true);

      debugPrint('📊 HolidaysRepository: Raw response: $response');
      debugPrint('📊 HolidaysRepository: Response length: ${response.length}');

      if (response.isEmpty) {
        debugPrint('⚠️ HolidaysRepository: No holidays found');
        return [];
      }

      final holidays = response.map((json) => HolidayModel.fromJson(json)).toList();
      debugPrint('✅ HolidaysRepository: Successfully parsed ${holidays.length} holidays');
      
      return holidays;
    } catch (e, stackTrace) {
      debugPrint('❌ HolidaysRepository: Error loading holidays: $e');
      debugPrint('📍 HolidaysRepository: Stack trace: $stackTrace');
      throw Exception('فشل في جلب أيام الإجازات: ${e.toString()}');
    }
  }

  // Get active holidays only
  Future<List<HolidayModel>> getActiveHolidays() async {
    try {
      debugPrint('🔍 HolidaysRepository: Loading active holidays...');
      final response = await SupabaseConfig.holidays
          .select()
          .eq('is_active', true)
          .order('holiday_date', ascending: true);

      final holidays = response.map((json) => HolidayModel.fromJson(json)).toList();
      debugPrint('✅ HolidaysRepository: Successfully loaded ${holidays.length} active holidays');
      
      return holidays;
    } catch (e) {
      debugPrint('❌ HolidaysRepository: Error loading active holidays: $e');
      throw Exception('فشل في جلب أيام الإجازات النشطة: ${e.toString()}');
    }
  }

  // Check if a specific date is a holiday
  Future<HolidayModel?> getHolidayByDate(DateTime date) async {
    try {
      final dateString = date.toIso8601String().split('T')[0];
      debugPrint('🔍 HolidaysRepository: Checking holiday for date: $dateString');
      
      final response = await SupabaseConfig.holidays
          .select()
          .eq('holiday_date', dateString)
          .eq('is_active', true)
          .maybeSingle();

      if (response == null) {
        debugPrint('✅ HolidaysRepository: No holiday found for date $dateString');
        return null;
      }

      final holiday = HolidayModel.fromJson(response);
      debugPrint('🎉 HolidaysRepository: Found holiday: ${holiday.occasionName}');
      return holiday;
    } catch (e) {
      debugPrint('❌ HolidaysRepository: Error checking holiday for date: $e');
      throw Exception('فشل في التحقق من الإجازة: ${e.toString()}');
    }
  }

  // Get holidays in date range
  Future<List<HolidayModel>> getHolidaysInRange(DateTime startDate, DateTime endDate) async {
    try {
      final startDateString = startDate.toIso8601String().split('T')[0];
      final endDateString = endDate.toIso8601String().split('T')[0];
      
      debugPrint('🔍 HolidaysRepository: Loading holidays from $startDateString to $endDateString');
      
      final response = await SupabaseConfig.holidays
          .select()
          .gte('holiday_date', startDateString)
          .lte('holiday_date', endDateString)
          .eq('is_active', true)
          .order('holiday_date', ascending: true);

      final holidays = response.map((json) => HolidayModel.fromJson(json)).toList();
      debugPrint('✅ HolidaysRepository: Found ${holidays.length} holidays in range');
      
      return holidays;
    } catch (e) {
      debugPrint('❌ HolidaysRepository: Error loading holidays in range: $e');
      throw Exception('فشل في جلب الإجازات في النطاق المحدد: ${e.toString()}');
    }
  }

  // Add new holiday
  Future<HolidayModel> addHoliday(HolidayModel holiday) async {
    try {
      debugPrint('🔍 HolidaysRepository: Adding new holiday: ${holiday.occasionName}');
      
      final response = await SupabaseConfig.holidays
          .insert(holiday.toJson())
          .select()
          .single();

      final newHoliday = HolidayModel.fromJson(response);
      debugPrint('✅ HolidaysRepository: Successfully added holiday: ${newHoliday.id}');
      
      return newHoliday;
    } catch (e) {
      debugPrint('❌ HolidaysRepository: Error adding holiday: $e');
      throw Exception('فشل في إضافة الإجازة: ${e.toString()}');
    }
  }

  // Update holiday
  Future<HolidayModel> updateHoliday(HolidayModel holiday) async {
    try {
      debugPrint('🔍 HolidaysRepository: Updating holiday: ${holiday.id}');
      
      final response = await SupabaseConfig.holidays
          .update(holiday.toJson())
          .eq('id', holiday.id)
          .select()
          .single();

      final updatedHoliday = HolidayModel.fromJson(response);
      debugPrint('✅ HolidaysRepository: Successfully updated holiday: ${updatedHoliday.id}');
      
      return updatedHoliday;
    } catch (e) {
      debugPrint('❌ HolidaysRepository: Error updating holiday: $e');
      throw Exception('فشل في تحديث الإجازة: ${e.toString()}');
    }
  }

  // Delete holiday
  Future<void> deleteHoliday(String holidayId) async {
    try {
      debugPrint('🔍 HolidaysRepository: Deleting holiday: $holidayId');
      
      await SupabaseConfig.holidays
          .delete()
          .eq('id', holidayId);

      debugPrint('✅ HolidaysRepository: Successfully deleted holiday: $holidayId');
    } catch (e) {
      debugPrint('❌ HolidaysRepository: Error deleting holiday: $e');
      throw Exception('فشل في حذف الإجازة: ${e.toString()}');
    }
  }

  // Toggle holiday active status
  Future<HolidayModel> toggleHolidayStatus(String holidayId, bool isActive) async {
    try {
      debugPrint('🔍 HolidaysRepository: Toggling holiday status: $holidayId to $isActive');
      
      final response = await SupabaseConfig.holidays
          .update({'is_active': isActive, 'updated_at': DateTime.now().toIso8601String()})
          .eq('id', holidayId)
          .select()
          .single();

      final updatedHoliday = HolidayModel.fromJson(response);
      debugPrint('✅ HolidaysRepository: Successfully toggled holiday status');
      
      return updatedHoliday;
    } catch (e) {
      debugPrint('❌ HolidaysRepository: Error toggling holiday status: $e');
      throw Exception('فشل في تغيير حالة الإجازة: ${e.toString()}');
    }
  }

  // Search holidays by occasion name
  Future<List<HolidayModel>> searchHolidays(String query) async {
    try {
      debugPrint('🔍 HolidaysRepository: Searching holidays with query: $query');
      
      final response = await SupabaseConfig.holidays
          .select()
          .ilike('occasion_name', '%$query%')
          .order('holiday_date', ascending: true);

      final holidays = response.map((json) => HolidayModel.fromJson(json)).toList();
      debugPrint('✅ HolidaysRepository: Found ${holidays.length} holidays matching query');
      
      return holidays;
    } catch (e) {
      debugPrint('❌ HolidaysRepository: Error searching holidays: $e');
      throw Exception('فشل في البحث عن الإجازات: ${e.toString()}');
    }
  }
}
