import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../../../../core/constants/app_colors.dart';
import '../../../../../core/models/lab_test_model.dart';
import '../../../../../core/widgets/loading_dialog.dart';
import '../../bloc/lab_tests_bloc.dart';
import '../../bloc/lab_tests_event.dart';
import '../../bloc/lab_tests_state.dart';
import '../../widgets/lab_test_form_dialog.dart';

class LabTestsTab extends StatefulWidget {
  final String patientId;

  const LabTestsTab({
    super.key,
    required this.patientId,
  });

  @override
  State<LabTestsTab> createState() => _LabTestsTabState();
}

class _LabTestsTabState extends State<LabTestsTab> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // Load lab tests when widget is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<LabTestsBloc>().add(
          LoadLabTestsByPatientId(patientId: widget.patientId),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Scaffold(
      backgroundColor: AppColors.background,
      body: BlocConsumer<LabTestsBloc, LabTestsState>(
        listener: (context, state) {
          if (state is LabTestsLoading) {
            LoadingDialog.show(context, 'جاري التحميل...');
          } else if (state is LabTestsLoaded) {
            LoadingDialog.hide(context);
          } else if (state is LabTestCreated) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إضافة الفحص المخبري بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is LabTestUpdated) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم تحديث الفحص المخبري بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is LabTestDeleted) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حذف الفحص المخبري بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is LabTestsError) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              // Add button and search
              _buildTopActionBar(),

              // Lab tests list
              Expanded(
                child: _buildLabTestsList(state),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTopActionBar() {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.2),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Add button
          ElevatedButton.icon(
            onPressed: _showAddLabTestDialog,
            icon: const Icon(Icons.add, size: 20),
            label: const Text('إضافة فحص مخبري'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
          ),

          const Spacer(),

          // Refresh button
          IconButton(
            onPressed: _refreshLabTests,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
        ],
      ),
    );
  }

  Widget _buildLabTestsList(LabTestsState state) {
    if (state is LabTestsLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is LabTestsLoaded) {
      if (state.labTests.isEmpty) {
        return _buildEmptyState();
      }
      return _buildLabTestsListView(state.labTests);
    }

    if (state is LabTestsError) {
      return _buildErrorState(state.message);
    }

    return _buildInitialState();
  }

  Widget _buildLabTestsListView(List<LabTestModel> labTests) {
    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: ListView.builder(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16.w),
        itemCount: labTests.length,
        itemBuilder: (context, index) {
          final labTest = labTests[index];
          return _buildLabTestCard(labTest);
        },
      ),
    );
  }

  Widget _buildLabTestCard(LabTestModel labTest) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray200),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with test name and actions
          Row(
            children: [
              Icon(
                Icons.science,
                size: 20.w,
                color: AppColors.primary,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  labTest.testName,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      _showEditLabTestDialog(labTest);
                      break;
                    case 'delete':
                      _deleteLabTest(labTest);
                      break;
                    case 'view_image':
                      if (labTest.imageUrl != null) {
                        _showImageFullScreen(labTest.imageUrl!);
                      }
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 20),
                        SizedBox(width: 8),
                        Text('تعديل'),
                      ],
                    ),
                  ),
                  if (labTest.imageUrl != null)
                    const PopupMenuItem(
                      value: 'view_image',
                      child: Row(
                        children: [
                          Icon(Icons.image, size: 20),
                          SizedBox(width: 8),
                          Text('عرض الصورة'),
                        ],
                      ),
                    ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 20, color: Colors.red),
                        SizedBox(width: 8),
                        Text('حذف', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 12.h),

          // Test details
          _buildTestDetails(labTest),

          // Image if available
          if (labTest.imageUrl != null) ...[
            SizedBox(height: 12.h),
            _buildTestImage(labTest.imageUrl!),
          ],

          // Results if available
          if (labTest.results != null && labTest.results!.isNotEmpty) ...[
            SizedBox(height: 12.h),
            _buildResultsSection(labTest.results!),
          ],

          // Doctor notes if available
          if (labTest.doctorNotes != null && labTest.doctorNotes!.isNotEmpty) ...[
            SizedBox(height: 12.h),
            _buildDoctorNotesSection(labTest.doctorNotes!),
          ],
        ],
      ),
    );
  }

  Widget _buildTestDetails(LabTestModel labTest) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.gray50,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildDetailItem(
                  'نوع الفحص',
                  _getTestTypeLabel(labTest.testType ?? ''),
                  Icons.category,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildDetailItem(
                  'التاريخ',
                  DateFormat('dd/MM/yyyy').format(labTest.testDate),
                  Icons.calendar_today,
                ),
              ),
            ],
          ),
          if (labTest.isNormal != null) ...[
            SizedBox(height: 8.h),
            _buildDetailItem(
              'الحالة',
              labTest.isNormal! ? 'طبيعي' : 'غير طبيعي',
              labTest.isNormal! ? Icons.check_circle : Icons.warning,
              color: labTest.isNormal! ? AppColors.success : AppColors.error,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailItem(String label, String value, IconData icon, {Color? color}) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.w,
          color: color ?? AppColors.textSecondary,
        ),
        SizedBox(width: 6.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 10.sp,
                  color: AppColors.textSecondary,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w600,
                  color: color ?? AppColors.textPrimary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTestImage(String imageUrl) {
    return GestureDetector(
      onTap: () => _showImageFullScreen(imageUrl),
      child: Container(
        height: 120.h,
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: AppColors.gray200),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8.r),
          child: Image.network(
            imageUrl,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                color: AppColors.gray100,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error,
                      size: 32.w,
                      color: AppColors.error,
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'خطأ في تحميل الصورة',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              );
            },
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Container(
                color: AppColors.gray100,
                child: Center(
                  child: CircularProgressIndicator(
                    value: loadingProgress.expectedTotalBytes != null
                        ? loadingProgress.cumulativeBytesLoaded /
                            loadingProgress.expectedTotalBytes!
                        : null,
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildResultsSection(String results) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.assignment,
            size: 16.w,
            color: AppColors.info,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'النتائج',
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.info,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  results,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDoctorNotesSection(String notes) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.note,
            size: 16.w,
            color: AppColors.warning,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'ملاحظات الطبيب',
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.warning,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  notes,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.science,
            size: 64.w,
            color: AppColors.gray400,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد فحوصات مخبرية',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'ابدأ بإضافة أول فحص مخبري للمريض',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton.icon(
            onPressed: _showAddLabTestDialog,
            icon: const Icon(Icons.add),
            label: const Text('إضافة فحص مخبري'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.w,
            color: AppColors.error,
          ),
          SizedBox(height: 16.h),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.error,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _refreshLabTests,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.science,
            size: 64.w,
            color: AppColors.gray400,
          ),
          SizedBox(height: 16.h),
          Text(
            'جاري تحميل الفحوصات المخبرية...',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _refreshLabTests,
            child: const Text('تحميل البيانات'),
          ),
        ],
      ),
    );
  }

  String _getTestTypeLabel(String testType) {
    switch (testType) {
      case 'blood':
        return 'فحص دم';
      case 'urine':
        return 'فحص بول';
      case 'x_ray':
        return 'أشعة سينية';
      case 'mri':
        return 'رنين مغناطيسي';
      case 'ct_scan':
        return 'أشعة مقطعية';
      case 'ultrasound':
        return 'موجات فوق صوتية';
      case 'other':
        return 'فحص آخر';
      default:
        return testType;
    }
  }

  void _showAddLabTestDialog() {
    showDialog(
      context: context,
      builder: (context) => BlocProvider.value(
        value: context.read<LabTestsBloc>(),
        child: LabTestFormDialog(patientId: widget.patientId),
      ),
    );
  }

  void _showEditLabTestDialog(LabTestModel labTest) {
    showDialog(
      context: context,
      builder: (context) => BlocProvider.value(
        value: context.read<LabTestsBloc>(),
        child: LabTestFormDialog(
          patientId: widget.patientId,
          labTest: labTest,
        ),
      ),
    );
  }

  void _deleteLabTest(LabTestModel labTest) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف فحص "${labTest.testName}"؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Show loading dialog immediately
              LoadingDialog.show(context, 'جاري حذف الفحص...');
              context.read<LabTestsBloc>().add(
                DeleteLabTest(
                  labTestId: labTest.id,
                  patientId: widget.patientId,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _showImageFullScreen(String imageUrl) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            Center(
              child: InteractiveViewer(
                child: Image.network(
                  imageUrl,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      padding: EdgeInsets.all(20.w),
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.error,
                            size: 48.w,
                            color: AppColors.error,
                          ),
                          SizedBox(height: 16.h),
                          Text(
                            'خطأ في تحميل الصورة',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: AppColors.textPrimary,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
            Positioned(
              top: 40.h,
              right: 20.w,
              child: IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _refreshLabTests() {
    context.read<LabTestsBloc>().add(
      RefreshLabTests(patientId: widget.patientId),
    );
  }

  Future<void> _onRefresh() async {
    _refreshLabTests();
    // انتظار قصير لإظهار مؤشر التحديث
    await Future.delayed(const Duration(milliseconds: 500));
  }
}
