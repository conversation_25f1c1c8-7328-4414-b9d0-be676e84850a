import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../../../../core/constants/app_colors.dart';
import '../../../../../core/models/weekly_result_model.dart';
import '../../../../../core/widgets/loading_dialog.dart';
import '../../bloc/weekly_results_bloc.dart';
import '../../bloc/weekly_results_event.dart';
import '../../bloc/weekly_results_state.dart';
import '../../widgets/weekly_result_form_dialog.dart';

class WeeklyResultsTab extends StatefulWidget {
  final String patientId;

  const WeeklyResultsTab({
    super.key,
    required this.patientId,
  });

  @override
  State<WeeklyResultsTab> createState() => _WeeklyResultsTabState();
}

class _WeeklyResultsTabState extends State<WeeklyResultsTab> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // Load weekly results when widget is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<WeeklyResultsBloc>().add(
          LoadWeeklyResultsByPatientId(patientId: widget.patientId),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Scaffold(
      backgroundColor: AppColors.background,
      body: BlocConsumer<WeeklyResultsBloc, WeeklyResultsState>(
        listener: (context, state) {
          if (state is WeeklyResultsLoading) {
            LoadingDialog.show(context, 'جاري التحميل...');
          } else if (state is WeeklyResultsLoaded) {
            LoadingDialog.hide(context);
          } else if (state is WeeklyResultCreated) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إضافة النتيجة الأسبوعية بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is WeeklyResultUpdated) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم تحديث النتيجة الأسبوعية بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is WeeklyResultDeleted) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حذف النتيجة الأسبوعية بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is WeeklyResultsError) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              // Add button
              _buildTopActionBar(),

              // Results list
              Expanded(
                child: _buildResultsList(state),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTopActionBar() {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.2),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Add button
          ElevatedButton.icon(
            onPressed: _showAddResultDialog,
            icon: const Icon(Icons.add, size: 20),
            label: const Text('إضافة نتيجة أسبوعية'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
          ),

          const Spacer(),

          // Refresh button
          IconButton(
            onPressed: _refreshResults,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
        ],
      ),
    );
  }

  Widget _buildResultsList(WeeklyResultsState state) {
    if (state is WeeklyResultsLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is WeeklyResultsLoaded) {
      if (state.weeklyResults.isEmpty) {
        return _buildEmptyState();
      }
      return _buildResultsListView(state.weeklyResults);
    }

    if (state is WeeklyResultsError) {
      return _buildErrorState(state.message);
    }

    return _buildInitialState();
  }

  Widget _buildResultsListView(List<WeeklyResultModel> results) {
    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: ListView.builder(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16.w),
        itemCount: results.length,
        itemBuilder: (context, index) {
          final result = results[index];
          return _buildResultCard(result);
        },
      ),
    );
  }

  Widget _buildResultCard(WeeklyResultModel result) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray200),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with date and actions
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: 20.w,
                color: AppColors.primary,
              ),
              SizedBox(width: 8.w),
              Text(
                DateFormat('dd/MM/yyyy').format(result.recordedDate),
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              const Spacer(),
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      _showEditResultDialog(result);
                      break;
                    case 'delete':
                      _deleteResult(result);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 20),
                        SizedBox(width: 8),
                        Text('تعديل'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 20, color: Colors.red),
                        SizedBox(width: 8),
                        Text('حذف', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 12.h),

          // Measurements grid
          _buildMeasurementsGrid(result),

          // Notes if available
          if (result.notes != null && result.notes!.isNotEmpty) ...[
            SizedBox(height: 12.h),
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: AppColors.gray100,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.note,
                    size: 16.w,
                    color: AppColors.textSecondary,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      result.notes!,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMeasurementsGrid(WeeklyResultModel result) {
    final measurements = <Map<String, dynamic>>[];

    if (result.weight != null) {
      measurements.add({
        'icon': Icons.monitor_weight,
        'label': 'الوزن',
        'value': '${result.weight!.toStringAsFixed(1)} كجم',
        'color': AppColors.primary,
      });
    }

    if (result.bodyFat != null) {
      measurements.add({
        'icon': Icons.fitness_center,
        'label': 'الدهون',
        'value': '${result.bodyFat!.toStringAsFixed(1)}%',
        'color': AppColors.warning,
      });
    }

    if (result.visceralFat != null) {
      measurements.add({
        'icon': Icons.health_and_safety,
        'label': 'الدهون الحشوية',
        'value': result.visceralFat!.toStringAsFixed(1),
        'color': AppColors.error,
      });
    }

    if (result.waterPercentage != null) {
      measurements.add({
        'icon': Icons.water_drop,
        'label': 'السوائل',
        'value': '${result.waterPercentage!.toStringAsFixed(1)}%',
        'color': AppColors.info,
      });
    }

    if (result.muscleMass != null) {
      measurements.add({
        'icon': Icons.sports_gymnastics,
        'label': 'العضلات',
        'value': '${result.muscleMass!.toStringAsFixed(1)} كجم',
        'color': AppColors.success,
      });
    }

    if (measurements.isEmpty) {
      return Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: AppColors.gray100,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Text(
          'لا توجد قياسات مسجلة',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColors.textSecondary,
          ),
        ),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3,
        crossAxisSpacing: 8.w,
        mainAxisSpacing: 8.h,
      ),
      itemCount: measurements.length,
      itemBuilder: (context, index) {
        final measurement = measurements[index];
        return Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: (measurement['color'] as Color).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Row(
            children: [
              Icon(
                measurement['icon'] as IconData,
                size: 16.w,
                color: measurement['color'] as Color,
              ),
              SizedBox(width: 6.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      measurement['label'] as String,
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      measurement['value'] as String,
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics,
            size: 64.w,
            color: AppColors.gray400,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد نتائج أسبوعية',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'ابدأ بإضافة أول نتيجة أسبوعية للمريض',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton.icon(
            onPressed: _showAddResultDialog,
            icon: const Icon(Icons.add),
            label: const Text('إضافة نتيجة أسبوعية'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.w,
            color: AppColors.error,
          ),
          SizedBox(height: 16.h),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.error,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _refreshResults,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics,
            size: 64.w,
            color: AppColors.gray400,
          ),
          SizedBox(height: 16.h),
          Text(
            'جاري تحميل النتائج الأسبوعية...',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _refreshResults,
            child: const Text('تحميل البيانات'),
          ),
        ],
      ),
    );
  }

  void _showAddResultDialog() {
    showDialog(
      context: context,
      builder: (context) => BlocProvider.value(
        value: context.read<WeeklyResultsBloc>(),
        child: WeeklyResultFormDialog(patientId: widget.patientId),
      ),
    );
  }

  void _showEditResultDialog(WeeklyResultModel result) {
    showDialog(
      context: context,
      builder: (context) => BlocProvider.value(
        value: context.read<WeeklyResultsBloc>(),
        child: WeeklyResultFormDialog(
          patientId: widget.patientId,
          weeklyResult: result,
        ),
      ),
    );
  }

  void _deleteResult(WeeklyResultModel result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف نتيجة تاريخ ${DateFormat('dd/MM/yyyy').format(result.recordedDate)}؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Show loading dialog immediately
              LoadingDialog.show(context, 'جاري حذف النتيجة...');
              context.read<WeeklyResultsBloc>().add(
                DeleteWeeklyResult(
                  resultId: result.id,
                  patientId: widget.patientId,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _refreshResults() {
    context.read<WeeklyResultsBloc>().add(
      RefreshWeeklyResults(patientId: widget.patientId),
    );
  }

  Future<void> _onRefresh() async {
    _refreshResults();
    // انتظار قصير لإظهار مؤشر التحديث
    await Future.delayed(const Duration(milliseconds: 500));
  }
}
