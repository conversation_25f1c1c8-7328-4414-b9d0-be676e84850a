import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/utils/debouncer.dart';
import '../../../../core/widgets/custom_search_bar.dart';
import '../../../../core/widgets/responsive_button.dart';
import '../bloc/patients_bloc.dart';
import '../bloc/patients_event.dart';
import '../bloc/patients_state.dart';
import '../widgets/patient_form_dialog.dart';
import '../widgets/patient_details_dialog.dart';
import 'medical_record_page.dart';

class PatientsPage extends StatefulWidget {
  final bool isVisible;
  final bool hasBeenVisited;

  const PatientsPage({
    super.key,
    this.isVisible = false,
    this.hasBeenVisited = false,
  });

  @override
  State<PatientsPage> createState() => _PatientsPageState();
}

class _PatientsPageState extends State<PatientsPage>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  final Debouncer _debouncer = Debouncer(milliseconds: 500);

  // Track if data has been loaded
  bool _dataLoaded = false;

  @override
  bool get wantKeepAlive => true; // Keep state alive

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    // Don't load patients automatically - wait for user to visit this page
  }

  @override
  void didUpdateWidget(PatientsPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Check if page became visible
    if (widget.isVisible && !oldWidget.isVisible) {
      _loadPatientsIfNeeded();
    }
  }

  void _loadPatientsIfNeeded() {
    // Only load if page is visible and data hasn't been loaded yet
    if (widget.isVisible && !_dataLoaded) {
      debugPrint('🔄 Loading patients data for first time...');
      context.read<PatientsBloc>().add(LoadAllPatients());
      _dataLoaded = true;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _debouncer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    // Load data only if page is currently visible
    if (widget.isVisible) {
      _loadPatientsIfNeeded();
    }

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(AppStrings.patients),
        backgroundColor: AppColors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
          tabs: const [
            Tab(text: AppStrings.premiumPatients),
            Tab(text: AppStrings.allPatients),
          ],
        ),
      ),
      body: BlocConsumer<PatientsBloc, PatientsState>(
        listener: (context, state) {
          if (state is PatientsError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          } else if (state is PatientCreated) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إضافة المريض بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is PatientUpdated) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم تحديث بيانات المريض بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is PatientDeleted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حذف المريض بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is PatientsLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is PatientsLoaded) {
            return TabBarView(
              controller: _tabController,
              children: [
                _buildPremiumPatientsView(state.premiumPatients),
                _buildAllPatientsView(state.patients),
              ],
            );
          }

          return const Center(child: Text('لا توجد بيانات'));
        },
      ),
    );
  }

  void _showAddPatientDialog() {
    showDialog(
      context: context,
      builder:
          (context) => BlocProvider.value(
            value: context.read<PatientsBloc>(),
            child: const PatientFormDialog(),
          ),
    );
  }

  Future<void> _onRefreshPatients() async {
    debugPrint('🔄 Refreshing patients data...');
    context.read<PatientsBloc>().add(LoadAllPatients());
    await Future.delayed(const Duration(milliseconds: 500));
  }

  Widget _buildPremiumPatientsView(List<PatientModel> patients) {
    return RefreshIndicator(
      onRefresh: _onRefreshPatients,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // Search Bar
            CustomSearchBar(
              hintText: 'البحث بالاسم أو رقم الهاتف أو الهوية...',
              controller: _searchController,
              onSearch: (query) {
                context.read<PatientsBloc>().add(SearchPatients(query: query));
              },
              onClear: () {
                context.read<PatientsBloc>().add(LoadAllPatients());
              },
            ),
            SizedBox(height: 16.h),

            // Premium Patients List
            Expanded(
              child:
                  patients.isEmpty
                      ? const Center(
                        child: Text(
                          'لا توجد مرضى مميزين',
                          style: TextStyle(
                            fontSize: 16,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      )
                      : ListView.builder(
                        itemCount: patients.length,
                        itemBuilder: (context, index) {
                          final patient = patients[index];
                          return Container(
                            margin: EdgeInsets.only(bottom: 12.h),
                            padding: EdgeInsets.all(16.w),
                            decoration: BoxDecoration(
                              color: AppColors.white,
                              borderRadius: BorderRadius.circular(12.r),
                              border: Border.all(
                                color: AppColors.primary.withValues(alpha: 0.3),
                                width: 1,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.gray300.withValues(
                                    alpha: 0.3,
                                  ),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: IntrinsicHeight(
                              // ✅ ارتفاع ديناميكي
                              child: Stack(
                                children: [
                                  // المحتوى الأساسي
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisSize:
                                        MainAxisSize.min, // ✅ حجم أدنى
                                    children: [
                                      Row(
                                        children: [
                                          CircleAvatar(
                                            radius: 24.r,
                                            backgroundColor: AppColors.primary
                                                .withValues(alpha: 0.1),
                                            child: Icon(
                                              Icons.person,
                                              color: AppColors.primary,
                                              size: 24.w,
                                            ),
                                          ),
                                          SizedBox(width: 12.w),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  children: [
                                                    Flexible(
                                                      child: Text(
                                                        patient.name,
                                                        style: TextStyle(
                                                          fontSize: 16.sp,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          color:
                                                              AppColors
                                                                  .textPrimary,
                                                        ),
                                                        overflow:
                                                            TextOverflow
                                                                .ellipsis,
                                                      ),
                                                    ),
                                                    SizedBox(width: 8.w),
                                                    Container(
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                            horizontal: 6.w,
                                                            vertical: 2.h,
                                                          ),
                                                      decoration: BoxDecoration(
                                                        color:
                                                            AppColors.primary,
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                              4.r,
                                                            ),
                                                      ),
                                                      child: Text(
                                                        'مميز',
                                                        style: TextStyle(
                                                          fontSize: 10.sp,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          color:
                                                              AppColors.white,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(height: 4.h),
                                                // ✅ عرض رقم الهاتف مع أيقونات الاتصال والنسخ
                                                if (patient.phone != null &&
                                                    patient.phone!.isNotEmpty)
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        Icons.phone,
                                                        size: 14.w,
                                                        color:
                                                            AppColors
                                                                .textSecondary,
                                                      ),
                                                      SizedBox(width: 4.w),
                                                      Expanded(
                                                        child: Text(
                                                          patient.phone!,
                                                          style: TextStyle(
                                                            fontSize: 13.sp,
                                                            color:
                                                                AppColors
                                                                    .textSecondary,
                                                          ),
                                                        ),
                                                      ),
                                                      SizedBox(width: 8.w),
                                                      // Call button
                                                      InkWell(
                                                        onTap:
                                                            () =>
                                                                _makePhoneCall(
                                                                  patient
                                                                      .phone!,
                                                                ),
                                                        child: Container(
                                                          padding:
                                                              EdgeInsets.all(
                                                                4.w,
                                                              ),
                                                          decoration: BoxDecoration(
                                                            color: AppColors
                                                                .primary
                                                                .withValues(
                                                                  alpha: 0.1,
                                                                ),
                                                            borderRadius:
                                                                BorderRadius.circular(
                                                                  4.r,
                                                                ),
                                                          ),
                                                          child: Icon(
                                                            Icons.phone,
                                                            size: 14.w,
                                                            color:
                                                                AppColors
                                                                    .primary,
                                                          ),
                                                        ),
                                                      ),
                                                      SizedBox(width: 4.w),
                                                      // Copy button
                                                      InkWell(
                                                        onTap:
                                                            () =>
                                                                _copyToClipboard(
                                                                  patient
                                                                      .phone!,
                                                                ),
                                                        child: Container(
                                                          padding:
                                                              EdgeInsets.all(
                                                                4.w,
                                                              ),
                                                          decoration: BoxDecoration(
                                                            color: AppColors
                                                                .primary
                                                                .withValues(
                                                                  alpha: 0.1,
                                                                ),
                                                            borderRadius:
                                                                BorderRadius.circular(
                                                                  4.r,
                                                                ),
                                                          ),
                                                          child: Icon(
                                                            Icons.copy,
                                                            size: 14.w,
                                                            color:
                                                                AppColors
                                                                    .primary,
                                                          ),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                SizedBox(height: 2.h),
                                                // ✅ عرض رقم الهوية
                                                Row(
                                                  children: [
                                                    Icon(
                                                      Icons.badge,
                                                      size: 14.w,
                                                      color:
                                                          AppColors
                                                              .textSecondary,
                                                    ),
                                                    SizedBox(width: 4.w),
                                                    Text(
                                                      'ID: ${patient.id}',
                                                      style: TextStyle(
                                                        fontSize: 13.sp,
                                                        color:
                                                            AppColors
                                                                .textSecondary,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(height: 2.h),
                                                Text(
                                                  'العمر: ${patient.age} سنة${patient.gender != null ? ' | ${patient.gender}' : ''}',
                                                  style: TextStyle(
                                                    fontSize: 13.sp,
                                                    color:
                                                        AppColors.textSecondary,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          SizedBox(
                                            width: 30.w,
                                          ), // مساحة لأيقونة 3 نقاط
                                        ],
                                      ),
                                      SizedBox(height: 12.h),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: ResponsiveButton(
                                              onPressed: () {
                                                _navigateToMedicalRecord(
                                                  patient,
                                                );
                                              },
                                              text: 'السجل الطبي',
                                              icon: Icons.medical_information,
                                              isOutlined: true,
                                              showLoadingState: false,
                                            ),
                                          ),
                                          SizedBox(width: 8.w),
                                          Expanded(
                                            child: BookAppointmentButton(
                                              onPressed: () {
                                                _navigateToAppointmentBooking(
                                                  patient,
                                                );
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  // ✅ أيقونة 3 نقاط في الزاوية العلوية اليسرى
                                  Positioned(
                                    top: 0,
                                    left: 0,
                                    child: PopupMenuButton<String>(
                                      onSelected: (value) {
                                        if (value == 'downgrade') {
                                          _showDowngradeConfirmation(patient);
                                        }
                                      },
                                      itemBuilder:
                                          (context) => [
                                            const PopupMenuItem<String>(
                                              value: 'downgrade',
                                              child: Row(
                                                children: [
                                                  Icon(
                                                    Icons.star_border,
                                                    color: AppColors.error,
                                                  ),
                                                  SizedBox(width: 8),
                                                  Text('إلغاء العضوية المميزة'),
                                                ],
                                              ),
                                            ),
                                          ],
                                      child: Container(
                                        padding: EdgeInsets.all(4.w),
                                        child: Icon(
                                          Icons.more_vert,
                                          color: AppColors.textSecondary,
                                          size: 20.w,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAllPatientsView(List<PatientModel> patients) {
    return RefreshIndicator(
      onRefresh: _onRefreshPatients,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // Search Bar
            CustomSearchBar(
              hintText: 'البحث بالاسم أو رقم الهاتف أو الهوية...',
              onSearch: (query) {
                context.read<PatientsBloc>().add(SearchPatients(query: query));
              },
              onClear: () {
                context.read<PatientsBloc>().add(LoadAllPatients());
              },
            ),
            SizedBox(height: 16.h),

            // All Patients List
            Expanded(
              child:
                  patients.isEmpty
                      ? const Center(
                        child: Text(
                          'لا توجد مرضى',
                          style: TextStyle(
                            fontSize: 16,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      )
                      : ListView.builder(
                        itemCount: patients.length,
                        itemBuilder: (context, index) {
                          final patient = patients[index];
                          final isPremium = patient.isPremium;
                          return GestureDetector(
                            onTap: () => _showPatientDetailsDialog(patient),
                            child: Container(
                              margin: EdgeInsets.only(bottom: 12.h),
                              padding: EdgeInsets.all(16.w),
                              decoration: BoxDecoration(
                                color: AppColors.white,
                                borderRadius: BorderRadius.circular(12.r),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.gray300.withValues(
                                      alpha: 0.3,
                                    ),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Row(
                                children: [
                                  CircleAvatar(
                                    radius: 20.r,
                                    backgroundColor:
                                        isPremium
                                            ? AppColors.primary.withValues(
                                              alpha: 0.1,
                                            )
                                            : AppColors.gray200,
                                    child: Icon(
                                      Icons.person,
                                      color:
                                          isPremium
                                              ? AppColors.primary
                                              : AppColors.gray500,
                                      size: 20.w,
                                    ),
                                  ),
                                  SizedBox(width: 12.w),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            Flexible(
                                              child: Text(
                                                patient.name,
                                                style: TextStyle(
                                                  fontSize: 16.sp,
                                                  fontWeight: FontWeight.w600,
                                                  color: AppColors.textPrimary,
                                                ),
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                            if (isPremium) ...[
                                              SizedBox(width: 8.w),
                                              Container(
                                                padding: EdgeInsets.symmetric(
                                                  horizontal: 6.w,
                                                  vertical: 2.h,
                                                ),
                                                decoration: BoxDecoration(
                                                  color: AppColors.primary,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                        4.r,
                                                      ),
                                                ),
                                                child: Text(
                                                  'مميز',
                                                  style: TextStyle(
                                                    fontSize: 10.sp,
                                                    fontWeight: FontWeight.w600,
                                                    color: AppColors.white,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ],
                                        ),
                                        SizedBox(height: 2.h),
                                        // ✅ عرض رقم الهاتف مع أيقونات الاتصال والنسخ
                                        if (patient.phone != null &&
                                            patient.phone!.isNotEmpty)
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.phone,
                                                size: 14.w,
                                                color: AppColors.textSecondary,
                                              ),
                                              SizedBox(width: 4.w),
                                              Expanded(
                                                child: Text(
                                                  patient.phone!,
                                                  style: TextStyle(
                                                    fontSize: 13.sp,
                                                    color:
                                                        AppColors.textSecondary,
                                                  ),
                                                ),
                                              ),
                                              SizedBox(width: 8.w),
                                              // Call button
                                              InkWell(
                                                onTap:
                                                    () => _makePhoneCall(
                                                      patient.phone!,
                                                    ),
                                                child: Container(
                                                  padding: EdgeInsets.all(4.w),
                                                  decoration: BoxDecoration(
                                                    color: AppColors.primary
                                                        .withValues(alpha: 0.1),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          4.r,
                                                        ),
                                                  ),
                                                  child: Icon(
                                                    Icons.phone,
                                                    size: 14.w,
                                                    color: AppColors.primary,
                                                  ),
                                                ),
                                              ),
                                              SizedBox(width: 4.w),
                                              // Copy button
                                              InkWell(
                                                onTap:
                                                    () => _copyToClipboard(
                                                      patient.phone!,
                                                    ),
                                                child: Container(
                                                  padding: EdgeInsets.all(4.w),
                                                  decoration: BoxDecoration(
                                                    color: AppColors.primary
                                                        .withValues(alpha: 0.1),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                          4.r,
                                                        ),
                                                  ),
                                                  child: Icon(
                                                    Icons.copy,
                                                    size: 14.w,
                                                    color: AppColors.primary,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        SizedBox(height: 2.h),
                                        // ✅ عرض رقم الهوية
                                        Row(
                                          children: [
                                            Icon(
                                              Icons.badge,
                                              size: 14.w,
                                              color: AppColors.textSecondary,
                                            ),
                                            SizedBox(width: 4.w),
                                            Text(
                                              'ID: ${patient.id}',
                                              style: TextStyle(
                                                fontSize: 13.sp,
                                                color: AppColors.textSecondary,
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: 2.h),
                                        Text(
                                          'العمر: ${patient.age} سنة | ${patient.gender}',
                                          style: TextStyle(
                                            fontSize: 13.sp,
                                            color: AppColors.textSecondary,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  if (!isPremium)
                                    SizedBox(
                                      height: 28.h,
                                      child: TextButton(
                                        onPressed: () {
                                          context.read<PatientsBloc>().add(
                                            UpgradeToPremium(
                                              patientId: patient.id,
                                            ),
                                          );
                                        },
                                        style: TextButton.styleFrom(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: 8.w,
                                            vertical: 0,
                                          ),
                                        ),
                                        child: Text(
                                          AppStrings.upgradeToPremium,
                                          style: TextStyle(fontSize: 12.sp),
                                        ),
                                      ),
                                    ),
                                  // ✅ حذف PopupMenuButton (أيقونة 3 نقاط)
                                ],
                              ),
                            ),
                          );
                        },
                      ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToMedicalRecord(PatientModel patient) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MedicalRecordPage(patient: patient),
      ),
    );
  }

  void _navigateToAppointmentBooking(PatientModel patient) {
    // الانتقال مباشرة لصفحة السجل الطبي مع فتح تاب حجز الاستشارة
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => MedicalRecordPage(
              patient: patient,
              initialTabIndex: 5, // تاب حجز الاستشارة (آخر تاب)
            ),
      ),
    );
  }

  // دالة الاتصال
  void _makePhoneCall(String phoneNumber) async {
    try {
      // تنظيف رقم الهاتف من المسافات والرموز الخاصة
      final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

      // استخدام tel: scheme مع DIAL mode (أكثر أماناً)
      final Uri phoneUri = Uri.parse('tel:$cleanNumber');

      debugPrint('🔄 Attempting to call: $cleanNumber');
      debugPrint('📞 URI: $phoneUri');

      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(
          phoneUri,
          mode: LaunchMode.externalApplication, // فتح في تطبيق خارجي
        );
        debugPrint('✅ Phone call initiated successfully');
      } else {
        debugPrint('❌ Cannot launch phone app');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا يمكن فتح تطبيق الهاتف'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ Error making phone call: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الاتصال: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  // دالة نسخ النص
  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نسخ رقم الهاتف'),
          backgroundColor: AppColors.success,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  // دالة إظهار تفاصيل المريض
  void _showPatientDetailsDialog(PatientModel patient) {
    showDialog(
      context: context,
      builder: (context) => PatientDetailsDialog(patient: patient),
    );
  }

  // دالة تأكيد إلغاء العضوية المميزة
  void _showDowngradeConfirmation(PatientModel patient) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد إلغاء العضوية المميزة'),
            content: Text(
              'هل أنت متأكد من إلغاء العضوية المميزة للمريض "${patient.name}"؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.read<PatientsBloc>().add(
                    DowngradeFromPremium(patientId: patient.id),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                ),
                child: const Text('تأكيد'),
              ),
            ],
          ),
    );
  }
}
