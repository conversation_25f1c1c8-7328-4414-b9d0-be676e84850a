{"version": 3, "names": ["formatters", "require", "_builder", "smart", "exports", "createTemplateBuilder", "statement", "statements", "expression", "program", "_default", "default", "Object", "assign", "bind", "undefined", "ast"], "sources": ["../src/index.ts"], "sourcesContent": ["import * as formatters from \"./formatters.ts\";\nimport createTemplateBuilder from \"./builder.ts\";\n\nexport const smart = createTemplateBuilder(formatters.smart);\nexport const statement = createTemplateBuilder(formatters.statement);\nexport const statements = createTemplateBuilder(formatters.statements);\nexport const expression = createTemplateBuilder(formatters.expression);\nexport const program = createTemplateBuilder(formatters.program);\n\ntype DefaultTemplateBuilder = typeof smart & {\n  smart: typeof smart;\n  statement: typeof statement;\n  statements: typeof statements;\n  expression: typeof expression;\n  program: typeof program;\n};\n\nexport default Object.assign(smart.bind(undefined) as DefaultTemplateBuilder, {\n  smart,\n  statement,\n  statements,\n  expression,\n  program,\n  ast: smart.ast,\n});\n\nexport type {\n  PublicOpts as Options,\n  PublicReplacements as Replacements,\n} from \"./options.ts\";\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAEO,MAAME,KAAK,GAAAC,OAAA,CAAAD,KAAA,GAAG,IAAAE,gBAAqB,EAACL,UAAU,CAACG,KAAK,CAAC;AACrD,MAAMG,SAAS,GAAAF,OAAA,CAAAE,SAAA,GAAG,IAAAD,gBAAqB,EAACL,UAAU,CAACM,SAAS,CAAC;AAC7D,MAAMC,UAAU,GAAAH,OAAA,CAAAG,UAAA,GAAG,IAAAF,gBAAqB,EAACL,UAAU,CAACO,UAAU,CAAC;AAC/D,MAAMC,UAAU,GAAAJ,OAAA,CAAAI,UAAA,GAAG,IAAAH,gBAAqB,EAACL,UAAU,CAACQ,UAAU,CAAC;AAC/D,MAAMC,OAAO,GAAAL,OAAA,CAAAK,OAAA,GAAG,IAAAJ,gBAAqB,EAACL,UAAU,CAACS,OAAO,CAAC;AAAC,IAAAC,QAAA,GAAAN,OAAA,CAAAO,OAAA,GAUlDC,MAAM,CAACC,MAAM,CAACV,KAAK,CAACW,IAAI,CAACC,SAAS,CAAC,EAA4B;EAC5EZ,KAAK;EACLG,SAAS;EACTC,UAAU;EACVC,UAAU;EACVC,OAAO;EACPO,GAAG,EAAEb,KAAK,CAACa;AACb,CAAC,CAAC", "ignoreList": []}