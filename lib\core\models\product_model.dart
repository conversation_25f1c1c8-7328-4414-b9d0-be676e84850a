import 'package:equatable/equatable.dart';
import 'category_model.dart';
import 'product_image_model.dart';

class ProductModel extends Equatable {
  final String id;
  final String name;
  final String description;
  final String productCode;
  final double price;
  final double discountPercentage;
  final int stock;
  final String categoryId;
  final CategoryModel? categoryModel;
  final List<ProductImageModel> images;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ProductModel({
    required this.id,
    required this.name,
    required this.description,
    required this.productCode,
    required this.price,
    this.discountPercentage = 0.0,
    required this.stock,
    required this.categoryId,
    this.categoryModel,
    this.images = const [],
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    try {
      print('🔄 ProductModel.fromJson: Starting to parse product data');
      print('📋 Raw JSON: $json');

      // Parse images if available
      List<ProductImageModel> imagesList = [];
      try {
        if (json['images'] != null) {
          print('📷 Processing images from "images" field');
          imagesList = (json['images'] as List)
              .map((img) => ProductImageModel.fromJson(img))
              .toList();
        } else if (json['product_images'] != null) {
          print('📷 Processing images from "product_images" field');
          // Fallback for old structure
          imagesList = (json['product_images'] as List)
              .map((img) => ProductImageModel.fromJson(img))
              .toList();
        }
        print('✅ Images processed: ${imagesList.length} images');
      } catch (e) {
        print('❌ Error processing images: $e');
        imagesList = [];
      }

      // Parse category if available
      CategoryModel? categoryModel;
      try {
        if (json['categories'] != null) {
          print('🏷️ Processing category data');
          categoryModel = CategoryModel.fromJson(json['categories']);
          print('✅ Category processed: ${categoryModel.name}');
        }
      } catch (e) {
        print('❌ Error processing category: $e');
        categoryModel = null;
      }

      print('🔄 Processing individual fields...');

      final id = json['id']?.toString() ?? '';
      print('✅ ID: $id');

      final name = json['name']?.toString() ?? 'غير محدد';
      print('✅ Name: $name');

      final description = json['description']?.toString() ?? 'غير محدد';
      print('✅ Description: $description');

      final productCode = json['product_code']?.toString() ?? 'PROD-0000';
      print('✅ Product Code: $productCode');

      final price = (json['price'] as num?)?.toDouble() ?? 0.0;
      print('✅ Price: $price');

      final discountPercentage = (json['discount_percentage'] as num?)?.toDouble() ?? 0.0;
      print('✅ Discount Percentage: $discountPercentage');

      final stock = json['stock'] as int? ?? 0;
      print('✅ Stock: $stock');

      final categoryId = json['category_id']?.toString() ?? '';
      print('✅ Category ID: $categoryId');

      final isActive = json['is_active'] as bool? ?? true;
      print('✅ Is Active: $isActive');

      final createdAtStr = json['created_at']?.toString() ?? DateTime.now().toIso8601String();
      print('✅ Created At String: $createdAtStr');
      final createdAt = DateTime.parse(createdAtStr);
      print('✅ Created At Parsed: $createdAt');

      final updatedAtStr = json['updated_at']?.toString() ?? DateTime.now().toIso8601String();
      print('✅ Updated At String: $updatedAtStr');
      final updatedAt = DateTime.parse(updatedAtStr);
      print('✅ Updated At Parsed: $updatedAt');

      final product = ProductModel(
        id: id,
        name: name,
        description: description,
        productCode: productCode,
        price: price,
        discountPercentage: discountPercentage,
        stock: stock,
        categoryId: categoryId,
        categoryModel: categoryModel,
        images: imagesList,
        isActive: isActive,
        createdAt: createdAt,
        updatedAt: updatedAt,
      );

      print('✅ ProductModel created successfully: ${product.name}');
      return product;
    } catch (e, stackTrace) {
      print('❌ ProductModel.fromJson: Error parsing product');
      print('💥 Error: $e');
      print('📍 Stack trace: $stackTrace');
      print('📋 JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'name': name,
      'description': description,
      'product_code': productCode,
      'price': price,
      'discount_percentage': discountPercentage,
      'stock': stock,
      'category_id': categoryId,
      'images': images.map((img) => img.toJson()).toList(),
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };

    // Only include id if it's not empty (for updates)
    if (id.isNotEmpty) {
      json['id'] = id;
    }

    return json;
  }

  ProductModel copyWith({
    String? id,
    String? name,
    String? description,
    String? productCode,
    double? price,
    double? discountPercentage,
    int? stock,
    String? categoryId,
    CategoryModel? categoryModel,
    List<ProductImageModel>? images,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ProductModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      productCode: productCode ?? this.productCode,
      price: price ?? this.price,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      stock: stock ?? this.stock,
      categoryId: categoryId ?? this.categoryId,
      categoryModel: categoryModel ?? this.categoryModel,
      images: images ?? this.images,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  String get primaryImageUrl {
    if (images.isNotEmpty) {
      final primaryImage = images.firstWhere(
        (img) => img.isPrimary,
        orElse: () => images.first,
      );
      return primaryImage.imageUrl;
    }
    return '';
  }

  String get categoryName {
    return categoryModel?.name ?? 'غير محدد';
  }

  // Calculate discounted price
  double get discountedPrice {
    if (discountPercentage > 0) {
      return price * (1 - discountPercentage / 100);
    }
    return price;
  }

  // Check if product has discount
  bool get hasDiscount {
    return discountPercentage > 0;
  }

  // Get discount amount
  double get discountAmount {
    return price - discountedPrice;
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        productCode,
        price,
        discountPercentage,
        stock,
        categoryId,
        categoryModel,
        images,
        isActive,
        createdAt,
        updatedAt,
      ];
}
