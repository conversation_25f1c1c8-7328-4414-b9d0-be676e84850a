import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'firebase_messaging_service.dart';

class NotificationHttpService {
  static final _supabase = Supabase.instance.client;

  // Process due notifications (called by Supabase Edge Function)
  static Future<Map<String, dynamic>> processDueNotifications() async {
    try {
      print('🔍 Processing due notifications...');
      
      final now = DateTime.now();
      final currentTime = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
      final currentDayOfWeek = now.weekday; // 1 = Monday, 7 = Sunday
      
      print('Current time: $currentTime, Day: $currentDayOfWeek');
      
      // Get due notifications
      final response = await _supabase
          .from('scheduled_notifications')
          .select('*')
          .eq('is_active', true)
          .eq('scheduled_time', currentTime);
      
      final notifications = response as List;
      
      // Filter by day of week
      final dueNotifications = notifications.where((notification) {
        final daysOfWeek = List<int>.from(notification['days_of_week']);
        return daysOfWeek.contains(currentDayOfWeek);
      }).toList();
      
      print('📊 Found ${dueNotifications.length} due notifications');
      
      int successCount = 0;
      int failedCount = 0;
      
      // Process each notification
      for (final notification in dueNotifications) {
        try {
          final success = await _processNotification(notification);
          if (success) {
            successCount++;
          } else {
            failedCount++;
          }
        } catch (e) {
          print('❌ Error processing notification ${notification['id']}: $e');
          failedCount++;
        }
      }
      
      final result = {
        'success': true,
        'processed': dueNotifications.length,
        'successful': successCount,
        'failed': failedCount,
        'time': currentTime,
        'day': currentDayOfWeek,
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      print('✅ Processed ${dueNotifications.length} notifications: $successCount successful, $failedCount failed');
      
      return result;
      
    } catch (e) {
      print('❌ Error in processDueNotifications: $e');
      return {
        'success': false,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
  
  // Process a single notification
  static Future<bool> _processNotification(Map<String, dynamic> notification) async {
    try {
      final patientId = notification['patient_id'];
      
      // Get patient info
      final patientResponse = await _supabase
          .from('patients')
          .select('auth_id, name')
          .eq('id', patientId)
          .single();
      
      final patient = patientResponse;
      
      if (patient == null) {
        print('❌ Patient not found: $patientId');
        return false;
      }
      
      // Get FCM tokens
      final tokensResponse = await _supabase
          .from('user_fcm_tokens')
          .select('fcm_token')
          .eq('user_id', patient['auth_id'])
          .eq('is_active', true);
      
      final tokens = tokensResponse as List;
      
      if (tokens.isEmpty) {
        print('❌ No FCM tokens found for patient: $patientId');
        return false;
      }
      
      print('📱 Found ${tokens.length} FCM token(s) for patient: ${patient['name']}');
      
      // Prepare notification content
      final title = _getNotificationTitle(notification['notification_type']);
      final body = 'مرحباً ${patient['name']}، ${notification['body']}';
      
      // Send to all tokens
      bool allSuccess = true;
      for (final tokenData in tokens) {
        try {
          final success = await FirebaseMessagingService.sendPersonalizedNotification(
            fcmToken: tokenData['fcm_token'],
            patientName: patient['name'],
            notificationType: notification['notification_type'],
            content: notification['body'],
            additionalData: {
              'scheduled_notification_id': notification['id'],
              'patient_id': patientId,
              'reminder_id': notification['reminder_id'] ?? '',
            },
          );
          
          // Log notification
          await _supabase.from('notification_logs').insert({
            'scheduled_notification_id': notification['id'],
            'patient_id': patientId,
            'fcm_token': tokenData['fcm_token'],
            'title': title,
            'body': body,
            'status': success ? 'sent' : 'failed',
            'firebase_response': success 
                ? {'messageId': 'fcm-${DateTime.now().millisecondsSinceEpoch}', 'success': true}
                : {'error': 'FCM send failed'},
          });
          
          if (success) {
            print('✅ Notification sent successfully to ${patient['name']}');
          } else {
            print('❌ Failed to send notification to ${patient['name']}');
            allSuccess = false;
          }
          
        } catch (e) {
          print('❌ Error sending to token: $e');
          allSuccess = false;
          
          // Log error
          await _supabase.from('notification_logs').insert({
            'scheduled_notification_id': notification['id'],
            'patient_id': patientId,
            'fcm_token': tokenData['fcm_token'],
            'title': title,
            'body': body,
            'status': 'failed',
            'error_message': e.toString(),
          });
        }
      }
      
      return allSuccess;
      
    } catch (e) {
      print('❌ Error processing notification: $e');
      return false;
    }
  }
  
  // Helper method to get notification title
  static String _getNotificationTitle(String notificationType) {
    switch (notificationType) {
      case 'meal':
        return '🍽️ تذكير الوجبة';
      case 'exercise':
        return '🏃‍♂️ تذكير النشاط البدني';
      case 'medication':
        return '💊 تذكير الدواء';
      case 'water':
        return '💧 تذكير شرب الماء';
      default:
        return '🔔 تذكير من Diet Rx';
    }
  }
  
  // Create HTTP server endpoint (for testing)
  static Future<void> startHttpServer({int port = 8080}) async {
    // This would require a proper HTTP server implementation
    // For now, we'll use the existing notification automation service
    print('🚀 Notification HTTP service ready');
  }
}
