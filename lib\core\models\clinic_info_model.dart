import 'package:equatable/equatable.dart';

class ClinicInfoModel extends Equatable {
  final String id;
  final String infoType;
  final String infoKey;
  final String infoValue;
  final String displayName;
  final String? iconName;
  final bool isActive;
  final int displayOrder;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ClinicInfoModel({
    required this.id,
    required this.infoType,
    required this.infoKey,
    required this.infoValue,
    required this.displayName,
    this.iconName,
    required this.isActive,
    required this.displayOrder,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ClinicInfoModel.fromJson(Map<String, dynamic> json) {
    return ClinicInfoModel(
      id: json['id'] as String,
      infoType: json['info_type'] as String,
      infoKey: json['info_key'] as String,
      infoValue: json['info_value'] as String,
      displayName: json['display_name'] as String,
      iconName: json['icon_name'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      displayOrder: json['display_order'] as int? ?? 0,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'info_type': infoType,
      'info_key': infoKey,
      'info_value': infoValue,
      'display_name': displayName,
      'icon_name': iconName,
      'is_active': isActive,
      'display_order': displayOrder,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };

    // Only include ID if it's not empty (for updates)
    if (id.isNotEmpty) {
      json['id'] = id;
    }

    return json;
  }

  ClinicInfoModel copyWith({
    String? id,
    String? infoType,
    String? infoKey,
    String? infoValue,
    String? displayName,
    String? iconName,
    bool? isActive,
    int? displayOrder,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ClinicInfoModel(
      id: id ?? this.id,
      infoType: infoType ?? this.infoType,
      infoKey: infoKey ?? this.infoKey,
      infoValue: infoValue ?? this.infoValue,
      displayName: displayName ?? this.displayName,
      iconName: iconName ?? this.iconName,
      isActive: isActive ?? this.isActive,
      displayOrder: displayOrder ?? this.displayOrder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  bool get isPhone => infoType == 'phone';
  bool get isEmail => infoType == 'email';
  bool get isSocialMedia => infoType == 'social_media';
  bool get isAddress => infoType == 'address';
  bool get isWorkingHours => infoType == 'working_hours';
  bool get isWebsite => infoType == 'website';

  // Check if the info value is a URL
  bool get isUrl => infoValue.startsWith('http://') || infoValue.startsWith('https://');

  // Check if the info value is a phone number
  bool get isPhoneNumber => infoValue.startsWith('01') || infoValue.startsWith('+2');

  // Check if the info value is an email
  bool get isEmailAddress => infoValue.contains('@');

  @override
  List<Object?> get props => [
        id,
        infoType,
        infoKey,
        infoValue,
        displayName,
        iconName,
        isActive,
        displayOrder,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'ClinicInfoModel(id: $id, infoType: $infoType, infoKey: $infoKey, '
        'infoValue: $infoValue, displayName: $displayName, iconName: $iconName, '
        'isActive: $isActive, displayOrder: $displayOrder)';
  }
}
