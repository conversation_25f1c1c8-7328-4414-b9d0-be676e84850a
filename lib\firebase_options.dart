// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAubb-XUMcRags2xFXISdpJZJw3LKM8q58',
    appId: '1:306693328328:web:18a642dd37fdce381e84fd',
    messagingSenderId: '306693328328',
    projectId: 'deit-rx-30741',
    authDomain: 'deit-rx-30741.firebaseapp.com',
    storageBucket: 'deit-rx-30741.firebasestorage.app',
    measurementId: 'G-7607D0KGDM',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAxW_eNy8EXp662tm_4F6yF_eRptUcBn18',
    appId: '1:306693328328:android:c8a6fde22f031ec71e84fd',
    messagingSenderId: '306693328328',
    projectId: 'deit-rx-30741',
    storageBucket: 'deit-rx-30741.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAiptA_aks2TkJK1Z7In2zwtwK_2Xy9HGo',
    appId: '1:306693328328:ios:a7012996aa0fd7a31e84fd',
    messagingSenderId: '306693328328',
    projectId: 'deit-rx-30741',
    storageBucket: 'deit-rx-30741.firebasestorage.app',
    iosBundleId: 'com.deitrxoneadmin',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAiptA_aks2TkJK1Z7In2zwtwK_2Xy9HGo',
    appId: '1:306693328328:ios:a7012996aa0fd7a31e84fd',
    messagingSenderId: '306693328328',
    projectId: 'deit-rx-30741',
    storageBucket: 'deit-rx-30741.firebasestorage.app',
    iosBundleId: 'com.deitrxoneadmin',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAubb-XUMcRags2xFXISdpJZJw3LKM8q58',
    appId: '1:306693328328:web:20175034b885adb61e84fd',
    messagingSenderId: '306693328328',
    projectId: 'deit-rx-30741',
    authDomain: 'deit-rx-30741.firebaseapp.com',
    storageBucket: 'deit-rx-30741.firebasestorage.app',
    measurementId: 'G-094RG9TQ3X',
  );
}
