import 'package:equatable/equatable.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import '../../../../core/models/article_model.dart';

abstract class ArticlesEvent extends Equatable {
  const ArticlesEvent();

  @override
  List<Object?> get props => [];
}

class LoadAllArticles extends ArticlesEvent {}

class LoadPublishedArticles extends ArticlesEvent {}

class LoadDraftArticles extends ArticlesEvent {}

class LoadArticlesByCategory extends ArticlesEvent {
  final String category;

  const LoadArticlesByCategory({required this.category});

  @override
  List<Object?> get props => [category];
}

class LoadArticlesByCategoryId extends ArticlesEvent {
  final String categoryId;

  const LoadArticlesByCategoryId({required this.categoryId});

  @override
  List<Object?> get props => [categoryId];
}

class SearchArticles extends ArticlesEvent {
  final String query;

  const SearchArticles({required this.query});

  @override
  List<Object?> get props => [query];
}

class CreateArticle extends ArticlesEvent {
  final ArticleModel article;

  const CreateArticle({required this.article});

  @override
  List<Object?> get props => [article];
}

class UpdateArticle extends ArticlesEvent {
  final ArticleModel article;

  const UpdateArticle({required this.article});

  @override
  List<Object?> get props => [article];
}

class DeleteArticle extends ArticlesEvent {
  final String articleId;

  const DeleteArticle({required this.articleId});

  @override
  List<Object?> get props => [articleId];
}

class PublishArticle extends ArticlesEvent {
  final String articleId;

  const PublishArticle({required this.articleId});

  @override
  List<Object?> get props => [articleId];
}

class UnpublishArticle extends ArticlesEvent {
  final String articleId;

  const UnpublishArticle({required this.articleId});

  @override
  List<Object?> get props => [articleId];
}

class RefreshArticles extends ArticlesEvent {}

class LoadFeaturedArticles extends ArticlesEvent {}

class CreateArticleWithImage extends ArticlesEvent {
  final ArticleModel article;
  final XFile? image;

  const CreateArticleWithImage({required this.article, this.image});

  @override
  List<Object?> get props => [article, image];
}

class UploadArticleImage extends ArticlesEvent {
  final String articleId;
  final XFile image;

  const UploadArticleImage({required this.articleId, required this.image});

  @override
  List<Object?> get props => [articleId, image];
}

class UpdateArticleWithImage extends ArticlesEvent {
  final ArticleModel article;
  final XFile? image;
  final bool deleteOldImage;
  final String? oldImageUrl;

  const UpdateArticleWithImage({
    required this.article,
    this.image,
    this.deleteOldImage = false,
    this.oldImageUrl,
  });

  @override
  List<Object?> get props => [article, image, deleteOldImage, oldImageUrl];
}

class CreateArticleWithFiles extends ArticlesEvent {
  final ArticleModel article;
  final XFile? image;
  final PlatformFile? pdf;

  const CreateArticleWithFiles({required this.article, this.image, this.pdf});

  @override
  List<Object?> get props => [article, image, pdf];
}

class UpdateArticleWithFiles extends ArticlesEvent {
  final ArticleModel article;
  final XFile? image;
  final PlatformFile? pdf;
  final bool deleteOldImage;
  final String? oldImageUrl;
  final bool deleteOldPdf;
  final String? oldPdfUrl;

  const UpdateArticleWithFiles({
    required this.article,
    this.image,
    this.pdf,
    this.deleteOldImage = false,
    this.oldImageUrl,
    this.deleteOldPdf = false,
    this.oldPdfUrl,
  });

  @override
  List<Object?> get props => [
    article,
    image,
    pdf,
    deleteOldImage,
    oldImageUrl,
    deleteOldPdf,
    oldPdfUrl,
  ];
}
