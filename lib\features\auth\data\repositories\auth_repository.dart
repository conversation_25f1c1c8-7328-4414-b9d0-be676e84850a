import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/admin_model.dart';

class AuthRepository {
  Future<AdminModel?> login(String email, String password) async {
    try {
      debugPrint('🔄 AuthRepository: Starting login process...');
      debugPrint('📧 AuthRepository: Email: $email');

      // تسجيل الدخول في Supabase Authentication أولاً
      debugPrint('🔐 AuthRepository: Authenticating with Supabase...');
      final authResponse = await SupabaseConfig.client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      debugPrint('📥 AuthRepository: Auth response received');
      debugPrint('🆔 AuthRepository: Auth User ID: ${authResponse.user?.id}');

      if (authResponse.user == null) {
        debugPrint('❌ AuthRepository: Auth user is null');
        throw Exception('بيانات الدخول غير صحيحة');
      }

      // جلب بيانات الأدمن من جدول الأدمن باستخدام auth_id
      debugPrint('💾 AuthRepository: Fetching admin data from database...');
      final response = await SupabaseConfig.admins
          .select()
          .eq('auth_id', authResponse.user!.id)
          .single();

      debugPrint('📥 AuthRepository: Database response: $response');

      final admin = AdminModel.fromJson(response);
      debugPrint('✅ AuthRepository: Login completed successfully');
      debugPrint('🎉 AuthRepository: Admin logged in with custom ID: ${admin.id}');

      return admin;
    } catch (e, stackTrace) {
      debugPrint('❌ AuthRepository: Login failed');
      debugPrint('💥 AuthRepository: Error: $e');
      debugPrint('📍 AuthRepository: Stack trace: $stackTrace');
      throw Exception('فشل في تسجيل الدخول: ${e.toString()}');
    }
  }

  Future<AdminModel> register(String name, String email, String password) async {
    try {
      debugPrint('🔄 AuthRepository: Starting registration process...');
      debugPrint('📧 AuthRepository: Email: $email');
      debugPrint('👤 AuthRepository: Name: $name');

      // إنشاء حساب في Supabase Authentication أولاً
      debugPrint('🔐 AuthRepository: Creating auth account...');
      final authResponse = await SupabaseConfig.client.auth.signUp(
        email: email,
        password: password,
      );

      debugPrint('📥 AuthRepository: Auth response received');
      debugPrint('🆔 AuthRepository: Auth User ID: ${authResponse.user?.id}');
      debugPrint('📧 AuthRepository: User Email: ${authResponse.user?.email}');

      if (authResponse.user == null) {
        debugPrint('❌ AuthRepository: Auth user is null');
        throw Exception('فشل في إنشاء حساب المصادقة');
      }

      // إنشاء ID مخصص بطول 10 أحرف
      final customId = _generateCustomId();
      debugPrint('🆔 AuthRepository: Generated custom ID: $customId');

      final hashedPassword = _hashPassword(password);
      debugPrint('🔒 AuthRepository: Password hashed successfully');

      // إضافة بيانات الأدمن لجدول الأدمن
      debugPrint('💾 AuthRepository: Inserting admin data to database...');
      final adminData = {
        'auth_id': authResponse.user!.id, // Primary Key - UUID من Authentication
        'id': customId, // ID مخصص بطول 10 أحرف
        'name': name,
        'email': email,
        'password_hash': hashedPassword,
      };
      debugPrint('📤 AuthRepository: Admin data: $adminData');

      final response = await SupabaseConfig.admins
          .insert(adminData)
          .select()
          .single();

      debugPrint('📥 AuthRepository: Database response: $response');

      final admin = AdminModel.fromJson(response);
      debugPrint('✅ AuthRepository: Registration completed successfully');
      debugPrint('🎉 AuthRepository: Admin created with ID: ${admin.id}');

      return admin;
    } catch (e, stackTrace) {
      debugPrint('❌ AuthRepository: Registration failed');
      debugPrint('💥 AuthRepository: Error: $e');
      debugPrint('📍 AuthRepository: Stack trace: $stackTrace');
      throw Exception('فشل في إنشاء الحساب: ${e.toString()}');
    }
  }

  Future<void> logout() async {
    try {
      await SupabaseConfig.client.auth.signOut();
    } catch (e) {
      throw Exception('فشل في تسجيل الخروج: ${e.toString()}');
    }
  }

  Future<bool> checkEmailExists(String email) async {
    try {
      final response = await SupabaseConfig.admins
          .select('auth_id')
          .eq('email', email);

      return response.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  Future<AdminModel?> getAdminById(String adminId) async {
    try {
      final response = await SupabaseConfig.admins
          .select()
          .eq('id', adminId)
          .single();

      return AdminModel.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  Future<AdminModel?> getCurrentUser() async {
    try {
      // Check if there's a current Supabase session
      final session = SupabaseConfig.client.auth.currentSession;
      if (session != null && session.user.email != null) {
        // Try to get admin by email from the session
        final response = await SupabaseConfig.admins
            .select()
            .eq('email', session.user.email!)
            .single();

        return AdminModel.fromJson(response);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  String _generateCustomId() {
    // إنشاء ID مخصص بطول 10 أحرف (أرقام وحروف)
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = DateTime.now().millisecondsSinceEpoch;
    var result = '';

    // استخدام timestamp كأساس
    var temp = random;
    for (int i = 0; i < 10; i++) {
      result += chars[temp % chars.length];
      temp = temp ~/ chars.length;
      if (temp == 0) temp = random + i; // إعادة تعيين إذا انتهى
    }

    return result;
  }
}
