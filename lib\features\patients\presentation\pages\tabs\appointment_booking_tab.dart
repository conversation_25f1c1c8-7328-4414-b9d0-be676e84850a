import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../../core/constants/app_colors.dart';
import '../../../../../core/models/patient_model.dart';
import '../../../../../core/models/time_slot_model.dart';
import '../../../../../core/models/appointment_model.dart';
import '../../../../../core/widgets/loading_dialog.dart';
import '../../../../../core/services/firebase_messaging_service.dart';
import '../../../../appointments/presentation/bloc/appointment_booking_bloc.dart';
import '../../../../appointments/presentation/bloc/appointment_booking_event.dart';
import '../../../../appointments/presentation/bloc/appointment_booking_state.dart';
import '../../../../appointments/presentation/widgets/appointment_type_dialog.dart';
import '../../../../appointments/data/repositories/appointment_booking_repository.dart';

class AppointmentBookingTab extends StatefulWidget {
  final PatientModel patient;

  const AppointmentBookingTab({super.key, required this.patient});

  @override
  State<AppointmentBookingTab> createState() => _AppointmentBookingTabState();
}

class _AppointmentBookingTabState extends State<AppointmentBookingTab>
    with AutomaticKeepAliveClientMixin {
  DateTime selectedDate = DateTime.now();
  AppointmentBookingResult? bookingResult;
  List<PatientAppointmentWithDetails> upcomingAppointments = [];
  List<PatientAppointmentWithDetails> pastAppointments = [];

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // Load data when widget is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadDataForDate(selectedDate);
        _loadUpcomingAppointments();
        _loadPastAppointments();
      }
    });
  }

  void _loadDataForDate(DateTime date) {
    // عرض loading dialog عند تحميل المواعيد
    LoadingDialog.show(context, 'جاري تحميل المواعيد المتاحة...');

    // Load available time slots for the selected date
    context.read<AppointmentBookingBloc>().add(
      LoadAvailableSlotsForDate(date: date),
    );
  }

  void _loadUpcomingAppointments() {
    // Load upcoming appointments for the patient
    context.read<AppointmentBookingBloc>().add(
      LoadPatientUpcomingAppointments(patientId: widget.patient.id),
    );
  }

  void _loadPastAppointments() {
    // Load past appointments for the patient
    context.read<AppointmentBookingBloc>().add(
      LoadPatientPastAppointments(patientId: widget.patient.id),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Scaffold(
      backgroundColor: AppColors.background,
      body: BlocListener<AppointmentBookingBloc, AppointmentBookingState>(
        listener: (context, state) {
          if (state is AppointmentBookingLoading) {
            // Loading dialog يتم عرضه في الدوال المناسبة
          } else if (state is AppointmentBookingLoaded) {
            LoadingDialog.hide(context);
            setState(() {
              bookingResult = state.bookingResult;
              upcomingAppointments = state.upcomingAppointments;
              pastAppointments = state.pastAppointments;
            });
          } else if (state is AppointmentBooked) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حجز الموعد بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is AppointmentBookingError) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          }
        },
        child: RefreshIndicator(
          onRefresh: _onRefresh,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                _buildHeader(),
                SizedBox(height: 20.h),

                // Date selector
                _buildDateSelector(),
                SizedBox(height: 20.h),

                // Available time slots
                _buildAvailableTimeSlots(),
                SizedBox(height: 20.h),

                // Patient's upcoming appointments
                _buildUpcomingAppointments(),
                SizedBox(height: 20.h),

                // Patient's past appointments
                _buildPastAppointments(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(Icons.calendar_today, size: 32.w, color: AppColors.white),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'حجز استشارة',
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.white,
                  ),
                ),
                Text(
                  'اختر الموعد المناسب للمريض ${widget.patient.name}',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateSelector() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray200),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختر التاريخ',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 12.h),
          InkWell(
            onTap: _selectDate,
            child: Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.gray300),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                children: [
                  Icon(Icons.calendar_today, color: AppColors.primary),
                  SizedBox(width: 12.w),
                  Text(
                    '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const Spacer(),
                  Icon(Icons.arrow_drop_down, color: AppColors.textSecondary),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAvailableTimeSlots() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray200),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'المواعيد المتاحة ليوم ${DateFormat('dd/MM/yyyy').format(selectedDate)}',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              if (bookingResult != null)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color:
                        bookingResult!.isHoliday
                            ? AppColors.error
                            : AppColors.primary,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    bookingResult!.dayName,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 16.h),

          if (bookingResult == null)
            const Center(child: CircularProgressIndicator())
          else if (bookingResult!.isHoliday)
            _buildHolidayMessage()
          else if (bookingResult!.availableSlots.isEmpty)
            _buildNoSlotsMessage()
          else
            _buildAvailableSlotsList(),
        ],
      ),
    );
  }

  Widget _buildHolidayMessage() {
    return Center(
      child: Column(
        children: [
          Icon(Icons.beach_access, size: 48.w, color: AppColors.error),
          SizedBox(height: 12.h),
          Text(
            'يوم إجازة',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.error,
            ),
          ),
          if (bookingResult?.holiday != null)
            Text(
              bookingResult!.holiday!.occasionName,
              style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
            ),
          SizedBox(height: 8.h),
          Text(
            'لا يمكن حجز مواعيد في هذا اليوم',
            style: TextStyle(fontSize: 12.sp, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildNoSlotsMessage() {
    return Center(
      child: Column(
        children: [
          Icon(Icons.access_time, size: 48.w, color: AppColors.gray400),
          SizedBox(height: 12.h),
          Text(
            'لا توجد مواعيد متاحة في هذا التاريخ',
            style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
          ),
          SizedBox(height: 8.h),
          Text(
            'جميع المواعيد محجوزة أو لا توجد مواعيد مجدولة لهذا اليوم',
            style: TextStyle(fontSize: 12.sp, color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAvailableSlotsList() {
    return Column(
      children: [
        // Available slots
        if (bookingResult!.availableSlots.isNotEmpty) ...[
          Text(
            'المواعيد المتاحة (${bookingResult!.availableSlots.length})',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.success,
            ),
          ),
          SizedBox(height: 8.h),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 1.2, // تقليل النسبة لتناسب التصميم العمودي
              crossAxisSpacing: 8.w,
              mainAxisSpacing: 8.h,
            ),
            itemCount: bookingResult!.availableSlots.length,
            itemBuilder: (context, index) {
              final slot = bookingResult!.availableSlots[index];
              return _buildTimeSlotCard(slot, isAvailable: true);
            },
          ),
        ],

        // Booked slots
        if (bookingResult!.bookedSlots.isNotEmpty) ...[
          SizedBox(height: 16.h),
          Text(
            'المواعيد المحجوزة (${bookingResult!.bookedSlots.length})',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.error,
            ),
          ),
          SizedBox(height: 8.h),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 1.2, // تقليل النسبة لتناسب التصميم العمودي
              crossAxisSpacing: 8.w,
              mainAxisSpacing: 8.h,
            ),
            itemCount: bookingResult!.bookedSlots.length,
            itemBuilder: (context, index) {
              final bookedSlot = bookingResult!.bookedSlots[index];
              return _buildTimeSlotCard(
                bookedSlot.timeSlot,
                isAvailable: false,
              );
            },
          ),
        ],
      ],
    );
  }

  Widget _buildTimeSlotCard(TimeSlotModel slot, {required bool isAvailable}) {
    final startTime12 = _formatTimeTo12Hour(slot.startTime);
    final endTime12 = _formatTimeTo12Hour(slot.endTime);

    return GestureDetector(
      onTap: isAvailable ? () => _bookAppointment(slot) : null,
      child: Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color:
              isAvailable
                  ? AppColors.primary.withValues(alpha: 0.1)
                  : AppColors.gray100,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: isAvailable ? AppColors.primary : AppColors.gray300,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            // وقت البداية
            Text(
              startTime12,
              style: TextStyle(
                fontSize: 11.sp,
                fontWeight: FontWeight.bold,
                color:
                    isAvailable ? AppColors.primary : AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            // خط فاصل
            Container(
              width: 20.w,
              height: 1.h,
              margin: EdgeInsets.symmetric(vertical: 2.h),
              color:
                  isAvailable
                      ? AppColors.primary.withValues(alpha: 0.5)
                      : AppColors.gray400,
            ),
            // وقت النهاية
            Text(
              endTime12,
              style: TextStyle(
                fontSize: 11.sp,
                fontWeight: FontWeight.bold,
                color:
                    isAvailable ? AppColors.primary : AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUpcomingAppointments() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray200),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.upcoming, color: AppColors.primary, size: 20.w),
              SizedBox(width: 8.w),
              Text(
                'المواعيد القادمة للمريض',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          if (upcomingAppointments.isEmpty)
            Center(
              child: Column(
                children: [
                  Icon(Icons.event_note, size: 48.w, color: AppColors.gray400),
                  SizedBox(height: 12.h),
                  Text(
                    'لا توجد مواعيد قادمة',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: upcomingAppointments.length,
              itemBuilder: (context, index) {
                final appointmentWithDetails = upcomingAppointments[index];
                return _buildAppointmentCard(appointmentWithDetails);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildPastAppointments() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray200),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.history, color: AppColors.textSecondary, size: 20.w),
              SizedBox(width: 8.w),
              Text(
                'المواعيد السابقة للمريض',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          if (pastAppointments.isEmpty)
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.history_outlined,
                    size: 48.w,
                    color: AppColors.gray400,
                  ),
                  SizedBox(height: 12.h),
                  Text(
                    'لا توجد مواعيد سابقة',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: pastAppointments.length,
              itemBuilder: (context, index) {
                final appointmentWithDetails = pastAppointments[index];
                return _buildAppointmentCard(
                  appointmentWithDetails,
                  isPast: true,
                );
              },
            ),
        ],
      ),
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 90)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: AppColors.white,
              surface: AppColors.white,
              onSurface: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
      });

      // Load data for the new date
      _loadDataForDate(selectedDate);

      // Show confirmation message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم اختيار تاريخ: ${DateFormat('dd/MM/yyyy').format(picked)}',
            ),
            backgroundColor: AppColors.success,
          ),
        );
      }
    }
  }

  void _bookAppointment(TimeSlotModel slot) {
    showDialog(
      context: context,
      builder:
          (context) => AppointmentTypeDialog(
            timeSlot: slot,
            selectedDate: selectedDate,
            patient: widget.patient,
            onTypeSelected:
                (appointmentType) => _confirmBooking(slot, appointmentType),
          ),
    );
  }

  void _confirmBooking(TimeSlotModel slot, String appointmentType) async {
    // عرض loading dialog فوراً
    LoadingDialog.show(context, 'جاري حجز الاستشارة...');

    final appointment = AppointmentModel(
      id: '', // سيتم إنشاؤه تلقائياً في قاعدة البيانات
      patientId: widget.patient.id,
      timeSlotId: slot.id,
      appointmentDate: selectedDate,
      status: appointmentType, // استخدام نوع الحجز المحدد (booked أو scheduled)
      notes: null,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    debugPrint(
      '🔄 AppointmentBookingTab: Creating appointment for patient: ${widget.patient.id}',
    );
    debugPrint('🔄 AppointmentBookingTab: Time slot ID: ${slot.id}');
    debugPrint(
      '🔄 AppointmentBookingTab: Date: ${selectedDate.toIso8601String()}',
    );

    // إرسال إشعار للمريض
    await _sendBookingNotification(appointment, slot, appointmentType);

    if (mounted) {
      context.read<AppointmentBookingBloc>().add(
        BookAppointmentSlot(appointment: appointment),
      );
    }
  }

  Future<void> _sendBookingNotification(
    AppointmentModel appointment,
    TimeSlotModel slot,
    String appointmentType,
  ) async {
    try {
      // الحصول على FCM tokens للمريض
      final fcmTokens = await _getPatientFcmTokens(widget.patient.id);

      if (fcmTokens.isEmpty) {
        debugPrint('⚠️ No FCM tokens found for patient: ${widget.patient.id}');
        return;
      }

      // تحديد نوع الإشعار والرسالة
      String title = '';
      String body = '';

      final dateStr = DateFormat('dd/MM/yyyy').format(selectedDate);
      final timeStr =
          '${_formatTimeTo12Hour(slot.startTime)} - ${_formatTimeTo12Hour(slot.endTime)}';

      if (appointmentType == 'booked') {
        title = '📅 تم حجز فحص واستشارة';
        body =
            'تم حجز فحص واستشارة من قبل العيادة.\nالتاريخ: $dateStr\nالوقت: $timeStr';
      } else if (appointmentType == 'scheduled') {
        title = '📅 تم حجز موعد قادم';
        body =
            'تم حجز موعد قادم من قبل العيادة.\nالتاريخ: $dateStr\nالوقت: $timeStr';
      }

      // إرسال الإشعار لجميع tokens المريض
      for (final token in fcmTokens) {
        await FirebaseMessagingService.sendNotification(
          fcmToken: token,
          title: title,
          body: body,
          data: {
            'type': 'appointment_booking',
            'appointment_type': appointmentType,
            'appointment_date': selectedDate.toIso8601String(),
            'time_slot_start': slot.startTime,
            'time_slot_end': slot.endTime,
            'patient_id': widget.patient.id,
          },
        );
      }

      debugPrint(
        '✅ Booking notification sent to patient: ${widget.patient.id}',
      );
    } catch (e) {
      debugPrint('❌ Error sending booking notification: $e');
      // لا نرمي الخطأ لأن الحجز نجح، فقط الإشعار فشل
    }
  }

  Future<List<String>> _getPatientFcmTokens(String patientId) async {
    try {
      // First get the auth_id for this patient
      final patientResponse =
          await Supabase.instance.client
              .from('patients')
              .select('auth_id')
              .eq('id', patientId)
              .maybeSingle();

      if (patientResponse == null || patientResponse['auth_id'] == null) {
        debugPrint('⚠️ No auth_id found for patient: $patientId');
        return [];
      }

      final authId = patientResponse['auth_id'] as String;
      debugPrint('🔍 Found auth_id for patient $patientId: $authId');

      // Now get FCM tokens using auth_id
      final response = await Supabase.instance.client
          .from('user_fcm_tokens')
          .select('fcm_token')
          .eq('user_id', authId)
          .eq('is_active', true);

      if (response.isEmpty) {
        debugPrint('⚠️ No FCM tokens found for auth_id: $authId');
        return [];
      }

      final tokens =
          (response as List)
              .map((item) => item['fcm_token'] as String)
              .where((token) => token.isNotEmpty)
              .toList();

      debugPrint(
        '📱 Found ${tokens.length} FCM tokens for patient: $patientId (auth_id: $authId)',
      );
      return tokens;
    } catch (e) {
      debugPrint('❌ Error getting FCM tokens for patient $patientId: $e');
      return [];
    }
  }

  Widget _buildAppointmentCard(
    PatientAppointmentWithDetails appointmentWithDetails, {
    bool isPast = false,
  }) {
    final appointment = appointmentWithDetails.appointment;
    final timeSlot = appointmentWithDetails.timeSlot;

    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color:
            isPast
                ? AppColors.gray100.withValues(alpha: 0.5)
                : AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color:
              isPast
                  ? AppColors.gray300.withValues(alpha: 0.5)
                  : AppColors.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            isPast ? Icons.history : Icons.schedule,
            color: isPast ? AppColors.textSecondary : AppColors.primary,
            size: 20.w,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  DateFormat('dd/MM/yyyy').format(appointment.appointmentDate),
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                    color:
                        isPast
                            ? AppColors.textSecondary
                            : AppColors.textPrimary,
                  ),
                ),
                Text(
                  'من ${_formatTimeTo12Hour(timeSlot.startTime)} إلى ${_formatTimeTo12Hour(timeSlot.endTime)}',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  'مدة الكشف: ${timeSlot.durationMinutes} دقيقة',
                  style: TextStyle(
                    fontSize: 11.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: _getStatusColor(appointment.status),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Text(
              _getStatusLabel(appointment.status),
              style: TextStyle(
                fontSize: 10.sp,
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'booked':
        return AppColors.primary;
      case 'scheduled':
        return AppColors.warning;
      case 'completed':
        return AppColors.success;
      case 'cancelled':
        return AppColors.error;
      case 'no_show':
        return AppColors.textSecondary;
      default:
        return AppColors.gray400;
    }
  }

  String _getStatusLabel(String status) {
    switch (status) {
      case 'booked':
        return 'كشف';
      case 'scheduled':
        return 'استشارة';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      case 'no_show':
        return 'لم يحضر';
      default:
        return status;
    }
  }

  /// تحويل الوقت من نظام 24 ساعة إلى 12 ساعة مع ص/م
  String _formatTimeTo12Hour(String time24) {
    try {
      final parts = time24.split(':');
      if (parts.length != 2) return time24;

      final hour = int.parse(parts[0]);
      final minute = parts[1];

      if (hour == 0) {
        return '12:$minute ص';
      } else if (hour < 12) {
        return '$hour:$minute ص';
      } else if (hour == 12) {
        return '12:$minute م';
      } else {
        return '${hour - 12}:$minute م';
      }
    } catch (e) {
      return time24; // إرجاع الوقت الأصلي في حالة الخطأ
    }
  }

  Future<void> _onRefresh() async {
    // تحديث البيانات للتاريخ المحدد والمريض
    context.read<AppointmentBookingBloc>().add(
      RefreshBookingData(
        selectedDate: selectedDate,
        patientId: widget.patient.id,
      ),
    );
    // انتظار قصير لإظهار مؤشر التحديث
    await Future.delayed(const Duration(milliseconds: 500));
  }
}
