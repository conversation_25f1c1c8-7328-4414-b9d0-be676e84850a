{"version": 3, "file": "toproto3json.js", "sourceRoot": "", "sources": ["../../typescript/src/toproto3json.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,EAAE;AACF,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,iDAAiD;AACjD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;AAIjC,+BAAyD;AACzD,mCAA0C;AAC1C,iCAA+D;AAC/D,iCAA0E;AAC1E,mCAOiB;AACjB,yCAAwE;AACxE,2CAA2E;AAC3E,yCAMoB;AACpB,2CAA2E;AAM3E,qFAAqF;AACrF,SAAS,kBAAkB,CAAC,KAAyB;;IACnD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAI,CAAA,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,WAAW,0CAAE,IAAI,MAAK,MAAM,EAAE;YACvC,OAAQ,KAAkB,CAAC,QAAQ,EAAE,CAAC;SACvC;QACD,MAAM,IAAI,KAAK,CAAC,iDAAiD,KAAK,EAAE,CAAC,CAAC;KAC3E;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAgB,YAAY,CAC1B,GAAqB,EACrB,OAA6B;IAE7B,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC;IAC1B,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,IAAI,KAAK,CACb,2HAA2H,CAC5H,CAAC;KACH;IAED,OAAO,CAAC,UAAU,EAAE,CAAC;IACrB,MAAM,QAAQ,GAAG,IAAA,gCAAyB,EAAC,OAAO,CAAC,CAAC;IAEpD,mDAAmD;IACnD,kEAAkE;IAClE,IAAI,QAAQ,KAAK,sBAAsB,EAAE;QACvC,OAAO,IAAA,mCAA6B,EAClC,GAA6B,EAC7B,OAAO,CACR,CAAC;KACH;IAED,IAAI,QAAQ,KAAK,wBAAwB,EAAE;QACzC,OAAO,IAAA,uCAA+B,EAAC,GAA+B,CAAC,CAAC;KACzE;IAED,IAAI,QAAQ,KAAK,yBAAyB,EAAE;QAC1C,OAAO,IAAA,wCAAgC,EAAC,GAAgC,CAAC,CAAC;KAC3E;IAED,IAAI,QAAQ,KAAK,4BAA4B,EAAE;QAC7C,OAAO,IAAA,2CAAmC,EACxC,GAAmC,CACpC,CAAC;KACH;IAED,IAAI,QAAQ,KAAK,2BAA2B,EAAE;QAC5C,OAAO,IAAA,6CAAkC,EACvC,GAAkC,CACnC,CAAC;KACH;IAED,IAAI,QAAQ,KAAK,4BAA4B,EAAE;QAC7C,OAAO,IAAA,+CAAmC,EACxC,GAAmC,CACpC,CAAC;KACH;IAED,IAAI,QAAQ,KAAK,4BAA4B,EAAE;QAC7C,OAAO,IAAA,+CAAmC,EACxC,GAAmC,CACpC,CAAC;KACH;IAED,IAAI,mBAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;QAC9B,OAAO,IAAA,8BAAmB,EACxB,GACsD,CACvD,CAAC;KACH;IAED,MAAM,MAAM,GAAe,EAAE,CAAC;IAC9B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC9C,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAClC,MAAM,iBAAiB,GAAG,KAAK,CAAC,YAAY,CAAC;QAC7C,MAAM,2BAA2B,GAAG,iBAAiB;YACnD,CAAC,CAAC,IAAA,gCAAyB,EAAC,iBAAiB,CAAC;YAC9C,CAAC,CAAC,IAAI,CAAC;QACT,IAAI,KAAK,KAAK,IAAI,EAAE;YAClB,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;YACnB,SAAS;SACV;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtB,wCAAwC;gBACxC,SAAS;aACV;YACD,2FAA2F;YAC3F,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CACrB,iBAAiB;gBACf,CAAC,CAAC,OAAO,CAAC,EAAE;oBACR,OAAO,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBACxC,CAAC;gBACH,CAAC,CAAC,kBAAkB,CACvB,CAAC;YACF,SAAS;SACV;QACD,IAAI,KAAK,CAAC,GAAG,EAAE;YACb,MAAM,GAAG,GAAe,EAAE,CAAC;YAC3B,KAAK,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACtD,sFAAsF;gBACtF,GAAG,CAAC,MAAM,CAAC,GAAG,iBAAiB;oBAC7B,CAAC,CAAC,YAAY,CAAC,QAA4B,EAAE,OAAO,CAAC;oBACrD,CAAC,CAAC,kBAAkB,CAAC,QAAqB,CAAC,CAAC;aAC/C;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;YAClB,SAAS;SACV;QACD,IAAI,2BAA2B,KAAK,4BAA4B,EAAE;YAChE,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;YACnB,SAAS;SACV;QACD,IAAI,iBAAiB,IAAI,QAAQ,IAAI,iBAAiB,IAAI,KAAK,KAAK,IAAI,EAAE;YACxE,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,YAAY,EAAE;gBACzB,MAAM,CAAC,GAAG,CAAC,GAAG,IAAA,+BAAwB,EAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;aAClE;iBAAM;gBACL,MAAM,CAAC,GAAG,CAAC,GAAG,IAAA,+BAAwB,EAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;aAClE;YACD,SAAS;SACV;QACD,IAAI,iBAAiB,EAAE;YACrB,MAAM,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAC3C,SAAS;SACV;QACD,IACE,OAAO,KAAK,KAAK,QAAQ;YACzB,OAAO,KAAK,KAAK,QAAQ;YACzB,OAAO,KAAK,KAAK,SAAS;YAC1B,KAAK,KAAK,IAAI,EACd;YACA,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACxD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;gBAC/B,SAAS;aACV;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACpB,SAAS;SACV;QACD,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,UAAU,EAAE;YACzD,MAAM,CAAC,GAAG,CAAC,GAAG,IAAA,yBAAiB,EAAC,KAAK,CAAC,CAAC;YACvC,SAAS;SACV;QACD,MAAM,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACxC,SAAS;KACV;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAxID,oCAwIC"}