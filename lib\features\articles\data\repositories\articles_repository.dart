import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/article_model.dart';

class ArticlesRepository {
  Future<List<ArticleModel>> getAllArticles() async {
    try {
      debugPrint('🔄 Starting to fetch all articles...');

      final response = await SupabaseConfig.articles
          .select('*')
          .order('created_at', ascending: false);

      debugPrint(
        '✅ Articles fetched successfully: ${response.length} articles',
      );
      debugPrint(
        '📊 Sample article data: ${response.isNotEmpty ? response.first.keys : "No articles"}',
      );

      if (response.isNotEmpty) {
        debugPrint('📄 First article sample: ${response.first}');
      }

      final articles =
          response.map<ArticleModel>((json) {
            try {
              return ArticleModel.fromJson(json);
            } catch (e) {
              debugPrint('❌ Error parsing article: $json');
              debugPrint('❌ Parse error: $e');
              rethrow;
            }
          }).toList();

      debugPrint('✅ Successfully parsed ${articles.length} articles');
      return articles;
    } catch (e) {
      debugPrint('❌ Error fetching articles: $e');
      debugPrint('📍 Stack trace: ${StackTrace.current}');
      throw Exception('فشل في جلب المقالات: ${e.toString()}');
    }
  }

  Future<List<ArticleModel>> getPublishedArticles() async {
    try {
      debugPrint('🔄 Fetching published articles...');
      final response = await SupabaseConfig.articles
          .select('*')
          .eq('is_published', true)
          .order('created_at', ascending: false);

      debugPrint('✅ Published articles fetched: ${response.length} articles');

      final articles =
          response.map<ArticleModel>((json) {
            try {
              return ArticleModel.fromJson(json);
            } catch (e) {
              debugPrint('❌ Error parsing published article: $json');
              debugPrint('❌ Parse error: $e');
              rethrow;
            }
          }).toList();

      return articles;
    } catch (e) {
      debugPrint('❌ Error fetching published articles: $e');
      throw Exception('فشل في جلب المقالات المنشورة: ${e.toString()}');
    }
  }

  Future<List<ArticleModel>> getDraftArticles() async {
    try {
      debugPrint('🔄 Fetching draft articles...');
      final response = await SupabaseConfig.articles
          .select('*')
          .eq('is_published', false)
          .order('created_at', ascending: false);

      debugPrint('✅ Draft articles fetched: ${response.length} articles');

      final articles =
          response.map<ArticleModel>((json) {
            try {
              return ArticleModel.fromJson(json);
            } catch (e) {
              debugPrint('❌ Error parsing draft article: $json');
              debugPrint('❌ Parse error: $e');
              rethrow;
            }
          }).toList();

      return articles;
    } catch (e) {
      debugPrint('❌ Error fetching draft articles: $e');
      throw Exception('فشل في جلب المسودات: ${e.toString()}');
    }
  }

  Future<ArticleModel> getArticleById(String id) async {
    try {
      final response =
          await SupabaseConfig.articles.select().eq('id', id).single();

      return ArticleModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في جلب بيانات المقال: ${e.toString()}');
    }
  }

  Future<ArticleModel> createArticle(ArticleModel article) async {
    try {
      debugPrint('🔄 Creating article: ${article.title}');
      final response =
          await SupabaseConfig.articles
              .insert(article.toJson())
              .select()
              .single();

      debugPrint('✅ Article created successfully: ${response['id']}');
      return ArticleModel.fromJson(response);
    } catch (e) {
      debugPrint('❌ Error creating article: $e');
      throw Exception('فشل في إنشاء المقال: ${e.toString()}');
    }
  }

  // New method to create article with image and PDF
  Future<ArticleModel> createArticleWithFiles(
    ArticleModel article,
    XFile? imageFile,
    PlatformFile? pdfFile,
  ) async {
    try {
      debugPrint('🔄 Creating article with files: ${article.title}');

      // Create the article first
      var createdArticle = await createArticle(article);

      // Upload image if provided
      if (imageFile != null) {
        debugPrint('📤 Uploading image for article: ${createdArticle.id}');
        final imageUrl = await uploadArticleImage(imageFile, createdArticle.id);
        createdArticle = createdArticle.copyWith(imageUrl: imageUrl);
      }

      // Upload PDF if provided
      if (pdfFile != null) {
        debugPrint('📤 Uploading PDF for article: ${createdArticle.id}');
        final pdfUrl = await uploadArticlePdf(pdfFile, createdArticle.id);
        createdArticle = createdArticle.copyWith(pdfUrl: pdfUrl);
      }

      // Update article with file URLs if any were uploaded
      if (imageFile != null || pdfFile != null) {
        return await updateArticle(createdArticle);
      }

      return createdArticle;
    } catch (e) {
      debugPrint('❌ Error creating article with files: $e');
      throw Exception('فشل في إنشاء المقال مع الملفات: ${e.toString()}');
    }
  }

  // Legacy method for backward compatibility
  Future<ArticleModel> createArticleWithImage(
    ArticleModel article,
    XFile? imageFile,
  ) async {
    return createArticleWithFiles(article, imageFile, null);
  }

  Future<ArticleModel> updateArticle(ArticleModel article) async {
    try {
      debugPrint('🔄 Updating article: ${article.id}');
      final response =
          await SupabaseConfig.articles
              .update(article.toJson())
              .eq('id', article.id)
              .select()
              .single();

      debugPrint('✅ Article updated successfully');
      return ArticleModel.fromJson(response);
    } catch (e) {
      debugPrint('❌ Error updating article: $e');
      throw Exception('فشل في تحديث المقال: ${e.toString()}');
    }
  }

  // New method to update article with files
  Future<ArticleModel> updateArticleWithFiles(
    ArticleModel article,
    XFile? imageFile,
    PlatformFile? pdfFile, {
    bool deleteOldImage = false,
    String? oldImageUrl,
    bool deleteOldPdf = false,
    String? oldPdfUrl,
  }) async {
    try {
      debugPrint('🔄 Updating article with files: ${article.id}');

      // Delete old image if requested
      if (deleteOldImage && oldImageUrl != null && oldImageUrl.isNotEmpty) {
        debugPrint('🗑️ Deleting old image from storage: $oldImageUrl');
        await _deleteImageFromStorage(oldImageUrl);
      }

      // Delete old PDF if requested
      if (deleteOldPdf && oldPdfUrl != null && oldPdfUrl.isNotEmpty) {
        debugPrint('🗑️ Deleting old PDF from storage: $oldPdfUrl');
        await _deletePdfFromStorage(oldPdfUrl);
      }

      // Update the article first
      var updatedArticle = await updateArticle(article);

      // Upload new image if provided
      if (imageFile != null) {
        debugPrint('📤 Uploading new image for article: ${article.id}');
        final imageUrl = await uploadArticleImage(imageFile, article.id);
        updatedArticle = updatedArticle.copyWith(imageUrl: imageUrl);
      }

      // Upload new PDF if provided
      if (pdfFile != null) {
        debugPrint('📤 Uploading new PDF for article: ${article.id}');
        final pdfUrl = await uploadArticlePdf(pdfFile, article.id);
        updatedArticle = updatedArticle.copyWith(pdfUrl: pdfUrl);
      }

      // Update article with new file URLs if any were uploaded
      if (imageFile != null || pdfFile != null) {
        updatedArticle = await updateArticle(updatedArticle);
      }

      return updatedArticle;
    } catch (e) {
      debugPrint('❌ Error updating article with files: $e');
      throw Exception('فشل في تحديث المقال مع الملفات: ${e.toString()}');
    }
  }

  // Legacy method for backward compatibility
  Future<ArticleModel> updateArticleWithImage(
    ArticleModel article,
    XFile? imageFile, {
    bool deleteOldImage = false,
    String? oldImageUrl,
  }) async {
    return updateArticleWithFiles(
      article,
      imageFile,
      null,
      deleteOldImage: deleteOldImage,
      oldImageUrl: oldImageUrl,
    );
  }

  Future<void> deleteArticle(String id) async {
    try {
      debugPrint('🔄 Deleting article: $id');

      // أولاً، جلب بيانات المقال للحصول على روابط الملفات
      final articleResponse =
          await SupabaseConfig.articles
              .select('image_url, pdf_url')
              .eq('id', id)
              .maybeSingle();

      // حذف المقال من قاعدة البيانات
      await SupabaseConfig.articles.delete().eq('id', id);

      debugPrint('✅ Article deleted from database: $id');

      // حذف الملفات من Storage إذا كانت موجودة
      if (articleResponse != null) {
        // حذف الصورة
        if (articleResponse['image_url'] != null) {
          final imageUrl = articleResponse['image_url'] as String;
          if (imageUrl.isNotEmpty) {
            await _deleteImageFromStorage(imageUrl);
          }
        }

        // حذف PDF
        if (articleResponse['pdf_url'] != null) {
          final pdfUrl = articleResponse['pdf_url'] as String;
          if (pdfUrl.isNotEmpty) {
            await _deletePdfFromStorage(pdfUrl);
          }
        }
      }

      debugPrint('✅ Article and associated files deleted successfully: $id');
    } catch (e) {
      debugPrint('❌ Error deleting article: $e');
      throw Exception('فشل في حذف المقال: ${e.toString()}');
    }
  }

  // دالة لاستخراج مسار الصورة من URL وحذفها
  Future<void> _deleteImageFromStorage(String imageUrl) async {
    try {
      debugPrint('🔄 Extracting image path from URL: $imageUrl');

      // استخراج مسار الصورة من URL
      // مثال URL: https://xwxeauofbzedfzaogzzy.supabase.co/storage/v1/object/public/article-images/articles/filename.jpg
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;

      // البحث عن مؤشر 'article-images' في المسار
      int bucketIndex = -1;
      for (int i = 0; i < pathSegments.length; i++) {
        if (pathSegments[i] == 'article-images') {
          bucketIndex = i;
          break;
        }
      }

      if (bucketIndex != -1 && bucketIndex < pathSegments.length - 1) {
        // استخراج المسار النسبي بعد اسم الـ bucket
        final relativePath = pathSegments.sublist(bucketIndex + 1).join('/');
        debugPrint('📁 Extracted relative path: $relativePath');

        // حذف الصورة من Storage
        await SupabaseConfig.storage.from('article-images').remove([
          relativePath,
        ]);

        debugPrint('✅ Image deleted from storage: $relativePath');
      } else {
        debugPrint('⚠️ Could not extract valid path from URL: $imageUrl');
      }
    } catch (e) {
      debugPrint('❌ Error deleting image from storage: $e');
      // لا نرمي exception هنا لأن حذف الصورة ليس أساسي
      // المهم أن المقال تم حذفه من قاعدة البيانات
    }
  }

  // دالة لاستخراج مسار PDF من URL وحذفه
  Future<void> _deletePdfFromStorage(String pdfUrl) async {
    try {
      debugPrint('🔄 Extracting PDF path from URL: $pdfUrl');

      // استخراج مسار PDF من URL
      // مثال URL: https://xwxeauofbzedfzaogzzy.supabase.co/storage/v1/object/public/article-pdfs/articles/filename.pdf
      final uri = Uri.parse(pdfUrl);
      final pathSegments = uri.pathSegments;

      // البحث عن مؤشر 'article-pdfs' في المسار
      int bucketIndex = -1;
      for (int i = 0; i < pathSegments.length; i++) {
        if (pathSegments[i] == 'article-pdfs') {
          bucketIndex = i;
          break;
        }
      }

      if (bucketIndex != -1 && bucketIndex < pathSegments.length - 1) {
        // استخراج المسار النسبي بعد اسم الـ bucket
        final relativePath = pathSegments.sublist(bucketIndex + 1).join('/');
        debugPrint('📁 Extracted relative PDF path: $relativePath');

        // حذف PDF من Storage
        await SupabaseConfig.articlePdfsBucket.remove([relativePath]);

        debugPrint('✅ PDF deleted from storage: $relativePath');
      } else {
        debugPrint('⚠️ Could not extract valid path from PDF URL: $pdfUrl');
      }
    } catch (e) {
      debugPrint('❌ Error deleting PDF from storage: $e');
      // لا نرمي exception هنا لأن حذف PDF ليس أساسي
    }
  }

  Future<ArticleModel> togglePublishStatus(String id) async {
    try {
      // First get the current status
      final current = await getArticleById(id);

      // Toggle the status
      final response =
          await SupabaseConfig.articles
              .update({'is_published': !current.isPublished})
              .eq('id', id)
              .select()
              .single();

      return ArticleModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في تغيير حالة النشر: ${e.toString()}');
    }
  }

  Future<List<ArticleModel>> getArticlesByCategory(String category) async {
    try {
      final response = await SupabaseConfig.articles
          .select()
          .eq('category', category)
          .eq('is_published', true)
          .order('created_at', ascending: false);

      return response
          .map<ArticleModel>((json) => ArticleModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب مقالات الفئة: ${e.toString()}');
    }
  }

  Future<List<ArticleModel>> getArticlesByAuthor(String author) async {
    try {
      final response = await SupabaseConfig.articles
          .select()
          .eq('author', author)
          .order('created_at', ascending: false);

      return response
          .map<ArticleModel>((json) => ArticleModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب مقالات الكاتب: ${e.toString()}');
    }
  }

  Future<List<ArticleModel>> searchArticles(String query) async {
    try {
      debugPrint('🔍 Searching articles by title with query: $query');

      final response = await SupabaseConfig.articles
          .select('*')
          .ilike('title', '%$query%')
          .order('created_at', ascending: false);

      final articles =
          response
              .map<ArticleModel>((json) => ArticleModel.fromJson(json))
              .toList();
      debugPrint(
        '✅ Found ${articles.length} articles with title matching: $query',
      );

      return articles;
    } catch (e) {
      debugPrint('❌ Error searching articles: $e');
      throw Exception('فشل في البحث عن المقالات: ${e.toString()}');
    }
  }

  Future<List<String>> getArticleCategories() async {
    try {
      debugPrint('🔄 Fetching article categories...');

      // Since we removed the category column and now use category_id,
      // we need to get categories from the article_categories table
      final response = await SupabaseConfig.client
          .from('article_categories')
          .select('id, name')
          .eq('is_active', true)
          .order('sort_order', ascending: true)
          .order('name', ascending: true);

      debugPrint(
        '📊 Raw article categories response: ${response.length} items',
      );

      final categories =
          response.map<String>((item) => item['name'] as String).toList();

      debugPrint(
        '✅ Article categories fetched: ${categories.length} unique categories',
      );
      debugPrint('📋 Article categories: $categories');
      return categories;
    } catch (e) {
      debugPrint('❌ Error fetching article categories: $e');
      throw Exception('فشل في جلب فئات المقالات: ${e.toString()}');
    }
  }

  Future<List<String>> getAuthors() async {
    try {
      final response = await SupabaseConfig.articles
          .select('author')
          .not('author', 'is', null);

      final authors =
          response
              .map<String>((item) => item['author'] as String)
              .toSet()
              .toList();

      authors.sort();
      return authors;
    } catch (e) {
      throw Exception('فشل في جلب قائمة الكتاب: ${e.toString()}');
    }
  }

  // Alias for getArticleCategories to match Bloc expectations
  Future<List<String>> getCategories() async {
    return getArticleCategories();
  }

  Future<List<ArticleModel>> getFeaturedArticles() async {
    try {
      debugPrint('🔄 Fetching featured articles...');
      final response = await SupabaseConfig.articles
          .select('*')
          .eq('is_featured', true)
          .eq('is_published', true)
          .order('created_at', ascending: false);

      debugPrint('✅ Featured articles fetched: ${response.length} articles');

      final articles =
          response.map<ArticleModel>((json) {
            try {
              return ArticleModel.fromJson(json);
            } catch (e) {
              debugPrint('❌ Error parsing featured article: $json');
              debugPrint('❌ Parse error: $e');
              rethrow;
            }
          }).toList();

      return articles;
    } catch (e) {
      debugPrint('❌ Error fetching featured articles: $e');
      throw Exception('فشل في جلب المقالات المميزة: ${e.toString()}');
    }
  }

  Future<ArticleModel> publishArticle(String articleId) async {
    try {
      final response =
          await SupabaseConfig.articles
              .update({
                'is_published': true,
                'published_at': DateTime.now().toIso8601String(),
              })
              .eq('id', articleId)
              .select()
              .single();

      return ArticleModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في نشر المقال: ${e.toString()}');
    }
  }

  Future<ArticleModel> unpublishArticle(String articleId) async {
    try {
      final response =
          await SupabaseConfig.articles
              .update({'is_published': false, 'published_at': null})
              .eq('id', articleId)
              .select()
              .single();

      return ArticleModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في إلغاء نشر المقال: ${e.toString()}');
    }
  }

  // New methods for category support
  Future<List<ArticleModel>> getArticlesByCategoryId(String categoryId) async {
    try {
      final response = await SupabaseConfig.articles
          .select('*')
          .eq('category_id', categoryId)
          .eq('is_published', true)
          .order('created_at', ascending: false);

      return response
          .map<ArticleModel>((json) => ArticleModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب مقالات القسم: ${e.toString()}');
    }
  }

  // Image upload methods
  Future<String> uploadArticleImage(XFile imageFile, String articleId) async {
    try {
      final fileName =
          'article_${articleId}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final filePath = 'articles/$fileName';

      debugPrint('📤 Uploading article image to: $filePath');

      // Upload to Supabase Storage
      final bytes = await imageFile.readAsBytes();
      debugPrint('📊 Article image size: ${bytes.length} bytes');

      await SupabaseConfig.storage
          .from('article-images')
          .uploadBinary(filePath, bytes);

      debugPrint('✅ Article image uploaded to storage');

      // Get public URL
      final imageUrl = SupabaseConfig.storage
          .from('article-images')
          .getPublicUrl(filePath);

      debugPrint('🔗 Article image public URL: $imageUrl');
      return imageUrl;
    } catch (e) {
      debugPrint('❌ Error uploading article image: $e');
      throw Exception('فشل في رفع الصورة: ${e.toString()}');
    }
  }

  Future<void> deleteArticleImage(String imagePath) async {
    try {
      await SupabaseConfig.storage.from('article-images').remove([imagePath]);
    } catch (e) {
      throw Exception('فشل في حذف الصورة: ${e.toString()}');
    }
  }

  // PDF upload methods
  Future<String> uploadArticlePdf(
    PlatformFile pdfFile,
    String articleId,
  ) async {
    try {
      final fileName =
          'article_${articleId}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final filePath = 'articles/$fileName';

      debugPrint('📤 Uploading article PDF to: $filePath');
      debugPrint('📄 PDF file name: ${pdfFile.name}');
      debugPrint('📄 PDF file size: ${pdfFile.size} bytes');

      // Get file bytes - handle both mobile and web platforms
      Uint8List bytes;

      if (pdfFile.bytes != null) {
        // Web platform - bytes are already loaded
        bytes = pdfFile.bytes!;
        debugPrint('📱 Using pre-loaded bytes (Web)');
      } else if (pdfFile.path != null) {
        // Mobile platform - read from file path
        debugPrint('📱 Reading from file path (Mobile): ${pdfFile.path}');
        final file = File(pdfFile.path!);
        if (!await file.exists()) {
          throw Exception('ملف PDF غير موجود في المسار المحدد');
        }
        bytes = await file.readAsBytes();
        debugPrint('📱 Successfully read ${bytes.length} bytes from file');
      } else {
        throw Exception('لا يمكن الوصول إلى ملف PDF - لا يوجد مسار أو بيانات');
      }

      debugPrint('📊 Final PDF size: ${bytes.length} bytes');

      // Check file size (max 10MB)
      if (bytes.length > 10 * 1024 * 1024) {
        throw Exception('حجم ملف PDF كبير جداً. الحد الأقصى 10 ميجابايت');
      }

      // Verify it's a PDF file by checking the header
      if (bytes.length < 4 ||
          bytes[0] != 0x25 ||
          bytes[1] != 0x50 ||
          bytes[2] != 0x44 ||
          bytes[3] != 0x46) {
        throw Exception('الملف المحدد ليس ملف PDF صحيح');
      }

      // Upload to Supabase Storage
      await SupabaseConfig.articlePdfsBucket.uploadBinary(filePath, bytes);

      debugPrint('✅ Article PDF uploaded to storage');

      // Get public URL
      final pdfUrl = SupabaseConfig.articlePdfsBucket.getPublicUrl(filePath);

      debugPrint('🔗 Article PDF public URL: $pdfUrl');
      return pdfUrl;
    } catch (e) {
      debugPrint('❌ Error uploading article PDF: $e');
      throw Exception('فشل في رفع ملف PDF: ${e.toString()}');
    }
  }

  Future<void> deleteArticlePdf(String pdfPath) async {
    try {
      await SupabaseConfig.articlePdfsBucket.remove([pdfPath]);
    } catch (e) {
      throw Exception('فشل في حذف ملف PDF: ${e.toString()}');
    }
  }
}
