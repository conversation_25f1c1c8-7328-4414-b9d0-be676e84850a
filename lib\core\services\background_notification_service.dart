import 'package:flutter/foundation.dart';
import 'package:workmanager/workmanager.dart';
import 'notification_scheduler_service.dart';

class BackgroundNotificationService {
  static const String _taskName = 'checkNotifications';
  static const String _uniqueName = 'notificationChecker';

  // Initialize WorkManager
  static Future<void> initialize() async {
    if (!kIsWeb) {
      await Workmanager().initialize(
        callbackDispatcher,
        isInDebugMode: kDebugMode,
      );
    }
  }

  // Start periodic background task
  static Future<void> startPeriodicTask() async {
    if (!kIsWeb) {
      await Workmanager().registerPeriodicTask(
        _uniqueName,
        _taskName,
        frequency: const Duration(minutes: 15), // Minimum on Android
        constraints: Constraints(
          networkType: NetworkType.connected,
        ),
      );
      debugPrint('🚀 Background notification service started');
    }
  }

  // Stop background task
  static Future<void> stopPeriodicTask() async {
    if (!kIsWeb) {
      await Workmanager().cancelByUniqueName(_uniqueName);
      debugPrint('🛑 Background notification service stopped');
    }
  }
}

// Background callback function
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      debugPrint('🔍 Background task: Checking for due notifications...');
      
      // Check and send due notifications
      await NotificationSchedulerService.processDueNotifications();
      
      debugPrint('✅ Background notification check completed');
      return Future.value(true);
    } catch (e) {
      debugPrint('❌ Background notification check failed: $e');
      return Future.value(false);
    }
  });
}
