{"version": 3, "sources": ["../../src/realtime.js"], "names": ["getRealtimeSignals", "length", "SIGRTMAX", "SIGRTMIN", "Array", "from", "getRealtimeSignal", "value", "index", "name", "number", "action", "description", "standard"], "mappings": ";AACO,KAAMA,CAAAA,kBAAkB,CAAG,UAAW;AAC3C,KAAMC,CAAAA,MAAM,CAAGC,QAAQ,CAAGC,QAAX,CAAsB,CAArC;AACA,MAAOC,CAAAA,KAAK,CAACC,IAAN,CAAW,CAAEJ,MAAF,CAAX,CAAuBK,iBAAvB,CAAP;AACD,CAHM,C;;AAKP,KAAMA,CAAAA,iBAAiB,CAAG,SAASC,KAAT,CAAgBC,KAAhB,CAAuB;AAC/C,MAAO;AACLC,IAAI,CAAG,QAAOD,KAAK,CAAG,CAAE,EADnB;AAELE,MAAM,CAAEP,QAAQ,CAAGK,KAFd;AAGLG,MAAM,CAAE,WAHH;AAILC,WAAW,CAAE,wCAJR;AAKLC,QAAQ,CAAE,OALL,CAAP;;AAOD,CARD;;AAUA,KAAMV,CAAAA,QAAQ,CAAG,EAAjB;AACO,KAAMD,CAAAA,QAAQ,CAAG,EAAjB,C", "sourcesContent": ["// List of realtime signals with information about them\nexport const getRealtimeSignals = function() {\n  const length = SIGRTMAX - SIGRTMIN + 1\n  return Array.from({ length }, getRealtimeSignal)\n}\n\nconst getRealtimeSignal = function(value, index) {\n  return {\n    name: `SIGRT${index + 1}`,\n    number: SIGRTMIN + index,\n    action: 'terminate',\n    description: 'Application-specific signal (realtime)',\n    standard: 'posix',\n  }\n}\n\nconst SIGRTMIN = 34\nexport const SIGRTMAX = 64\n"], "file": "src/realtime.js"}