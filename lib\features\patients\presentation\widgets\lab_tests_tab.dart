import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/utils/helpers.dart';

class LabTestsTab extends StatelessWidget {
  final PatientModel patient;

  const LabTestsTab({
    super.key,
    required this.patient,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with Add Button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                AppStrings.labTests,
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () {
                  _showAddLabTestDialog(context);
                },
                icon: Icon(
                  Icons.add,
                  size: 16.w,
                ),
                label: const Text('إضافة فحص'),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Lab Tests List
          Expanded(
            child: _buildLabTestsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildLabTestsList() {
    // Placeholder data - replace with actual data from repository
    final labTests = [
      {
        'name': 'تحليل دم شامل',
        'date': DateTime.now().subtract(const Duration(days: 30)),
        'hasImage': true,
      },
      {
        'name': 'وظائف الكلى',
        'date': DateTime.now().subtract(const Duration(days: 60)),
        'hasImage': false,
      },
      {
        'name': 'فيتامين د',
        'date': DateTime.now().subtract(const Duration(days: 90)),
        'hasImage': true,
      },
    ];

    if (labTests.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      itemCount: labTests.length,
      itemBuilder: (context, index) {
        final test = labTests[index];
        return Container(
          margin: EdgeInsets.only(bottom: 12.h),
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                color: AppColors.gray300.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Test Icon
              Container(
                width: 48.w,
                height: 48.h,
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  Icons.medical_information,
                  color: AppColors.primary,
                  size: 24.w,
                ),
              ),
              SizedBox(width: 12.w),

              // Test Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      test['name'] as String,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      Helpers.formatDate(test['date'] as DateTime),
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    if (test['hasImage'] as bool) ...[
                      SizedBox(height: 4.h),
                      Row(
                        children: [
                          Icon(
                            Icons.image,
                            size: 14.w,
                            color: AppColors.success,
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            'يحتوي على صورة',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: AppColors.success,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),

              // Action Buttons
              Column(
                children: [
                  IconButton(
                    onPressed: () {
                      // View test details
                    },
                    icon: Icon(
                      Icons.visibility,
                      color: AppColors.primary,
                      size: 20.w,
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      // Delete test
                      _showDeleteConfirmDialog(context, test['name'] as String);
                    },
                    icon: Icon(
                      Icons.delete,
                      color: AppColors.error,
                      size: 20.w,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.medical_information_outlined,
            size: 64.w,
            color: AppColors.gray400,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد فحوصات مخبرية',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'اضغط "إضافة فحص" لإضافة فحوصات جديدة',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textHint,
            ),
          ),
        ],
      ),
    );
  }

  void _showAddLabTestDialog(BuildContext context) {
    final testNameController = TextEditingController();
    DateTime selectedDate = DateTime.now();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('إضافة فحص مخبري'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: testNameController,
                decoration: const InputDecoration(
                  labelText: 'اسم الفحص',
                  hintText: 'مثال: تحليل دم شامل',
                ),
              ),
              SizedBox(height: 16.h),
              Row(
                children: [
                  Text(
                    'تاريخ الفحص: ',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextButton(
                    onPressed: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: selectedDate,
                        firstDate: DateTime(2020),
                        lastDate: DateTime.now(),
                      );
                      if (date != null) {
                        selectedDate = date;
                      }
                    },
                    child: Text(Helpers.formatDate(selectedDate)),
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              OutlinedButton.icon(
                onPressed: () {
                  // Pick image
                },
                icon: const Icon(Icons.image),
                label: const Text('إضافة صورة الفحص'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(AppStrings.cancel),
            ),
            ElevatedButton(
              onPressed: () {
                // Save lab test
                Navigator.of(context).pop();
                Helpers.showSuccessSnackbar(context, 'تم إضافة الفحص بنجاح');
              },
              child: const Text(AppStrings.save),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmDialog(BuildContext context, String testName) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('حذف الفحص'),
          content: Text('هل أنت متأكد من رغبتك في حذف فحص "$testName"؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(AppStrings.cancel),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                Helpers.showSuccessSnackbar(context, 'تم حذف الفحص بنجاح');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
              ),
              child: const Text(
                AppStrings.delete,
                style: TextStyle(color: AppColors.white),
              ),
            ),
          ],
        );
      },
    );
  }
}
