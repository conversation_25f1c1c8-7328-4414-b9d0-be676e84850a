name: Send Scheduled Notifications

on:
  schedule:
    # تشغيل كل دقيقة
    - cron: '* * * * *'
  workflow_dispatch: # للتشغيل اليدوي

jobs:
  send-notifications:
    runs-on: ubuntu-latest
    
    steps:
    - name: Send Notifications
      run: |
        curl -X POST \
          'https://xwxeauofbzedfzaogzzy.supabase.co/functions/v1/send-notifications-simple' \
          -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh3eGVhdW9mYnplZGZ6YW9nenp5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNjUyMTYsImV4cCI6MjA2NDc0MTIxNn0.YQMaXJELFdOOKOJBZJZJQJZJQJZJQJZJQJZJQJZJQJZ' \
          -H 'Content-Type: application/json' \
          -d '{}'
    
    - name: Log Result
      run: echo "Notification check completed at $(date)"
