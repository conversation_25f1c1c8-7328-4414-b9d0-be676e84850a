{"version": 3, "file": "transcoding.js", "sourceRoot": "", "sources": ["../../src/transcoding.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;AAmCH,4BAsBC;AAED,oEA6BC;AAED,kCAcC;AAED,gEA2BC;AAED,8CAKC;AAED,oDAKC;AAMD,oCA0BC;AAYD,sBA6BC;AAED,sCA0BC;AAED,sDAEC;AAED,8BAkEC;AAGD,8CAyCC;AApWD,iCAAuD;AASvD,MAAM,cAAc,GAAG,mBAAmB,CAAC;AAC3C,MAAM,kBAAkB,GAAG,iBAAiB,CAAC;AAK7C,qEAAqE;AACrE,MAAM,oBAAoB,GACxB,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;AAU5C,SAAgB,QAAQ,CACtB,OAAmB,EACnB,KAAa,EACb,YAAY,GAAG,KAAK,CAAC,qCAAqC;;IAE1D,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC/B,IAAI,KAAK,GAAc,OAAO,CAAC;IAC/B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,KAAK,GAAI,KAAoB,CAAC,IAAI,CAAc,CAAC;IACnD,CAAC;IACD,IACE,CAAC,YAAY;QACb,OAAO,KAAK,KAAK,QAAQ;QACzB,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QACrB,KAAK,KAAK,IAAI,EACd,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAgB,4BAA4B,CAC1C,OAAmB,EACnB,YAAyB,EACzB,cAAc,GAAG,EAAE;IAEnB,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;QACpD,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IACxC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,YAAY,CAAC,GAAG,CAAC,GAAG,cAAc,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC;YAChD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;YACjB,SAAS;QACX,CAAC;QACD,MAAM,kBAAkB,GAAG,GAAG,cAAc,GAAG,GAAG,GAAG,CAAC;QACtD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YAC7B,kHAAkH;YAClH,IAAI,CAAC,GAAG,CAAC,GAAI,IAAI,CAAC,GAAG,CAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAClD,4BAA4B,CAAC,KAAK,EAAE,IAAI,GAAG,EAAE,CAAC,CAC/C,CAAC;QACJ,CAAC;aAAM,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC;YAC/D,IAAI,CAAC,GAAG,CAAC,GAAG,4BAA4B,CACtC,IAAI,CAAC,GAAG,CAAe,EACvB,YAAY,EACZ,kBAAkB,CACnB,CAAC;QACJ,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAgB,WAAW,CAAC,OAAmB,EAAE,KAAa;IAC5D,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC/B,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxB,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAChC,OAAO;QACT,CAAC;QACD,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,EAAY,CAAC;QACrC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAe,CAAC;IACxC,CAAC;IACD,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,EAAY,CAAC;IACrC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAChC,OAAO;IACT,CAAC;IACD,OAAO,OAAO,CAAC,IAAI,CAAC,CAAC;AACvB,CAAC;AAED,SAAgB,0BAA0B,CACxC,OAAmB,EACnB,MAAM,GAAG,EAAE;IAEX,MAAM,UAAU,GAAG,EAAE,CAAC;IACtB,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;QAC1B,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YAChC,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,GAAG,CAAiB,EAAE,CAAC;gBACjD,UAAU,CAAC,IAAI,CACb,GAAG,MAAM,GAAG,oBAAoB,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAC3D,KAAK,CAAC,QAAQ,EAAE,CACjB,EAAE,CACJ,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC;YACrE,UAAU,CAAC,IAAI,CACb,GAAG,0BAA0B,CAAC,OAAO,CAAC,GAAG,CAAe,EAAE,GAAG,GAAG,GAAG,CAAC,CACrE,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,UAAU,CAAC,IAAI,CACb,GAAG,MAAM,GAAG,oBAAoB,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAC3D,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAE,CAAC,QAAQ,EAAE,CAC1D,EAAE,CACJ,CAAC;QACJ,CAAC;IACH,CAAC;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAgB,iBAAiB,CAAC,GAAW;IAC3C,OAAO,GAAG;SACP,KAAK,CAAC,EAAE,CAAC;SACT,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;SAClE,IAAI,CAAC,EAAE,CAAC,CAAC;AACd,CAAC;AAED,SAAgB,oBAAoB,CAAC,GAAW;IAC9C,OAAO,GAAG;SACP,KAAK,CAAC,EAAE,CAAC;SACT,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;SACnE,IAAI,CAAC,EAAE,CAAC,CAAC;AACd,CAAC;AAED,SAAS,YAAY,CAAC,GAAW;IAC/B,OAAO,GAAG,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;AACpD,CAAC;AAED,SAAgB,YAAY,CAC1B,OAAe,EACf,UAAkB;IAElB,IAAI,CAAC,OAAO,IAAI,OAAO,KAAK,GAAG,EAAE,CAAC;QAChC,OAAO,iBAAiB,CAAC,UAAU,CAAC,CAAC;IACvC,CAAC;IAED,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,OAAO,KAAK,UAAU,EAAE,CAAC;QACrD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,6EAA6E;IAC7E,MAAM,KAAK,GAAG,IAAI,MAAM,CACtB,GAAG;QACD,YAAY,CAAC,OAAO,CAAC;aAClB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;aAC5B,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC;QAC9B,GAAG,CACN,CAAC;IAEF,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;QAC7B,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,oBAAoB,CAAC,UAAU,CAAC,CAAC;AAC1C,CAAC;AAED,SAAS,gBAAgB,CAAC,KAAa;IACrC,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC/B,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAA,kBAAgB,EAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7D,CAAC;AAOD,SAAgB,KAAK,CACnB,OAAmB,EACnB,OAAe;IAEf,IAAI,GAAG,GAAG,OAAO,CAAC;IAClB,MAAM,aAAa,GAAG,EAAE,CAAC;IACzB,SAAS,CAAC;QACR,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,oCAAoC,CAAC,CAAC;QAC9D,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM;QACR,CAAC;QACD,MAAM,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;QAChD,MAAM,eAAe,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAChD,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC,CAAC;QACtD,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;QACtD,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC7B,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,MAAM,cAAc,GAAG,YAAY,CACjC,OAAO,EACP,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAW,CAAC,QAAQ,EAAE,CACtD,CAAC;QACF,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;YACjC,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,GAAG,GAAG,MAAM,GAAG,cAAc,GAAG,KAAK,CAAC;IACxC,CAAC;IAED,OAAO,EAAC,aAAa,EAAE,GAAG,EAAC,CAAC;AAC9B,CAAC;AAED,SAAgB,aAAa,CAAC,OAAmB;IAC/C,MAAM,MAAM,GAAe,EAAE,CAAC;IAC9B,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;QAC1B,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;YAC/B,SAAS;QACX,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YAChC,kEAAkE;YAClE,uDAAuD;YACvD,MAAM,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;YAC3B,SAAS;QACX,CAAC;QAED,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC;YAC9D,MAAM,MAAM,GAAG,aAAa,CAAC,OAAO,CAAC,GAAG,CAAe,CAAC,CAAC;YACzD,KAAK,MAAM,SAAS,IAAI,MAAM,EAAE,CAAC;gBAC/B,MAAM,CAAC,GAAG,GAAG,IAAI,SAAS,EAAE,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;YACpD,CAAC;YACD,SAAS;QACX,CAAC;QAED,MAAM,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAgB,qBAAqB,CAAC,KAAY;IAChD,OAAO,KAAK,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAQ,CAAC,kBAAkB,CAAC,CAAC;AACtE,CAAC;AAED,SAAgB,SAAS,CACvB,OAAmB,EACnB,aAAgC;IAEhC,MAAM,SAAS,GAAG,EAAE,CAAC;IACrB,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;QACnC,IAAI,CAAC,CAAC,cAAc,IAAI,MAAM,CAAC,EAAE,CAAC;YAChC,SAAS;QACX,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,CAAC,cAAc,CAAyB,CAAC;QAChE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEzB,IAAI,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,mBAAmB,EAAE,CAAC;YAClC,MAAM,kBAAkB,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC;gBACpE,CAAC,CAAC,QAAQ,CAAC,mBAAmB;gBAC9B,CAAC,CAAC,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;YACnC,SAAS,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;QACjC,KAAK,MAAM,UAAU,IAAI,oBAAoB,EAAE,CAAC;YAC9C,IAAI,CAAC,CAAC,UAAU,IAAI,QAAQ,CAAC,EAAE,CAAC;gBAC9B,SAAS;YACX,CAAC;YACD,MAAM,YAAY,GAAG,QAAQ,CAC3B,UAAwC,CAC/B,CAAC;YACZ,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YACjD,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;gBAC9B,SAAS;YACX,CAAC;YACD,MAAM,EAAC,GAAG,EAAE,aAAa,EAAC,GAAG,WAAW,CAAC;YAEzC,IAAI,IAAI,GACN,4BAA4B,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;YAChE,IAAI,QAAQ,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;gBAC1B,OAAO,EAAC,UAAU,EAAE,GAAG,EAAE,WAAW,EAAE,EAAE,EAAE,IAAI,EAAC,CAAC;YAClD,CAAC;YAED,qEAAqE;YACrE,MAAM,iBAAiB,GAAG,IAAI,CAAC;YAC/B,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAClB,IAAI,GAAG,QAAQ,CACb,iBAAiB,EACjB,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC/B,iBAAiB,CAAC,IAAI,CACvB,CAAC;gBACF,WAAW,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;YAClE,CAAC;iBAAM,CAAC;gBACN,IAAI,GAAG,EAAE,CAAC;YACZ,CAAC;YACD,MAAM,qBAAqB,GACzB,0BAA0B,CAAC,iBAAiB,CAAC,CAAC;YAChD,MAAM,WAAW,GAAG,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpD,IACE,CAAC,IAAI;gBACL,CAAC,OAAO,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,EAC5D,CAAC;gBACD,IAAI,GAAG,EAAE,CAAC;YACZ,CAAC;YACD,OAAO,EAAC,UAAU,EAAE,GAAG,EAAE,WAAW,EAAE,IAAI,EAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,+CAA+C;AAC/C,SAAgB,iBAAiB,CAC/B,SAAsC,EACtC,SAAwB;IAExB,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,SAAS;QACX,CAAC;QACD,MAAM,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAoB,CAAC;QAC/D,wEAAwE;QACxE,qDAAqD;QACrD,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;YAC/B,SAAS;QACX,CAAC;QACD,KAAK,MAAM,IAAI,IAAI,GAAG,CAAC,aAAa,EAAE,CAAC;YACrC,IAAI,CAAC,CAAC,cAAc,IAAI,IAAI,CAAC,EAAE,CAAC;gBAC9B,SAAS;YACX,CAAC;YACD,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;YACzC,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;gBACrC,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;oBACvB,IAAI,UAAU,KAAK,qBAAqB,EAAE,CAAC;wBACzC,SAAS;oBACX,CAAC;oBACD,WAAW,CAAC,UAAU,CAAC;wBACrB,IAAI,CAAC,UAAwC,CAAC,CAAC;gBACnD,CAAC;gBACD,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC7B,WAAW,CAAC,qBAAqB,CAAC,GAAG,CAAC,WAAW,CAC/C,qBAAqB,CACtB;wBACC,CAAC,CAAC,EAAE;wBACJ,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;4BACjD,CAAC,CAAC,WAAW,CAAC,qBAAqB,CAAC;4BACpC,CAAC,CAAC,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC,CAAC;oBAC3C,2DAA2D;oBAC3D,WAAW,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC;gBACvE,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC"}