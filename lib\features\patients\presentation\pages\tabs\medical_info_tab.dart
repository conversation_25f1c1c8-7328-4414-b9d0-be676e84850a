import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../../../../../core/constants/app_colors.dart';
import '../../../../../core/models/medical_info_model.dart';
import '../../../../../core/widgets/loading_dialog.dart';
import '../../bloc/medical_info_bloc.dart';
import '../../bloc/medical_info_event.dart';
import '../../bloc/medical_info_state.dart';
import '../../widgets/medical_info_form_dialog.dart';

class MedicalInfoTab extends StatefulWidget {
  final String patientId;

  const MedicalInfoTab({
    super.key,
    required this.patientId,
  });

  @override
  State<MedicalInfoTab> createState() => _MedicalInfoTabState();
}

class _MedicalInfoTabState extends State<MedicalInfoTab>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late TabController _subTabController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _subTabController = TabController(length: 4, vsync: this);

    // Load medical info when widget is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<MedicalInfoBloc>().add(
          LoadMedicalInfoByPatientId(patientId: widget.patientId),
        );
      }
    });
  }

  @override
  void dispose() {
    _subTabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(50.h),
        child: AppBar(
          backgroundColor: AppColors.white,
          elevation: 0,
          automaticallyImplyLeading: false,
          bottom: TabBar(
            controller: _subTabController,
            labelColor: AppColors.primary,
            unselectedLabelColor: AppColors.textSecondary,
            indicatorColor: AppColors.primary,
            isScrollable: true,
            tabAlignment: TabAlignment.start,
            tabs: const [
              Tab(text: 'الأدوية والمكملات'),
              Tab(text: 'الحالة الصحية'),
              Tab(text: 'حساسية الأطعمة'),
              Tab(text: 'النشاط البدني'),
            ],
          ),
        ),
      ),
      body: BlocConsumer<MedicalInfoBloc, MedicalInfoState>(
        listener: (context, state) {
          if (state is MedicalInfoLoading) {
            LoadingDialog.show(context, 'جاري التحميل...');
          } else if (state is MedicalInfoLoaded) {
            LoadingDialog.hide(context);
          } else if (state is MedicalInfoCreated) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إضافة المعلومة الطبية بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is MedicalInfoUpdated) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم تحديث المعلومة الطبية بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is MedicalInfoDeleted) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حذف المعلومة الطبية بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is MedicalInfoStatusToggled) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.isActive ? 'تم تفعيل المعلومة الطبية بنجاح' : 'تم إيقاف المعلومة الطبية بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is MedicalInfoError) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          }
        },
        builder: (context, state) {
          return TabBarView(
            controller: _subTabController,
            children: [
              // Medications and Supplements
              _buildMedicationsTab(state),

              // Medical Conditions
              _buildConditionsTab(state),

              // Food Allergies
              _buildAllergiesTab(state),

              // Physical Activity
              _buildActivityTab(state),
            ],
          );
        },
      ),
    );
  }

  Widget _buildMedicationsTab(MedicalInfoState state) {
    return Column(
      children: [
        _buildSubTabActionBar('إضافة دواء/مكمل', 'medication'),
        Expanded(
          child: _buildMedicalInfoContent(state, 'medication'),
        ),
      ],
    );
  }

  Widget _buildConditionsTab(MedicalInfoState state) {
    return Column(
      children: [
        _buildSubTabActionBar('إضافة حالة صحية', 'condition'),
        Expanded(
          child: _buildMedicalInfoContent(state, 'condition'),
        ),
      ],
    );
  }

  Widget _buildAllergiesTab(MedicalInfoState state) {
    return Column(
      children: [
        _buildSubTabActionBar('إضافة حساسية', 'allergy'),
        Expanded(
          child: _buildMedicalInfoContent(state, 'allergy'),
        ),
      ],
    );
  }

  Widget _buildActivityTab(MedicalInfoState state) {
    return Column(
      children: [
        _buildSubTabActionBar('إضافة نشاط', 'activity'),
        Expanded(
          child: _buildMedicalInfoContent(state, 'activity'),
        ),
      ],
    );
  }

  Widget _buildSubTabActionBar(String buttonText, String infoType) {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.2),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Add button
          ElevatedButton.icon(
            onPressed: () => _showAddDialog(infoType),
            icon: const Icon(Icons.add, size: 20),
            label: Text(buttonText),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
          ),

          const Spacer(),

          // Refresh button
          IconButton(
            onPressed: _refreshData,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
        ],
      ),
    );
  }

  Widget _buildMedicalInfoContent(MedicalInfoState state, String infoType) {
    if (state is MedicalInfoLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is MedicalInfoLoaded) {
      final filteredItems = state.groupedByType[infoType] ?? <MedicalInfoModel>[];

      if (filteredItems.isEmpty) {
        return _buildEmptyState(infoType);
      }

      return _buildMedicalInfoList(filteredItems);
    }

    if (state is MedicalInfoError) {
      return _buildErrorState(state.message);
    }

    return _buildInitialState(infoType);
  }

  Widget _buildMedicalInfoList(List<MedicalInfoModel> items) {
    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: ListView.builder(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16.w),
        itemCount: items.length,
        itemBuilder: (context, index) {
          final item = items[index];
          return _buildMedicalInfoCard(item);
        },
      ),
    );
  }

  Widget _buildMedicalInfoCard(MedicalInfoModel item) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray200),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with name and actions
          Row(
            children: [
              Icon(
                _getIconForType(item.infoType),
                size: 20.w,
                color: AppColors.primary,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  item.name,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              // Status indicator
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: item.isActive ? AppColors.success : AppColors.error,
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  item.isActive ? 'نشط' : 'غير نشط',
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      _showEditDialog(item);
                      break;
                    case 'delete':
                      _deleteMedicalInfo(item);
                      break;
                    case 'toggle':
                      _toggleStatus(item);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 20),
                        SizedBox(width: 8),
                        Text('تعديل'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'toggle',
                    child: Row(
                      children: [
                        Icon(
                          item.isActive ? Icons.pause : Icons.play_arrow,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(item.isActive ? 'إيقاف' : 'تفعيل'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 20, color: Colors.red),
                        SizedBox(width: 8),
                        Text('حذف', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),

          if (item.description != null && item.description!.isNotEmpty) ...[
            SizedBox(height: 8.h),
            Text(
              item.description!,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textSecondary,
              ),
            ),
          ],

          // Additional details based on type
          SizedBox(height: 12.h),
          _buildTypeSpecificDetails(item),

          // Notes if available
          if (item.notes != null && item.notes!.isNotEmpty) ...[
            SizedBox(height: 12.h),
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: AppColors.gray100,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.note,
                    size: 16.w,
                    color: AppColors.textSecondary,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      item.notes!,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTypeSpecificDetails(MedicalInfoModel item) {
    final details = <Widget>[];

    // Dosage and frequency for medications and supplements
    if ((item.infoType == 'medication' || item.infoType == 'supplement') &&
        (item.dosage != null || item.frequency != null)) {
      details.add(
        Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Row(
            children: [
              if (item.dosage != null) ...[
                Icon(Icons.medication, size: 16.w, color: AppColors.primary),
                SizedBox(width: 6.w),
                Text(
                  'الجرعة: ${item.dosage}',
                  style: TextStyle(fontSize: 12.sp, color: AppColors.textPrimary),
                ),
              ],
              if (item.dosage != null && item.frequency != null) ...[
                SizedBox(width: 16.w),
                Container(width: 1, height: 16.h, color: AppColors.gray300),
                SizedBox(width: 16.w),
              ],
              if (item.frequency != null) ...[
                Icon(Icons.schedule, size: 16.w, color: AppColors.primary),
                SizedBox(width: 6.w),
                Text(
                  'التكرار: ${item.frequency}',
                  style: TextStyle(fontSize: 12.sp, color: AppColors.textPrimary),
                ),
              ],
            ],
          ),
        ),
      );
    }

    // Severity for allergies and conditions
    if ((item.infoType == 'allergy' || item.infoType == 'condition') &&
        item.severity != null) {
      final severityColor = _getSeverityColor(item.severity!);
      details.add(
        Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: severityColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Row(
            children: [
              Icon(Icons.warning, size: 16.w, color: severityColor),
              SizedBox(width: 8.w),
              Text(
                'الشدة: ${_getSeverityLabel(item.severity!)}',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: severityColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Dates for medications, supplements, and conditions
    if ((item.infoType == 'medication' ||
         item.infoType == 'supplement' ||
         item.infoType == 'condition') &&
        (item.startDate != null || item.endDate != null)) {
      details.add(
        Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: AppColors.info.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Row(
            children: [
              Icon(Icons.calendar_today, size: 16.w, color: AppColors.info),
              SizedBox(width: 8.w),
              if (item.startDate != null) ...[
                Text(
                  'من: ${DateFormat('dd/MM/yyyy').format(item.startDate!)}',
                  style: TextStyle(fontSize: 12.sp, color: AppColors.textPrimary),
                ),
              ],
              if (item.startDate != null && item.endDate != null) ...[
                SizedBox(width: 16.w),
                Text(
                  'إلى: ${DateFormat('dd/MM/yyyy').format(item.endDate!)}',
                  style: TextStyle(fontSize: 12.sp, color: AppColors.textPrimary),
                ),
              ],
            ],
          ),
        ),
      );
    }

    return Column(children: details);
  }

  Widget _buildEmptyState(String infoType) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getIconForType(infoType),
            size: 64.w,
            color: AppColors.gray400,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد ${_getTypeLabel(infoType)}',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'ابدأ بإضافة أول ${_getTypeLabel(infoType)} للمريض',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton.icon(
            onPressed: () => _showAddDialog(infoType),
            icon: const Icon(Icons.add),
            label: Text('إضافة ${_getTypeLabel(infoType)}'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.w,
            color: AppColors.error,
          ),
          SizedBox(height: 16.h),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.error,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _refreshData,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialState(String infoType) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getIconForType(infoType),
            size: 64.w,
            color: AppColors.gray400,
          ),
          SizedBox(height: 16.h),
          Text(
            'جاري تحميل ${_getTypeLabel(infoType)}...',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _refreshData,
            child: const Text('تحميل البيانات'),
          ),
        ],
      ),
    );
  }

  IconData _getIconForType(String infoType) {
    switch (infoType) {
      case 'medication':
      case 'supplement':
        return Icons.medication;
      case 'condition':
        return Icons.local_hospital;
      case 'allergy':
        return Icons.warning;
      case 'activity':
        return Icons.directions_run;
      default:
        return Icons.info;
    }
  }

  String _getTypeLabel(String infoType) {
    switch (infoType) {
      case 'medication':
        return 'الأدوية والمكملات';
      case 'supplement':
        return 'المكملات الغذائية';
      case 'condition':
        return 'الحالات الصحية';
      case 'allergy':
        return 'الحساسيات';
      case 'activity':
        return 'الأنشطة البدنية';
      default:
        return 'المعلومات الطبية';
    }
  }

  Color _getSeverityColor(String severity) {
    switch (severity) {
      case 'mild':
        return AppColors.warning;
      case 'moderate':
        return Colors.orange;
      case 'severe':
        return AppColors.error;
      default:
        return AppColors.gray400;
    }
  }

  String _getSeverityLabel(String severity) {
    switch (severity) {
      case 'mild':
        return 'خفيف';
      case 'moderate':
        return 'متوسط';
      case 'severe':
        return 'شديد';
      default:
        return severity;
    }
  }

  void _showAddDialog(String infoType) {
    showDialog(
      context: context,
      builder: (context) => BlocProvider.value(
        value: context.read<MedicalInfoBloc>(),
        child: MedicalInfoFormDialog(
          patientId: widget.patientId,
          infoType: infoType,
        ),
      ),
    );
  }

  void _showEditDialog(MedicalInfoModel medicalInfo) {
    showDialog(
      context: context,
      builder: (context) => BlocProvider.value(
        value: context.read<MedicalInfoBloc>(),
        child: MedicalInfoFormDialog(
          patientId: widget.patientId,
          infoType: medicalInfo.infoType,
          medicalInfo: medicalInfo,
        ),
      ),
    );
  }

  void _deleteMedicalInfo(MedicalInfoModel medicalInfo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف "${medicalInfo.name}"؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Show loading dialog immediately
              LoadingDialog.show(context, 'جاري حذف المعلومة الطبية...');
              context.read<MedicalInfoBloc>().add(
                DeleteMedicalInfo(
                  medicalInfoId: medicalInfo.id,
                  patientId: widget.patientId,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _toggleStatus(MedicalInfoModel medicalInfo) {
    final newStatus = !medicalInfo.isActive;
    // Show loading dialog immediately
    LoadingDialog.show(
      context,
      newStatus ? 'جاري تفعيل المعلومة الطبية...' : 'جاري إيقاف المعلومة الطبية...'
    );
    context.read<MedicalInfoBloc>().add(
      ToggleMedicalInfoStatus(
        medicalInfoId: medicalInfo.id,
        isActive: newStatus,
        patientId: widget.patientId,
      ),
    );
  }

  void _refreshData() {
    context.read<MedicalInfoBloc>().add(
      RefreshMedicalInfo(patientId: widget.patientId),
    );
  }

  Future<void> _onRefresh() async {
    _refreshData();
    // انتظار قصير لإظهار مؤشر التحديث
    await Future.delayed(const Duration(milliseconds: 500));
  }
}
