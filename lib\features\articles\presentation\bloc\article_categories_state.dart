import 'package:equatable/equatable.dart';
import '../../../../core/models/article_category_model.dart';

abstract class ArticleCategoriesState extends Equatable {
  const ArticleCategoriesState();

  @override
  List<Object?> get props => [];
}

class ArticleCategoriesInitial extends ArticleCategoriesState {}

class ArticleCategoriesLoading extends ArticleCategoriesState {}

class ArticleCategoriesLoaded extends ArticleCategoriesState {
  final List<ArticleCategoryModel> categories;
  final List<ArticleCategoryModel> filteredCategories;
  final String searchQuery;

  const ArticleCategoriesLoaded({
    required this.categories,
    required this.filteredCategories,
    this.searchQuery = '',
  });

  ArticleCategoriesLoaded copyWith({
    List<ArticleCategoryModel>? categories,
    List<ArticleCategoryModel>? filteredCategories,
    String? searchQuery,
  }) {
    return ArticleCategoriesLoaded(
      categories: categories ?? this.categories,
      filteredCategories: filteredCategories ?? this.filteredCategories,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  @override
  List<Object?> get props => [categories, filteredCategories, searchQuery];
}

class ArticleCategoriesError extends ArticleCategoriesState {
  final String message;

  const ArticleCategoriesError({required this.message});

  @override
  List<Object?> get props => [message];
}

class ArticleCategoryCreated extends ArticleCategoriesState {
  final ArticleCategoryModel category;

  const ArticleCategoryCreated({required this.category});

  @override
  List<Object?> get props => [category];
}

class ArticleCategoryUpdated extends ArticleCategoriesState {
  final ArticleCategoryModel category;

  const ArticleCategoryUpdated({required this.category});

  @override
  List<Object?> get props => [category];
}

class ArticleCategoryDeleted extends ArticleCategoriesState {
  final String categoryId;

  const ArticleCategoryDeleted({required this.categoryId});

  @override
  List<Object?> get props => [categoryId];
}
