import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/article_model.dart';
import '../../../../core/utils/debouncer.dart';
import '../../../../core/widgets/custom_search_bar.dart';
import '../bloc/articles_bloc.dart';
import '../bloc/articles_event.dart';
import '../bloc/articles_state.dart';
import '../bloc/article_categories_bloc.dart';
import 'article_categories_page.dart';
import 'article_form_page.dart';

class ArticlesPage extends StatefulWidget {
  final bool isVisible;
  final bool hasBeenVisited;

  const ArticlesPage({
    super.key,
    this.isVisible = false,
    this.hasBeenVisited = false,
  });

  @override
  State<ArticlesPage> createState() => _ArticlesPageState();
}

class _ArticlesPageState extends State<ArticlesPage>
    with AutomaticKeepAliveClientMixin {
  final TextEditingController _searchController = TextEditingController();
  final Debouncer _debouncer = Debouncer(milliseconds: 500);
  bool _isDeleteDialogOpen = false;

  // Track if data has been loaded
  bool _dataLoaded = false;

  @override
  bool get wantKeepAlive => true; // Keep state alive

  @override
  void didUpdateWidget(ArticlesPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Check if page became visible
    if (widget.isVisible && !oldWidget.isVisible) {
      _loadArticlesIfNeeded();
    }
  }

  void _loadArticlesIfNeeded() {
    // Only load if page is visible and data hasn't been loaded yet
    if (widget.isVisible && !_dataLoaded) {
      debugPrint('🔄 Loading articles data for first time...');
      context.read<ArticlesBloc>().add(LoadAllArticles());
      _dataLoaded = true;
    }
  }

  Future<void> _onRefreshArticles() async {
    debugPrint('🔄 Refreshing articles data...');
    context.read<ArticlesBloc>().add(LoadAllArticles());
    await Future.delayed(const Duration(milliseconds: 500));
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debouncer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    // Load data only if page is currently visible
    if (widget.isVisible) {
      _loadArticlesIfNeeded();
    }

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(AppStrings.articles),
        backgroundColor: AppColors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const ArticleCategoriesPage(),
                ),
              );
            },
            icon: const Icon(Icons.category),
            tooltip: 'إدارة أقسام المقالات',
          ),
        ],
      ),
      body: BlocConsumer<ArticlesBloc, ArticlesState>(
        listener: (context, state) {
          if (state is ArticlesError) {
            // إغلاق loading dialog إذا كان مفتوح
            if (_isDeleteDialogOpen) {
              Navigator.of(context).pop();
              _isDeleteDialogOpen = false;
            }

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          } else if (state is ArticlesLoaded) {
            // إغلاق loading dialog عند نجاح العملية
            if (_isDeleteDialogOpen) {
              Navigator.of(context).pop();
              _isDeleteDialogOpen = false;

              // عرض رسالة نجاح
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم حذف المقال بنجاح'),
                  backgroundColor: AppColors.success,
                ),
              );
            }
          }
        },
        builder: (context, state) {
          if (state is ArticlesLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is ArticlesLoaded) {
            return _buildArticlesView(state);
          }

          return const Center(child: Text('لا توجد مقالات'));
        },
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: "articles_fab",
        onPressed: _showAddArticleForm,
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add, color: AppColors.white),
      ),
    );
  }

  Widget _buildArticlesView(ArticlesLoaded state) {
    return RefreshIndicator(
      onRefresh: _onRefreshArticles,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            // Search Bar
            CustomSearchBar(
              hintText: 'البحث بعنوان المقال...',
              controller: _searchController,
              onSearch: (query) {
                context.read<ArticlesBloc>().add(SearchArticles(query: query));
              },
              onClear: () {
                context.read<ArticlesBloc>().add(LoadAllArticles());
              },
            ),
            SizedBox(height: 16.h),

            // Articles List
            Expanded(
              child:
                  state.filteredArticles.isEmpty
                      ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.article_outlined,
                              size: 64.w,
                              color: AppColors.gray400,
                            ),
                            SizedBox(height: 16.h),
                            Text(
                              state.isSearching
                                  ? 'لا توجد نتائج للبحث'
                                  : 'لا توجد مقالات',
                              style: TextStyle(
                                fontSize: 18.sp,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      )
                      : ListView.builder(
                        itemCount: state.filteredArticles.length,
                        itemBuilder: (context, index) {
                          final article = state.filteredArticles[index];
                          return _buildArticleCard(article);
                        },
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildArticleCard(ArticleModel article) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Article Image
                Container(
                  width: 60.w,
                  height: 60.h,
                  decoration: BoxDecoration(
                    color: AppColors.gray100,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child:
                      article.displayImageUrl.isNotEmpty
                          ? ClipRRect(
                            borderRadius: BorderRadius.circular(8.r),
                            child: Image.network(
                              article.displayImageUrl,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Icon(
                                  Icons.article,
                                  size: 24.w,
                                  color: AppColors.gray400,
                                );
                              },
                            ),
                          )
                          : Icon(
                            Icons.article,
                            size: 24.w,
                            color: AppColors.gray400,
                          ),
                ),
                SizedBox(width: 12.w),

                // Article Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              article.title,
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                                color: AppColors.textPrimary,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 4.h,
                            ),
                            decoration: BoxDecoration(
                              color:
                                  article.isPublished
                                      ? AppColors.success.withValues(alpha: 0.1)
                                      : AppColors.warning.withValues(
                                        alpha: 0.1,
                                      ),
                              borderRadius: BorderRadius.circular(6.r),
                            ),
                            child: Text(
                              article.isPublished
                                  ? AppStrings.published
                                  : AppStrings.draft,
                              style: TextStyle(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w600,
                                color:
                                    article.isPublished
                                        ? AppColors.success
                                        : AppColors.warning,
                              ),
                            ),
                          ),
                        ],
                      ),
                      // عرض سطرين من المحتوى
                      SizedBox(height: 8.h),
                      Text(
                        article.content,
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: AppColors.textSecondary,
                          height: 1.4,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        'بواسطة: ${article.author}',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.textSecondary,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 8.w),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        _showEditArticleForm(article);
                        break;
                      case 'delete':
                        _deleteArticle(article.id);
                        break;
                    }
                  },
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit),
                              SizedBox(width: 8),
                              Text('تعديل'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: AppColors.error),
                              SizedBox(width: 8),
                              Text(
                                'حذف',
                                style: TextStyle(color: AppColors.error),
                              ),
                            ],
                          ),
                        ),
                      ],
                  icon: Icon(
                    Icons.more_vert,
                    color: AppColors.textSecondary,
                    size: 20.w,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showEditArticleForm(ArticleModel article) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => MultiBlocProvider(
              providers: [
                BlocProvider.value(value: context.read<ArticlesBloc>()),
                BlocProvider.value(
                  value: context.read<ArticleCategoriesBloc>(),
                ),
              ],
              child: ArticleFormPage(article: article, isEditing: true),
            ),
      ),
    );
  }

  void _deleteArticle(String articleId) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: const Text('هل أنت متأكد من حذف هذا المقال؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.of(context).pop(); // إغلاق dialog التأكيد

                  // عرض loading indicator
                  setState(() {
                    _isDeleteDialogOpen = true;
                  });

                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder:
                        (context) => AlertDialog(
                          content: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const CircularProgressIndicator(),
                              SizedBox(height: 16.h),
                              Text(
                                'جاري حذف المقال...',
                                style: TextStyle(fontSize: 16.sp),
                              ),
                            ],
                          ),
                        ),
                  );

                  // تنفيذ الحذف
                  context.read<ArticlesBloc>().add(
                    DeleteArticle(articleId: articleId),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                ),
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }

  void _showAddArticleForm() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => MultiBlocProvider(
              providers: [
                BlocProvider.value(value: context.read<ArticlesBloc>()),
                BlocProvider.value(
                  value: context.read<ArticleCategoriesBloc>(),
                ),
              ],
              child: const ArticleFormPage(),
            ),
      ),
    );
  }
}
