import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../core/constants/app_colors.dart';
import '../../../../../core/models/reminder_model.dart';
import '../../../../../core/widgets/loading_dialog.dart';
import '../../bloc/reminders_bloc.dart';
import '../../bloc/reminders_event.dart';
import '../../bloc/reminders_state.dart';
import '../../widgets/reminder_form_dialog.dart';
import '../../../../../core/services/notification_automation_service.dart';

class RemindersTab extends StatefulWidget {
  final String patientId;

  const RemindersTab({super.key, required this.patientId});

  @override
  State<RemindersTab> createState() => _RemindersTabState();
}

class _RemindersTabState extends State<RemindersTab>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late TabController _subTabController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _subTabController = TabController(length: 4, vsync: this);

    // Load reminders when widget is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<RemindersBloc>().add(
          LoadRemindersByPatientId(patientId: widget.patientId),
        );
      }
    });
  }

  @override
  void dispose() {
    _subTabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(50.h),
        child: AppBar(
          backgroundColor: AppColors.white,
          elevation: 0,
          automaticallyImplyLeading: false,
          bottom: TabBar(
            controller: _subTabController,
            labelColor: AppColors.primary,
            unselectedLabelColor: AppColors.textSecondary,
            indicatorColor: AppColors.primary,
            isScrollable: true,
            tabAlignment: TabAlignment.start,
            tabs: const [
              Tab(text: 'تذكير الوجبات'),
              Tab(text: 'النشاط البدني'),
              Tab(text: 'الأدوية'),
              Tab(text: 'شرب الماء'),
            ],
          ),
        ),
      ),
      body: BlocConsumer<RemindersBloc, RemindersState>(
        listener: (context, state) {
          if (state is RemindersLoading) {
            LoadingDialog.show(context, 'جاري التحميل...');
          } else if (state is RemindersLoaded) {
            LoadingDialog.hide(context);
          } else if (state is ReminderCreated) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إضافة التذكير والإشعار بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is ReminderUpdated) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم تحديث التذكير والإشعار بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is ReminderDeleted) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حذف التذكير والإشعار بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is ReminderStatusToggled) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  state.isActive
                      ? 'تم تفعيل التذكير بنجاح'
                      : 'تم إيقاف التذكير بنجاح',
                ),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is RemindersError) {
            LoadingDialog.hide(context);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          }
        },
        builder: (context, state) {
          return TabBarView(
            controller: _subTabController,
            children: [
              // Meal Reminders
              _buildMealRemindersTab(state),

              // Exercise Reminders
              _buildExerciseRemindersTab(state),

              // Medication Reminders
              _buildMedicationRemindersTab(state),

              // Water Reminders
              _buildWaterRemindersTab(state),
            ],
          );
        },
      ),
    );
  }

  Widget _buildMealRemindersTab(RemindersState state) {
    return Column(
      children: [
        _buildSubTabActionBar('إضافة تذكير وجبة', 'meal'),
        Expanded(child: _buildRemindersContent(state, 'meal')),
      ],
    );
  }

  Widget _buildExerciseRemindersTab(RemindersState state) {
    return Column(
      children: [
        _buildSubTabActionBar('إضافة تذكير نشاط', 'exercise'),
        Expanded(child: _buildRemindersContent(state, 'exercise')),
      ],
    );
  }

  Widget _buildMedicationRemindersTab(RemindersState state) {
    return Column(
      children: [
        _buildSubTabActionBar('إضافة تذكير دواء', 'medication'),
        Expanded(child: _buildRemindersContent(state, 'medication')),
      ],
    );
  }

  Widget _buildWaterRemindersTab(RemindersState state) {
    return Column(
      children: [
        _buildSubTabActionBar('إضافة تذكير ماء', 'water'),
        Expanded(child: _buildRemindersContent(state, 'water')),
      ],
    );
  }

  Widget _buildSubTabActionBar(String buttonText, String reminderType) {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.2),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Add button
          ElevatedButton.icon(
            onPressed: () => _showAddReminderDialog(reminderType),
            icon: const Icon(Icons.add, size: 20),
            label: Text(buttonText),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
          ),

          const Spacer(),

          // Test notification button
          IconButton(
            onPressed: () => _testNotification(reminderType),
            icon: const Icon(Icons.notification_add),
            tooltip: 'اختبار الإشعار',
          ),

          // Refresh button
          IconButton(
            onPressed: _refreshReminders,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
        ],
      ),
    );
  }

  Widget _buildRemindersContent(RemindersState state, String reminderType) {
    if (state is RemindersLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state is RemindersLoaded) {
      final filteredReminders = state.groupedByType[reminderType] ?? [];

      if (filteredReminders.isEmpty) {
        return _buildEmptyState(reminderType);
      }

      return _buildRemindersList(filteredReminders);
    }

    if (state is RemindersError) {
      return _buildErrorState(state.message);
    }

    return _buildInitialState(reminderType);
  }

  Widget _buildRemindersList(List<ReminderModel> reminders) {
    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: ListView.builder(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16.w),
        itemCount: reminders.length,
        itemBuilder: (context, index) {
          final reminder = reminders[index];
          return _buildReminderCard(reminder);
        },
      ),
    );
  }

  Widget _buildReminderCard(ReminderModel reminder) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray200),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and actions
          Row(
            children: [
              Icon(
                _getIconForReminderType(reminder.reminderType),
                size: 20.w,
                color: AppColors.primary,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  reminder.title,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              // Status indicator
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color:
                      reminder.isActive ? AppColors.success : AppColors.error,
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  reminder.isActive ? 'نشط' : 'غير نشط',
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      _showEditReminderDialog(reminder);
                      break;
                    case 'delete':
                      _deleteReminder(reminder);
                      break;
                    case 'toggle':
                      _toggleReminderStatus(reminder);
                      break;
                  }
                },
                itemBuilder:
                    (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 20),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'toggle',
                        child: Row(
                          children: [
                            Icon(
                              reminder.isActive
                                  ? Icons.pause
                                  : Icons.play_arrow,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(reminder.isActive ? 'إيقاف' : 'تفعيل'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 20, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
              ),
            ],
          ),

          if (reminder.description != null &&
              reminder.description!.isNotEmpty) ...[
            SizedBox(height: 8.h),
            Text(
              reminder.description!,
              style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
            ),
          ],

          SizedBox(height: 12.h),

          // Time and days info
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 16.w,
                      color: AppColors.primary,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      'الوقت: ${_formatTimeDisplay(reminder.reminderTime)}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 16.w,
                      color: AppColors.primary,
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Text(
                        'الأيام: ${_formatDaysOfWeek(reminder.daysOfWeek)}',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Icon(
                      Icons.notifications_active,
                      size: 16.w,
                      color: AppColors.success,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      'الإشعارات: مفعلة تلقائ<|im_start|>',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: AppColors.success,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String reminderType) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getIconForReminderType(reminderType),
            size: 64.w,
            color: AppColors.gray400,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد ${_getReminderTypeLabel(reminderType)}',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'ابدأ بإضافة أول ${_getReminderTypeLabel(reminderType)} للمريض',
            style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
          ),
          SizedBox(height: 16.h),
          ElevatedButton.icon(
            onPressed: () => _showAddReminderDialog(reminderType),
            icon: const Icon(Icons.add),
            label: Text('إضافة ${_getReminderTypeLabel(reminderType)}'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.w, color: AppColors.error),
          SizedBox(height: 16.h),
          Text(
            'حدث خطأ في تحميل البيانات',
            style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: TextStyle(fontSize: 14.sp, color: AppColors.error),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _refreshReminders,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialState(String reminderType) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getIconForReminderType(reminderType),
            size: 64.w,
            color: AppColors.gray400,
          ),
          SizedBox(height: 16.h),
          Text(
            'جاري تحميل ${_getReminderTypeLabel(reminderType)}...',
            style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _refreshReminders,
            child: const Text('تحميل البيانات'),
          ),
        ],
      ),
    );
  }

  IconData _getIconForReminderType(String reminderType) {
    switch (reminderType) {
      case 'meal':
        return Icons.restaurant;
      case 'exercise':
        return Icons.directions_run;
      case 'medication':
        return Icons.medication;
      case 'water':
        return Icons.water_drop;
      default:
        return Icons.notifications;
    }
  }

  String _getReminderTypeLabel(String reminderType) {
    switch (reminderType) {
      case 'meal':
        return 'تذكيرات الوجبات';
      case 'exercise':
        return 'تذكيرات النشاط';
      case 'medication':
        return 'تذكيرات الأدوية';
      case 'water':
        return 'تذكيرات الماء';
      default:
        return 'التذكيرات';
    }
  }

  String _formatTimeDisplay(String timeString) {
    try {
      final parts = timeString.split(':');
      final hour = int.parse(parts[0]);
      final minute = int.parse(parts[1]);
      final period = hour >= 12 ? 'م' : 'ص';
      final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);

      return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
    } catch (e) {
      return timeString;
    }
  }

  String _formatDaysOfWeek(List<int> daysOfWeek) {
    final dayNames = {
      1: 'الاثنين',
      2: 'الثلاثاء',
      3: 'الأربعاء',
      4: 'الخميس',
      5: 'الجمعة',
      6: 'السبت',
      7: 'الأحد',
    };

    if (daysOfWeek.length == 7) {
      return 'يومياً';
    }

    final sortedDays = List<int>.from(daysOfWeek)..sort();
    return sortedDays.map((day) => dayNames[day] ?? '').join(', ');
  }

  void _showAddReminderDialog(String reminderType) {
    showDialog(
      context: context,
      builder:
          (context) => BlocProvider.value(
            value: context.read<RemindersBloc>(),
            child: ReminderFormDialog(
              patientId: widget.patientId,
              reminderType: reminderType,
            ),
          ),
    );
  }

  void _showEditReminderDialog(ReminderModel reminder) {
    showDialog(
      context: context,
      builder:
          (context) => BlocProvider.value(
            value: context.read<RemindersBloc>(),
            child: ReminderFormDialog(
              patientId: widget.patientId,
              reminderType: reminder.reminderType,
              reminder: reminder,
            ),
          ),
    );
  }

  void _deleteReminder(ReminderModel reminder) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: Text('هل أنت متأكد من حذف تذكير "${reminder.title}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // Show loading dialog immediately
                  LoadingDialog.show(context, 'جاري حذف التذكير...');
                  context.read<RemindersBloc>().add(
                    DeleteReminder(
                      reminderId: reminder.id,
                      patientId: widget.patientId,
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                ),
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }

  void _toggleReminderStatus(ReminderModel reminder) {
    final newStatus = !reminder.isActive;
    // Show loading dialog immediately
    LoadingDialog.show(
      context,
      newStatus ? 'جاري تفعيل التذكير...' : 'جاري إيقاف التذكير...',
    );
    context.read<RemindersBloc>().add(
      ToggleReminderStatus(
        reminderId: reminder.id,
        isActive: newStatus,
        patientId: widget.patientId,
      ),
    );
  }

  void _refreshReminders() {
    context.read<RemindersBloc>().add(
      RefreshReminders(patientId: widget.patientId),
    );
  }

  Future<void> _onRefresh() async {
    _refreshReminders();
    // انتظار قصير لإظهار مؤشر التحديث
    await Future.delayed(const Duration(milliseconds: 500));
  }

  Future<void> _testNotification(String reminderType) async {
    try {
      LoadingDialog.show(context, 'جاري إرسال إشعار تجريبي...');

      String body = _getTestNotificationBody(reminderType);

      await NotificationAutomationService.sendTestNotification(
        patientId: widget.patientId,
        customMessage: body,
      );

      if (mounted) {
        LoadingDialog.hide(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال الإشعار التجريبي بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        LoadingDialog.hide(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إرسال الإشعار: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  String _getTestNotificationBody(String reminderType) {
    switch (reminderType) {
      case 'meal':
        return 'حان وقت تناول وجبتك! لا تنس اتباع النظام الغذائي المحدد.';
      case 'exercise':
        return 'حان وقت ممارسة النشاط البدني! ابدأ تمارينك الآن.';
      case 'medication':
        return 'حان وقت تناول الدواء! لا تنس جرعتك المحددة.';
      case 'water':
        return 'حان وقت شرب الماء! اشرب كوب من الماء الآن.';
      default:
        return 'هذا إشعار تجريبي من تطبيق Diet Rx';
    }
  }
}
