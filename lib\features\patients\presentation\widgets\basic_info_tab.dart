import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/utils/helpers.dart';

class BasicInfoTab extends StatelessWidget {
  final PatientModel patient;

  const BasicInfoTab({
    super.key,
    required this.patient,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Basic Information Card
          _buildInfoCard(
            title: 'المعلومات الأساسية',
            children: [
              _buildInfoRow(AppStrings.name, patient.name),
              if (patient.email != null)
                _buildInfoRow(AppStrings.email, patient.email!),
              if (patient.phone != null)
                _buildInfoRow('رقم الهاتف', patient.phone!),
              if (patient.age != null)
                _buildInfoRow(AppStrings.age, '${patient.age} سنة'),
              if (patient.gender != null)
                _buildInfoRow(AppStrings.gender, Helpers.getGenderText(patient.gender!)),
            ],
          ),
          SizedBox(height: 16.h),

          // Physical Measurements Card
          _buildInfoCard(
            title: 'القياسات الجسمية',
            children: [
              if (patient.height != null)
                _buildInfoRow(AppStrings.height, Helpers.formatHeight(patient.height!)),
              if (patient.weight != null)
                _buildInfoRow(AppStrings.weight, Helpers.formatWeight(patient.weight!)),
              if (patient.height != null && patient.weight != null)
                _buildInfoRow(
                  'مؤشر كتلة الجسم',
                  '${Helpers.calculateBMI(patient.weight!, patient.height!).toStringAsFixed(1)} - ${Helpers.getBMICategory(Helpers.calculateBMI(patient.weight!, patient.height!))}',
                ),
            ],
          ),
          SizedBox(height: 16.h),

          // Weekly Results Section
          _buildWeeklyResultsSection(),
          SizedBox(height: 16.h),

          // Action Buttons
          _buildActionButtons(context),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required String title,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 12.h),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeeklyResultsSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                AppStrings.weeklyResults,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              TextButton.icon(
                onPressed: () {
                  // Add new weekly result
                },
                icon: Icon(
                  Icons.add,
                  size: 16.w,
                ),
                label: const Text('إضافة نتيجة'),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          
          // Weekly Results List (Placeholder)
          Container(
            height: 200.h,
            decoration: BoxDecoration(
              color: AppColors.gray50,
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: AppColors.border),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.timeline,
                    size: 48.w,
                    color: AppColors.gray400,
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'لا توجد نتائج أسبوعية',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    'اضغط "إضافة نتيجة" لإضافة قياسات جديدة',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textHint,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: SizedBox(
            height: 32.h, // ارتفاع أقل
            child: OutlinedButton.icon(
              onPressed: () {
                // Edit patient info
              },
              style: OutlinedButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 0),
              ),
              icon: Icon(
                Icons.edit,
                size: 14.w,
              ),
              label: Text(
                'تعديل البيانات',
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: SizedBox(
            height: 32.h, // ارتفاع أقل
            child: ElevatedButton.icon(
              onPressed: () {
                // Add weekly result
              },
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 0),
              ),
              icon: Icon(
                Icons.add_chart,
                size: 14.w,
              ),
              label: Text(
                'إضافة قياسات',
                style: TextStyle(fontSize: 12.sp),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
