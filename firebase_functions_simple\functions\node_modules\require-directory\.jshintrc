{"maxerr": 50, "bitwise": true, "camelcase": true, "curly": true, "eqeqeq": true, "forin": true, "immed": true, "indent": 2, "latedef": true, "newcap": true, "noarg": true, "noempty": true, "nonew": true, "plusplus": true, "quotmark": true, "undef": true, "unused": true, "strict": true, "trailing": true, "maxparams": false, "maxdepth": false, "maxstatements": false, "maxcomplexity": false, "maxlen": false, "asi": false, "boss": false, "debug": false, "eqnull": true, "es5": false, "esnext": false, "moz": false, "evil": false, "expr": true, "funcscope": true, "globalstrict": true, "iterator": true, "lastsemic": false, "laxbreak": false, "laxcomma": false, "loopfunc": false, "multistr": false, "proto": false, "scripturl": false, "smarttabs": false, "shadow": false, "sub": false, "supernew": false, "validthis": false, "browser": true, "couch": false, "devel": true, "dojo": false, "jquery": false, "mootools": false, "node": true, "nonstandard": false, "prototypejs": false, "rhino": false, "worker": false, "wsh": false, "yui": false, "nomen": true, "onevar": true, "passfail": false, "white": true}