-- إعد<PERSON> Cron Job لإرسال الإشعارات المجدولة كل دقيقة
-- يتم تنفيذ هذا في Supabase SQL Editor

-- 1. تفعيل pg_cron extension (إذا لم تكن مفعلة)
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- 2. إنشاء Cron Job لإرسال الإشعارات كل دقيقة
SELECT cron.schedule(
  'send-scheduled-notifications', -- اسم المهمة
  '* * * * *', -- كل دقيقة (minute hour day month day_of_week)
  $$
  SELECT net.http_post(
    url := 'https://xwxeauofbzedfzaogzzy.supabase.co/functions/v1/send-notifications-simple',
    headers := '{"Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh3eGVhdW9mYnplZGZ6YW9nenp5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNjUyMTYsImV4cCI6MjA2NDc0MTIxNn0.YQMaXJELFdOOKOJBZJZJQJZJQJZJQJZJQJZJQJZJQJZ"}'::jsonb,
    body := '{}'::jsonb
  );
  $$
);

-- 3. التحقق من المهام المجدولة
SELECT * FROM cron.job;

-- 4. مراقبة تنفيذ المهام
SELECT * FROM cron.job_run_details 
WHERE jobname = 'send-scheduled-notifications' 
ORDER BY start_time DESC 
LIMIT 10;

-- 5. لحذف المهمة (إذا احتجت)
-- SELECT cron.unschedule('send-scheduled-notifications');

-- 6. لتعديل توقيت المهمة (مثال: كل 5 دقائق)
-- SELECT cron.alter_job('send-scheduled-notifications', schedule := '*/5 * * * *');

-- ملاحظات:
-- * * * * * = كل دقيقة
-- */5 * * * * = كل 5 دقائق  
-- 0 * * * * = كل ساعة عند الدقيقة 0
-- 0 8 * * * = كل يوم في الساعة 8:00 صباحاً

-- مثال لجدولة متقدمة:
-- SELECT cron.schedule(
--   'morning-notifications',
--   '0 8 * * *', -- كل يوم في الساعة 8:00 صباحاً
--   $$ SELECT net.http_post(...); $$
-- );

-- SELECT cron.schedule(
--   'evening-notifications', 
--   '0 20 * * *', -- كل يوم في الساعة 8:00 مساءً
--   $$ SELECT net.http_post(...); $$
-- );
