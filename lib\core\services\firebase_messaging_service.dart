import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:googleapis_auth/auth_io.dart';
import 'package:flutter/services.dart';

class FirebaseMessagingService {
  static const String _projectId = 'deit-rx-30741';
  static const String _fcmUrl =
      'https://fcm.googleapis.com/v1/projects/$_projectId/messages:send';

  // Load service account key
  static Future<String> _getAccessToken() async {
    try {
      // Load the service account key from assets
      final serviceAccountJson = await rootBundle.loadString(
        'assets/key/deit-rx-30741-5972f5ebce39.json',
      );
      final serviceAccount = json.decode(serviceAccountJson);

      // Create credentials
      final credentials = ServiceAccountCredentials.fromJson(serviceAccount);

      // Get access token
      final client = await clientViaServiceAccount(credentials, [
        'https://www.googleapis.com/auth/firebase.messaging',
      ]);

      final accessToken = client.credentials.accessToken.data;
      client.close();

      return accessToken;
    } catch (e) {
      throw Exception('Failed to get access token: $e');
    }
  }

  // Send notification to a specific FCM token
  static Future<bool> sendNotification({
    required String fcmToken,
    required String title,
    required String body,
    Map<String, String>? data,
  }) async {
    try {
      final accessToken = await _getAccessToken();

      // Convert all data values to strings (FCM requirement)
      final stringData = <String, String>{};
      data?.forEach((key, value) {
        stringData[key] = value.toString();
      });

      final message = {
        'message': {
          'token': fcmToken,
          'notification': {'title': title, 'body': body},
          'data': stringData,
          'android': {
            'priority': 'high',
            'ttl': '3600s',
            'notification': {
              'title': title,
              'body': body,
              'channel_id': 'diet_rx_notifications',
              'sound': 'default',
              'default_sound': true,
              'default_vibrate_timings': true,
              'notification_priority': 'PRIORITY_HIGH',
              'visibility': 'PUBLIC',
              'icon': 'ic_notification',
            },
          },
          'apns': {
            'headers': {'apns-priority': '10'},
            'payload': {
              'aps': {
                'alert': {'title': title, 'body': body},
                'sound': 'default',
                'badge': 1,
              },
            },
          },
        },
      };

      print('🚀 Sending FCM notification to: $fcmToken');
      print('📝 Message payload: ${json.encode(message)}');

      final response = await http.post(
        Uri.parse(_fcmUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        },
        body: json.encode(message),
      );

      print('📡 FCM Response Status: ${response.statusCode}');
      print('📡 FCM Response Body: ${response.body}');

      if (response.statusCode == 200) {
        print('✅ Notification sent successfully to $fcmToken');
        return true;
      } else {
        print(
          '❌ Failed to send notification: ${response.statusCode} - ${response.body}',
        );
        return false;
      }
    } catch (e) {
      print('❌ Error sending notification: $e');
      return false;
    }
  }

  // Send notification to multiple tokens
  static Future<Map<String, bool>> sendNotificationToMultipleTokens({
    required List<String> fcmTokens,
    required String title,
    required String body,
    Map<String, String>? data,
  }) async {
    final results = <String, bool>{};

    for (final token in fcmTokens) {
      final success = await sendNotification(
        fcmToken: token,
        title: title,
        body: body,
        data: data,
      );
      results[token] = success;
    }

    return results;
  }

  // Send personalized notification (with patient-specific content)
  static Future<bool> sendPersonalizedNotification({
    required String fcmToken,
    required String patientName,
    required String notificationType,
    required String content,
    Map<String, String>? additionalData,
  }) async {
    String title;
    String body;

    switch (notificationType) {
      case 'meal':
        title = '🍽️ تذكير الوجبة';
        body = 'مرحباً $patientName، حان وقت $content';
        break;
      case 'exercise':
        title = '🏃‍♂️ تذكير النشاط البدني';
        body = 'مرحباً $patientName، حان وقت $content';
        break;
      case 'medication':
        title = '💊 تذكير الدواء';
        body = 'مرحباً $patientName، حان وقت تناول $content';
        break;
      case 'water':
        title = '💧 تذكير شرب الماء';
        body = 'مرحباً $patientName، $content';
        break;
      default:
        title = '🔔 تذكير من Diet Rx';
        body = 'مرحباً $patientName، $content';
    }

    final data = {
      'type': notificationType,
      'patient_name': patientName,
      'content': content,
      ...?additionalData,
    };

    return await sendNotification(
      fcmToken: fcmToken,
      title: title,
      body: body,
      data: data,
    );
  }
}
