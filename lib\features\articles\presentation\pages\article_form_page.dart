import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/article_model.dart';
import '../../../../core/widgets/loading_dialog.dart';
import '../bloc/article_categories_bloc.dart';
import '../bloc/article_categories_event.dart';
import '../bloc/article_categories_state.dart';
import '../bloc/articles_bloc.dart';
import '../bloc/articles_event.dart';
import '../bloc/articles_state.dart';

class ArticleFormPage extends StatefulWidget {
  final ArticleModel? article;
  final bool isEditing;

  const ArticleFormPage({super.key, this.article, this.isEditing = false});

  @override
  State<ArticleFormPage> createState() => _ArticleFormPageState();
}

class _ArticleFormPageState extends State<ArticleFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _authorController = TextEditingController();
  final _referenceController = TextEditingController(); // حقل المرجع

  String? _selectedCategoryId;
  XFile? _selectedImage;
  PlatformFile? _selectedPdf;
  final ImagePicker _imagePicker = ImagePicker();
  bool _isPublished = false;
  bool _imageDeleted = false;
  bool _pdfDeleted = false;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();

    // Load categories
    context.read<ArticleCategoriesBloc>().add(LoadActiveArticleCategories());

    // Initialize form if editing
    if (widget.isEditing && widget.article != null) {
      _titleController.text = widget.article!.title;
      _contentController.text = widget.article!.content;
      _authorController.text = widget.article!.author;
      _referenceController.text =
          widget.article!.reference ?? ''; // تهيئة حقل المرجع
      _selectedCategoryId = widget.article!.categoryId;
      _isPublished = widget.article!.isPublished;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _authorController.dispose();
    _referenceController.dispose(); // تنظيف controller المرجع
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ArticlesBloc, ArticlesState>(
      listener: (context, state) {
        if (state is ArticlesLoaded && _isProcessing) {
          // Close loading dialog
          LoadingDialog.hide(context);

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.isEditing
                    ? 'تم تحديث المقال بنجاح'
                    : 'تم حفظ المقال بنجاح',
              ),
              backgroundColor: AppColors.success,
            ),
          );

          // Reset processing flag
          setState(() {
            _isProcessing = false;
          });

          // Close form page
          Navigator.of(context).pop();
        } else if (state is ArticlesError && _isProcessing) {
          // Close loading dialog
          LoadingDialog.hide(context);

          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حفظ المقال: ${state.message}'),
              backgroundColor: AppColors.error,
            ),
          );

          // Reset processing flag
          setState(() {
            _isProcessing = false;
          });
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          title: Text(widget.isEditing ? 'تعديل المقال' : 'إضافة مقال جديد'),
          backgroundColor: AppColors.white,
          elevation: 0,
          actions: [
            TextButton(
              onPressed: _saveArticle,
              child: Text(
                widget.isEditing ? 'تحديث' : 'حفظ',
                style: TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                  fontSize: 16.sp,
                ),
              ),
            ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(20.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Article Title
                _buildSectionTitle('عنوان المقال *'),
                SizedBox(height: 8.h),
                TextFormField(
                  controller: _titleController,
                  decoration: InputDecoration(
                    hintText: 'أدخل عنوان المقال',
                    prefixIcon: const Icon(Icons.title),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'يرجى إدخال عنوان المقال';
                    }
                    return null;
                  },
                ),

                SizedBox(height: 20.h),

                // Content
                _buildSectionTitle('محتوى المقال *'),
                SizedBox(height: 8.h),
                TextFormField(
                  controller: _contentController,
                  maxLines: 8,
                  decoration: InputDecoration(
                    hintText: 'أدخل محتوى المقال',
                    prefixIcon: const Icon(Icons.article),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  validator: (value) {
                    if (value?.isEmpty ?? true) {
                      return 'يرجى إدخال محتوى المقال';
                    }
                    return null;
                  },
                ),

                SizedBox(height: 20.h),

                // Author and Category Row
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildSectionTitle('الكاتب *'),
                          SizedBox(height: 8.h),
                          TextFormField(
                            controller: _authorController,
                            decoration: InputDecoration(
                              hintText: 'اسم الكاتب',
                              prefixIcon: const Icon(Icons.person),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                            ),
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return 'يرجى إدخال اسم الكاتب';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildSectionTitle('القسم *'),
                          SizedBox(height: 8.h),
                          BlocBuilder<
                            ArticleCategoriesBloc,
                            ArticleCategoriesState
                          >(
                            builder: (context, state) {
                              if (state is ArticleCategoriesLoaded) {
                                return DropdownButtonFormField<String>(
                                  value: _selectedCategoryId,
                                  isExpanded: true, // This prevents overflow
                                  decoration: InputDecoration(
                                    hintText: 'اختر القسم',
                                    prefixIcon: const Icon(Icons.category),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12.r),
                                    ),
                                  ),
                                  items:
                                      state.categories.map((category) {
                                        return DropdownMenuItem<String>(
                                          value: category.id,
                                          child: Text(
                                            category.name,
                                            overflow:
                                                TextOverflow
                                                    .ellipsis, // Handle long text
                                          ),
                                        );
                                      }).toList(),
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedCategoryId = value;
                                    });
                                  },
                                  validator: (value) {
                                    if (value?.isEmpty ?? true) {
                                      return 'يرجى اختيار القسم';
                                    }
                                    return null;
                                  },
                                );
                              }
                              return const CircularProgressIndicator();
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 20.h),

                // Image Section
                _buildSectionTitle('صورة المقال *'),
                SizedBox(height: 8.h),
                _buildImageSection(),

                SizedBox(height: 20.h),

                // PDF Section
                _buildSectionTitle('ملف PDF (اختياري)'),
                SizedBox(height: 8.h),
                _buildPdfSection(),

                SizedBox(height: 20.h),

                // Reference Section
                _buildSectionTitle('مرجع المقال (اختياري)'),
                SizedBox(height: 8.h),
                TextFormField(
                  controller: _referenceController,
                  decoration: InputDecoration(
                    hintText: 'أدخل رابط أو مرجع المقال (اختياري)',
                    prefixIcon: const Icon(Icons.link),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    helperText: 'يمكنك إضافة رابط أو مرجع للمقال',
                    helperStyle: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  maxLines: 2,
                  // لا يوجد validator لأن الحقل اختياري
                ),

                SizedBox(height: 20.h),

                // Published Status
                Row(
                  children: [
                    Switch(
                      value: _isPublished,
                      onChanged: (value) {
                        setState(() {
                          _isPublished = value;
                        });
                      },
                    ),
                    SizedBox(width: 12.w),
                    Text(
                      'نشر المقال',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 40.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: title.contains('*') ? title.replaceAll('*', '') : title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          if (title.contains('*'))
            TextSpan(
              text: ' *',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.error,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildImageSection() {
    // تحديد ما إذا كانت الصورة مطلوبة ومفقودة
    bool isImageRequired =
        _selectedImage == null &&
        (widget.article?.imageUrl == null || _imageDeleted);

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color:
              isImageRequired
                  ? AppColors.error.withValues(alpha: 0.5)
                  : AppColors.gray300,
          width: isImageRequired ? 2 : 1,
        ),
      ),
      child: Column(
        children: [
          // Selected Image Preview
          if (_selectedImage != null) ...[
            Container(
              width: double.infinity,
              height: 200.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: AppColors.gray300),
              ),
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8.r),
                    child: Image.file(
                      File(_selectedImage!.path),
                      width: double.infinity,
                      height: 200.h,
                      fit: BoxFit.cover,
                    ),
                  ),
                  Positioned(
                    top: 8.w,
                    right: 8.w,
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedImage = null;
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.all(6.w),
                        decoration: const BoxDecoration(
                          color: AppColors.error,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.close,
                          color: AppColors.white,
                          size: 16.w,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 16.h),
          ] else if (widget.article?.imageUrl != null && !_imageDeleted) ...[
            Container(
              width: double.infinity,
              height: 200.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: AppColors.gray300),
              ),
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8.r),
                    child: Image.network(
                      widget.article!.imageUrl!,
                      width: double.infinity,
                      height: 200.h,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: AppColors.gray100,
                          child: Icon(
                            Icons.image_not_supported,
                            size: 48.w,
                            color: AppColors.gray400,
                          ),
                        );
                      },
                    ),
                  ),
                  Positioned(
                    top: 8.w,
                    right: 8.w,
                    child: GestureDetector(
                      onTap: () {
                        _confirmDeleteImage();
                      },
                      child: Container(
                        padding: EdgeInsets.all(6.w),
                        decoration: const BoxDecoration(
                          color: AppColors.error,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.close,
                          color: AppColors.white,
                          size: 16.w,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 16.h),
          ],

          // Add/Change Image Button
          ElevatedButton.icon(
            onPressed: _pickImage,
            icon: const Icon(Icons.add_photo_alternate),
            label: Text(
              _selectedImage == null &&
                      (widget.article?.imageUrl == null || _imageDeleted)
                  ? 'إضافة صورة (مطلوبة)'
                  : 'تغيير الصورة',
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  isImageRequired
                      ? AppColors.error.withValues(alpha: 0.1)
                      : AppColors.primary.withValues(alpha: 0.1),
              foregroundColor:
                  isImageRequired ? AppColors.error : AppColors.primary,
              elevation: 0,
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
            ),
          ),

          // رسالة تحذيرية عند عدم وجود صورة
          if (isImageRequired) ...[
            SizedBox(height: 8.h),
            Text(
              'الصورة مطلوبة للمقال',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.error,
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _pickImage() async {
    final XFile? image = await _imagePicker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 1024,
      maxHeight: 1024,
      imageQuality: 80,
    );

    if (image != null) {
      setState(() {
        _selectedImage = image;
      });
    }
  }

  void _confirmDeleteImage() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد حذف الصورة'),
            content: const Text(
              'هل أنت متأكد من حذف هذه الصورة؟ سيتم حذفها نهائياً من التخزين.\n\nتذكر: الصورة مطلوبة للمقال، ستحتاج لإضافة صورة جديدة.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _imageDeleted = true;
                  });
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                ),
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }

  Widget _buildPdfSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.gray300),
      ),
      child: Column(
        children: [
          // Selected PDF Preview
          if (_selectedPdf != null) ...[
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: AppColors.gray50,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: AppColors.gray300),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.picture_as_pdf,
                    color: AppColors.error,
                    size: 32.w,
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _selectedPdf!.name,
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                            color: AppColors.textPrimary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          '${(_selectedPdf!.size / 1024 / 1024).toStringAsFixed(2)} MB',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _selectedPdf = null;
                      });
                    },
                    icon: Icon(Icons.close, color: AppColors.error, size: 20.w),
                  ),
                ],
              ),
            ),
            SizedBox(height: 16.h),
          ] else if (widget.article?.pdfUrl != null && !_pdfDeleted) ...[
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: AppColors.gray50,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: AppColors.gray300),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.picture_as_pdf,
                    color: AppColors.error,
                    size: 32.w,
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'ملف PDF موجود',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          'اضغط لعرض الملف',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _pdfDeleted = true;
                      });
                    },
                    icon: Icon(Icons.close, color: AppColors.error, size: 20.w),
                  ),
                ],
              ),
            ),
            SizedBox(height: 16.h),
          ],

          // Add/Change PDF Button
          ElevatedButton.icon(
            onPressed: _pickPdf,
            icon: const Icon(Icons.upload_file),
            label: Text(
              _selectedPdf == null &&
                      (widget.article?.pdfUrl == null || _pdfDeleted)
                  ? 'إضافة ملف PDF (اختياري)'
                  : 'تغيير ملف PDF',
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary.withValues(alpha: 0.1),
              foregroundColor: AppColors.primary,
              elevation: 0,
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
            ),
          ),

          SizedBox(height: 8.h),
          Text(
            'يمكنك إضافة ملف PDF كمرفق للمقال (اختياري)',
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textSecondary,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _pickPdf() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['pdf'],
      allowMultiple: false,
    );

    if (result != null && result.files.isNotEmpty) {
      final file = result.files.first;

      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت'),
              backgroundColor: AppColors.error,
            ),
          );
        }
        return;
      }

      setState(() {
        _selectedPdf = file;
        _pdfDeleted = false;
      });
    }
  }

  bool _validateImage() {
    // التحقق من وجود صورة
    bool hasImage = false;

    if (_selectedImage != null) {
      // يوجد صورة جديدة محددة
      hasImage = true;
    } else if (widget.isEditing &&
        widget.article?.imageUrl != null &&
        !_imageDeleted) {
      // في حالة التحديث ويوجد صورة موجودة ولم يتم حذفها
      hasImage = true;
    }

    if (!hasImage) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إضافة صورة للمقال'),
          backgroundColor: AppColors.error,
        ),
      );
      return false;
    }

    return true;
  }

  void _saveArticle() async {
    if (_formKey.currentState!.validate() && _validateImage()) {
      // Set processing flag
      setState(() {
        _isProcessing = true;
      });

      // Show loading indicator
      String loadingMessage;
      bool hasImageChanges = _imageDeleted || _selectedImage != null;
      bool hasPdfChanges = _pdfDeleted || _selectedPdf != null;

      if (widget.isEditing) {
        if (hasImageChanges && hasPdfChanges) {
          loadingMessage = 'جاري تحديث المقال والملفات...';
        } else if (hasImageChanges) {
          loadingMessage = 'جاري تحديث المقال والصورة...';
        } else if (hasPdfChanges) {
          loadingMessage = 'جاري تحديث المقال وملف PDF...';
        } else {
          loadingMessage = 'جاري تحديث المقال...';
        }
      } else {
        if (_selectedImage != null && _selectedPdf != null) {
          loadingMessage = 'جاري حفظ المقال ورفع الملفات...';
        } else if (_selectedImage != null) {
          loadingMessage = 'جاري حفظ المقال ورفع الصورة...';
        } else if (_selectedPdf != null) {
          loadingMessage = 'جاري حفظ المقال ورفع ملف PDF...';
        } else {
          loadingMessage = 'جاري حفظ المقال...';
        }
      }

      LoadingDialog.show(context, loadingMessage);
      // Determine final image URL
      String? finalImageUrl;
      if (_imageDeleted) {
        finalImageUrl = null; // Remove image
      } else if (_selectedImage != null) {
        finalImageUrl = null; // Will be set after upload
      } else {
        finalImageUrl = widget.article?.imageUrl; // Keep existing
      }

      // Determine final PDF URL
      String? finalPdfUrl;
      if (_pdfDeleted) {
        finalPdfUrl = null; // Remove PDF
      } else if (_selectedPdf != null) {
        finalPdfUrl = null; // Will be set after upload
      } else {
        finalPdfUrl = widget.article?.pdfUrl; // Keep existing
      }

      final article = ArticleModel(
        id: widget.article?.id ?? '',
        title: _titleController.text.trim(),
        content: _contentController.text.trim(),
        author: _authorController.text.trim(), // الآن مطلوب وليس null
        categoryId: _selectedCategoryId,
        reference:
            _referenceController.text.trim().isEmpty
                ? null
                : _referenceController.text.trim(), // حقل المرجع
        isPublished: _isPublished,
        isFeatured: widget.article?.isFeatured ?? false,
        createdAt: widget.article?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
        imageUrl: finalImageUrl,
        pdfUrl: finalPdfUrl,
      );

      if (widget.isEditing) {
        // Use the new method that handles both article update and file upload/deletion
        context.read<ArticlesBloc>().add(
          UpdateArticleWithFiles(
            article: article,
            image: _selectedImage,
            pdf: _selectedPdf,
            deleteOldImage: _imageDeleted,
            oldImageUrl: widget.article?.imageUrl,
            deleteOldPdf: _pdfDeleted,
            oldPdfUrl: widget.article?.pdfUrl,
          ),
        );
      } else {
        context.read<ArticlesBloc>().add(
          CreateArticleWithFiles(
            article: article,
            image: _selectedImage,
            pdf: _selectedPdf,
          ),
        );
      }
    }
  }
}
