import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/category_model.dart';

class CategoriesRepository {
  Future<List<CategoryModel>> getAllCategories() async {
    try {
      final response = await SupabaseConfig.categories
          .select()
          .eq('is_active', true)
          .order('name', ascending: true);
      
      return response.map<CategoryModel>((json) => CategoryModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في جلب الفئات: ${e.toString()}');
    }
  }

  Future<CategoryModel> getCategoryById(String id) async {
    try {
      final response = await SupabaseConfig.categories
          .select()
          .eq('id', id)
          .single();
      
      return CategoryModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في جلب الفئة: ${e.toString()}');
    }
  }

  Future<CategoryModel> createCategory(CategoryModel category) async {
    try {
      final response = await SupabaseConfig.categories
          .insert({
            'name': category.name,
            'description': category.description,
            'icon': category.icon,
            'color': category.color,
            'is_active': category.isActive,
          })
          .select()
          .single();
      
      return CategoryModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في إنشاء الفئة: ${e.toString()}');
    }
  }

  Future<CategoryModel> updateCategory(CategoryModel category) async {
    try {
      final response = await SupabaseConfig.categories
          .update({
            'name': category.name,
            'description': category.description,
            'icon': category.icon,
            'color': category.color,
            'is_active': category.isActive,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', category.id)
          .select()
          .single();
      
      return CategoryModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في تحديث الفئة: ${e.toString()}');
    }
  }

  Future<void> deleteCategory(String categoryId) async {
    try {
      await SupabaseConfig.categories
          .delete()
          .eq('id', categoryId);
    } catch (e) {
      throw Exception('فشل في حذف الفئة: ${e.toString()}');
    }
  }

  Future<CategoryModel> toggleCategoryStatus(String id) async {
    try {
      // First get the current status
      final current = await getCategoryById(id);
      
      // Toggle the status
      final response = await SupabaseConfig.categories
          .update({'is_active': !current.isActive})
          .eq('id', id)
          .select()
          .single();
      
      return CategoryModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في تغيير حالة الفئة: ${e.toString()}');
    }
  }

  Future<List<CategoryModel>> searchCategories(String query) async {
    try {
      final response = await SupabaseConfig.categories
          .select()
          .or('name.ilike.%$query%,description.ilike.%$query%')
          .eq('is_active', true)
          .order('name', ascending: true);
      
      return response.map<CategoryModel>((json) => CategoryModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في البحث عن الفئات: ${e.toString()}');
    }
  }

  Future<int> getCategoryProductsCount(String categoryId) async {
    try {
      final response = await SupabaseConfig.products
          .select('id')
          .eq('category_id', categoryId)
          .eq('is_active', true);

      return response.length;
    } catch (e) {
      throw Exception('فشل في جلب عدد منتجات الفئة: ${e.toString()}');
    }
  }
}
