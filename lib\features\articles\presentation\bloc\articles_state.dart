import 'package:equatable/equatable.dart';
import '../../../../core/models/article_model.dart';

abstract class ArticlesState extends Equatable {
  const ArticlesState();

  @override
  List<Object?> get props => [];
}

class ArticlesInitial extends ArticlesState {}

class ArticlesLoading extends ArticlesState {}

class ArticlesLoaded extends ArticlesState {
  final List<ArticleModel> articles;
  final List<ArticleModel> filteredArticles;
  final List<ArticleModel> publishedArticles;
  final List<ArticleModel> draftArticles;
  final List<ArticleModel> featuredArticles;
  final List<String> categories;
  final String selectedCategory;
  final bool isSearching;
  final String searchQuery;

  const ArticlesLoaded({
    required this.articles,
    required this.filteredArticles,
    required this.publishedArticles,
    required this.draftArticles,
    required this.featuredArticles,
    required this.categories,
    this.selectedCategory = 'الكل',
    this.isSearching = false,
    this.searchQuery = '',
  });

  @override
  List<Object?> get props => [
        articles,
        filteredArticles,
        publishedArticles,
        draftArticles,
        featuredArticles,
        categories,
        selectedCategory,
        isSearching,
        searchQuery,
      ];

  ArticlesLoaded copyWith({
    List<ArticleModel>? articles,
    List<ArticleModel>? filteredArticles,
    List<ArticleModel>? publishedArticles,
    List<ArticleModel>? draftArticles,
    List<ArticleModel>? featuredArticles,
    List<String>? categories,
    String? selectedCategory,
    bool? isSearching,
    String? searchQuery,
  }) {
    return ArticlesLoaded(
      articles: articles ?? this.articles,
      filteredArticles: filteredArticles ?? this.filteredArticles,
      publishedArticles: publishedArticles ?? this.publishedArticles,
      draftArticles: draftArticles ?? this.draftArticles,
      featuredArticles: featuredArticles ?? this.featuredArticles,
      categories: categories ?? this.categories,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      isSearching: isSearching ?? this.isSearching,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

class ArticlesError extends ArticlesState {
  final String message;

  const ArticlesError({required this.message});

  @override
  List<Object?> get props => [message];
}

class ArticleCreated extends ArticlesState {
  final ArticleModel article;

  const ArticleCreated({required this.article});

  @override
  List<Object?> get props => [article];
}

class ArticleUpdated extends ArticlesState {
  final ArticleModel article;

  const ArticleUpdated({required this.article});

  @override
  List<Object?> get props => [article];
}

class ArticleDeleted extends ArticlesState {
  final String articleId;

  const ArticleDeleted({required this.articleId});

  @override
  List<Object?> get props => [articleId];
}

class ArticlePublished extends ArticlesState {
  final ArticleModel article;

  const ArticlePublished({required this.article});

  @override
  List<Object?> get props => [article];
}

class ArticleUnpublished extends ArticlesState {
  final ArticleModel article;

  const ArticleUnpublished({required this.article});

  @override
  List<Object?> get props => [article];
}
