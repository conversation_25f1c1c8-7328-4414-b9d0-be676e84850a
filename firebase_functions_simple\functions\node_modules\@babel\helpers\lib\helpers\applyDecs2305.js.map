{"version": 3, "names": ["_checkInRHS", "require", "_setFunctionName", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "applyDecs2305", "targetClass", "memberDecs", "classDecs", "classDecsHaveThis", "instanceBrand", "parentClass", "_bindPropCall", "obj", "name", "before", "_this", "value", "call", "runInitializers", "initializers", "i", "length", "assertCallable", "fn", "hint1", "hint2", "throwUndefined", "TypeError", "applyDec", "Class", "decInfo", "decoratorsHaveThis", "kind", "metadata", "ret", "isStatic", "isPrivate", "isField", "isAccessor", "hasPrivateBrand", "assertInstanceIfPrivate", "target", "decs", "decVal", "_", "isClass", "Array", "isArray", "desc", "init", "key", "get", "setFunctionName", "set", "Object", "getOwnPropertyDescriptor", "newValue", "dec", "decT<PERSON>", "decoratorFinishedRef", "ctx", "addInitializer", "initializer", "v", "Error", "push", "bind", "access", "has", "instance", "defineProperty", "applyMemberDecs", "decInfos", "protoInitializers", "staticInitializers", "staticBrand", "checkInRHS", "existingNonFields", "Map", "pushInitializers", "existingKind", "prototype", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defineMetadata", "Symbol", "configurable", "enumerable", "arguments", "parentMetadata", "create", "e", "c"], "sources": ["../../src/helpers/applyDecs2305.ts"], "sourcesContent": ["/* @minVersion 7.21.0 */\n/* @mangleFns */\n/* @onlyBabel7 */\n\nimport checkInRHS from \"./checkInRHS.ts\";\nimport setFunctionName from \"./setFunctionName.ts\";\nimport toPropertyKey from \"./toPropertyKey.ts\";\n\nconst enum PROP_KIND {\n  FIELD = 0,\n  ACCESSOR = 1,\n  METHOD = 2,\n  GETTER = 3,\n  SETTER = 4,\n  CLASS = 5,\n\n  STATIC = 8,\n\n  DECORATORS_HAVE_THIS = 16,\n}\n\ntype DecoratorFinishedRef = { v?: boolean };\ntype DecoratorContextAccess = {\n  get?: (target: object) => any;\n  set?: (target: object, value: any) => void;\n  has: (target: object) => boolean;\n};\ntype DecoratorContext = {\n  kind: \"accessor\" | \"method\" | \"getter\" | \"setter\" | \"field\" | \"class\";\n  name: string;\n  static?: boolean;\n  private?: boolean;\n  access?: DecoratorContextAccess;\n  metadata?: any;\n  addInitializer?: (initializer: Function) => void;\n};\ntype DecoratorInfo =\n  | [\n      decs: Function | Function[],\n      kind: PROP_KIND,\n      name: string,\n      any?,\n      Function?,\n    ]\n  | [classDecs: Function[]];\n\n/**\n  Basic usage:\n\n  applyDecs(\n    Class,\n    [\n      // member decorators\n      [\n        decs,               // dec, or array of decs, or array of this values and decs\n        0,                  // kind of value being decorated\n        'prop',             // name of public prop on class containing the value being decorated,\n        '#p',               // the name of the private property (if is private, void 0 otherwise),\n      ]\n    ],\n    [\n      // class decorators\n      dec1, dec2\n    ]\n  )\n  ```\n\n  Fully transpiled example:\n\n  ```js\n  @dec\n  class Class {\n    @dec\n    a = 123;\n\n    @dec\n    #a = 123;\n\n    @dec\n    @dec2\n    accessor b = 123;\n\n    @dec\n    accessor #b = 123;\n\n    @dec\n    c() { console.log('c'); }\n\n    @dec\n    #c() { console.log('privC'); }\n\n    @dec\n    get d() { console.log('d'); }\n\n    @dec\n    get #d() { console.log('privD'); }\n\n    @dec\n    set e(v) { console.log('e'); }\n\n    @dec\n    set #e(v) { console.log('privE'); }\n  }\n\n\n  // becomes\n  let initializeInstance;\n  let initializeClass;\n\n  let initA;\n  let initPrivA;\n\n  let initB;\n  let initPrivB, getPrivB, setPrivB;\n\n  let privC;\n  let privD;\n  let privE;\n\n  let Class;\n  class _Class {\n    static {\n      let ret = applyDecs(\n        this,\n        [\n          [dec, 0, 'a'],\n          [dec, 0, 'a', (i) => i.#a, (i, v) => i.#a = v],\n          [[dec, dec2], 1, 'b'],\n          [dec, 1, 'b', (i) => i.#privBData, (i, v) => i.#privBData = v],\n          [dec, 2, 'c'],\n          [dec, 2, 'c', () => console.log('privC')],\n          [dec, 3, 'd'],\n          [dec, 3, 'd', () => console.log('privD')],\n          [dec, 4, 'e'],\n          [dec, 4, 'e', () => console.log('privE')],\n        ],\n        [\n          dec\n        ]\n      );\n\n      initA = ret[0];\n\n      initPrivA = ret[1];\n\n      initB = ret[2];\n\n      initPrivB = ret[3];\n      getPrivB = ret[4];\n      setPrivB = ret[5];\n\n      privC = ret[6];\n\n      privD = ret[7];\n\n      privE = ret[8];\n\n      initializeInstance = ret[9];\n\n      Class = ret[10]\n\n      initializeClass = ret[11];\n    }\n\n    a = (initializeInstance(this), initA(this, 123));\n\n    #a = initPrivA(this, 123);\n\n    #bData = initB(this, 123);\n    get b() { return this.#bData }\n    set b(v) { this.#bData = v }\n\n    #privBData = initPrivB(this, 123);\n    get #b() { return getPrivB(this); }\n    set #b(v) { setPrivB(this, v); }\n\n    c() { console.log('c'); }\n\n    #c(...args) { return privC(this, ...args) }\n\n    get d() { console.log('d'); }\n\n    get #d() { return privD(this); }\n\n    set e(v) { console.log('e'); }\n\n    set #e(v) { privE(this, v); }\n  }\n\n  initializeClass(Class);\n */\n\nexport default /* @no-mangle */ function applyDecs2305(\n  targetClass: any,\n  memberDecs: DecoratorInfo[],\n  classDecs: Function[],\n  classDecsHaveThis: number,\n  instanceBrand: Function,\n  parentClass: any,\n) {\n  function _bindPropCall(obj: any, name: string, before?: Function) {\n    return function (_this: any, value?: any) {\n      if (before) {\n        before(_this);\n      }\n      return obj[name].call(_this, value);\n    };\n  }\n\n  function runInitializers(initializers: Function[], value: any) {\n    for (var i = 0; i < initializers.length; i++) {\n      initializers[i].call(value);\n    }\n    return value;\n  }\n\n  function assertCallable(\n    fn: any,\n    hint1: string,\n    hint2?: string,\n    throwUndefined?: boolean,\n  ) {\n    if (typeof fn !== \"function\") {\n      if (throwUndefined || fn !== void 0) {\n        throw new TypeError(\n          hint1 +\n            \" must \" +\n            (hint2 || \"be\") +\n            \" a function\" +\n            (throwUndefined ? \"\" : \" or undefined\"),\n        );\n      }\n    }\n    return fn;\n  }\n\n  /* @no-mangle */\n  function applyDec(\n    Class: any,\n    decInfo: DecoratorInfo,\n    decoratorsHaveThis: number,\n    name: string,\n    kind: PROP_KIND,\n    metadata: any,\n    initializers: Function[],\n    ret?: Function[],\n    isStatic?: boolean,\n    isPrivate?: boolean,\n    isField?: boolean,\n    isAccessor?: boolean,\n    hasPrivateBrand?: Function,\n  ) {\n    function assertInstanceIfPrivate(target: any) {\n      if (!hasPrivateBrand(target)) {\n        throw new TypeError(\n          \"Attempted to access private element on non-instance\",\n        );\n      }\n    }\n\n    var decs = decInfo[0],\n      decVal = decInfo[3],\n      _: any,\n      isClass = !ret;\n\n    if (!isClass) {\n      if (!decoratorsHaveThis && !Array.isArray(decs)) {\n        decs = [decs];\n      }\n\n      var desc: PropertyDescriptor = {},\n        init: Function[] = [],\n        key: \"get\" | \"set\" | \"value\" =\n          kind === PROP_KIND.GETTER\n            ? \"get\"\n            : kind === PROP_KIND.SETTER || isAccessor\n              ? \"set\"\n              : \"value\";\n\n      if (isPrivate) {\n        if (isField || isAccessor) {\n          desc = {\n            get: setFunctionName(\n              function (this: any) {\n                return decVal(this);\n              },\n              name,\n              \"get\",\n            ),\n            set: function (this: any, value: any) {\n              decInfo[4](this, value);\n            },\n          };\n        } else {\n          desc[key] = decVal;\n        }\n\n        if (!isField) {\n          setFunctionName(\n            desc[key],\n            name,\n            kind === PROP_KIND.METHOD ? \"\" : key,\n          );\n        }\n      } else if (!isField) {\n        desc = Object.getOwnPropertyDescriptor(Class, name);\n      }\n    }\n\n    var newValue = Class;\n\n    for (var i = decs.length - 1; i >= 0; i -= decoratorsHaveThis ? 2 : 1) {\n      var dec = (decs as Function[])[i],\n        decThis = decoratorsHaveThis ? (decs as any[])[i - 1] : void 0;\n\n      var decoratorFinishedRef: DecoratorFinishedRef = {};\n      var ctx: DecoratorContext = {\n        kind: [\"field\", \"accessor\", \"method\", \"getter\", \"setter\", \"class\"][\n          kind\n        ] as any,\n\n        name: name,\n        metadata: metadata,\n        addInitializer: function (\n          decoratorFinishedRef: DecoratorFinishedRef,\n          initializer: Function,\n        ) {\n          if (decoratorFinishedRef.v) {\n            throw new Error(\n              \"attempted to call addInitializer after decoration was finished\",\n            );\n          }\n          assertCallable(initializer, \"An initializer\", \"be\", true);\n          initializers.push(initializer);\n        }.bind(null, decoratorFinishedRef),\n      };\n\n      try {\n        if (isClass) {\n          if (\n            (_ = assertCallable(\n              dec.call(decThis, newValue, ctx),\n              \"class decorators\",\n              \"return\",\n            ))\n          ) {\n            newValue = _;\n          }\n        } else {\n          ctx[\"static\"] = isStatic;\n          ctx[\"private\"] = isPrivate;\n\n          var get, set;\n          if (!isPrivate) {\n            get = function (target: any) {\n              return target[name];\n            };\n            if (kind < PROP_KIND.METHOD || kind === PROP_KIND.SETTER) {\n              set = function (target: any, v: any) {\n                target[name] = v;\n              };\n            }\n          } else if (kind === PROP_KIND.METHOD) {\n            get = function (_this: any) {\n              assertInstanceIfPrivate(_this);\n              return desc.value;\n            };\n          } else {\n            if (kind < PROP_KIND.SETTER) {\n              get = _bindPropCall(desc, \"get\", assertInstanceIfPrivate);\n            }\n            if (kind !== PROP_KIND.GETTER) {\n              set = _bindPropCall(desc, \"set\", assertInstanceIfPrivate);\n            }\n          }\n\n          var access: DecoratorContextAccess = (ctx.access = {\n            has: isPrivate\n              ? // @ts-expect-error no thisArg\n                hasPrivateBrand.bind()\n              : function (target: object) {\n                  return name in target;\n                },\n          });\n          if (get) access.get = get;\n          if (set) access.set = set;\n\n          newValue = dec.call(\n            decThis,\n            isAccessor\n              ? {\n                  get: desc.get,\n                  set: desc.set,\n                }\n              : desc[key],\n            ctx,\n          );\n\n          if (isAccessor) {\n            if (typeof newValue === \"object\" && newValue) {\n              if ((_ = assertCallable(newValue.get, \"accessor.get\"))) {\n                desc.get = _;\n              }\n              if ((_ = assertCallable(newValue.set, \"accessor.set\"))) {\n                desc.set = _;\n              }\n              if ((_ = assertCallable(newValue.init, \"accessor.init\"))) {\n                init.push(_);\n              }\n            } else if (newValue !== void 0) {\n              throw new TypeError(\n                \"accessor decorators must return an object with get, set, or init properties or void 0\",\n              );\n            }\n          } else if (\n            assertCallable(\n              newValue,\n              (isField ? \"field\" : \"method\") + \" decorators\",\n              \"return\",\n            )\n          ) {\n            if (isField) {\n              init.push(newValue);\n            } else {\n              desc[key] = newValue;\n            }\n          }\n        }\n      } finally {\n        decoratorFinishedRef.v = true;\n      }\n    }\n\n    if (isField || isAccessor) {\n      ret.push(function (instance: any, value: any) {\n        for (var i = init.length - 1; i >= 0; i--) {\n          value = init[i].call(instance, value);\n        }\n        return value;\n      });\n    }\n\n    if (!isField && !isClass) {\n      if (isPrivate) {\n        if (isAccessor) {\n          ret.push(_bindPropCall(desc, \"get\"), _bindPropCall(desc, \"set\"));\n        } else {\n          ret.push(\n            kind === PROP_KIND.METHOD\n              ? desc[key]\n              : _bindPropCall.call.bind(desc[key]),\n          );\n        }\n      } else {\n        Object.defineProperty(Class, name, desc);\n      }\n    }\n    return newValue;\n  }\n\n  /* @no-mangle */\n  function applyMemberDecs(\n    Class: any,\n    decInfos: DecoratorInfo[],\n    instanceBrand: Function,\n    metadata: any,\n  ) {\n    var ret: Function[] = [];\n    var protoInitializers: Function[];\n    var staticInitializers: Function[];\n    var staticBrand = function (_: any) {\n      return checkInRHS(_) === Class;\n    };\n\n    var existingNonFields = new Map();\n\n    function pushInitializers(initializers: Function[]) {\n      if (initializers) {\n        ret.push(runInitializers.bind(null, initializers));\n      }\n    }\n\n    for (var i = 0; i < decInfos.length; i++) {\n      var decInfo = decInfos[i];\n\n      // skip computed property names\n      if (!Array.isArray(decInfo)) continue;\n\n      var kind = decInfo[1];\n      var name = decInfo[2];\n      var isPrivate = decInfo.length > 3;\n\n      var decoratorsHaveThis = kind & PROP_KIND.DECORATORS_HAVE_THIS;\n      var isStatic = !!(kind & PROP_KIND.STATIC);\n\n      kind &= 7; /* 0b111 */\n\n      var isField = kind === PROP_KIND.FIELD;\n\n      var key = name + \"/\" + isStatic;\n\n      if (!isField && !isPrivate) {\n        var existingKind = existingNonFields.get(key);\n\n        if (\n          existingKind === true ||\n          (existingKind === PROP_KIND.GETTER && kind !== PROP_KIND.SETTER) ||\n          (existingKind === PROP_KIND.SETTER && kind !== PROP_KIND.GETTER)\n        ) {\n          throw new Error(\n            \"Attempted to decorate a public method/accessor that has the same name as a previously decorated public method/accessor. This is not currently supported by the decorators plugin. Property name was: \" +\n              name,\n          );\n        }\n        existingNonFields.set(key, kind > PROP_KIND.METHOD ? kind : true);\n      }\n\n      applyDec(\n        isStatic ? Class : Class.prototype,\n        decInfo,\n        decoratorsHaveThis,\n        isPrivate ? \"#\" + name : (toPropertyKey(name) as string),\n        kind,\n        metadata,\n        isStatic\n          ? (staticInitializers = staticInitializers || [])\n          : (protoInitializers = protoInitializers || []),\n        ret,\n        isStatic,\n        isPrivate,\n        isField,\n        kind === PROP_KIND.ACCESSOR,\n        isStatic && isPrivate ? staticBrand : instanceBrand,\n      );\n    }\n\n    pushInitializers(protoInitializers);\n    pushInitializers(staticInitializers);\n    return ret;\n  }\n\n  function defineMetadata(Class: any, metadata: any) {\n    return Object.defineProperty(\n      Class,\n      Symbol.metadata || Symbol[\"for\"](\"Symbol.metadata\"),\n      { configurable: true, enumerable: true, value: metadata },\n    );\n  }\n\n  if (arguments.length >= 6) {\n    var parentMetadata =\n      parentClass[Symbol.metadata || Symbol[\"for\"](\"Symbol.metadata\")];\n  }\n  var metadata = Object.create(parentMetadata == null ? null : parentMetadata);\n  var e = applyMemberDecs(targetClass, memberDecs, instanceBrand, metadata);\n  if (!classDecs.length) defineMetadata(targetClass, metadata);\n  return {\n    e: e,\n    // Lazily apply class decorations so that member init locals can be properly bound.\n    get c() {\n      // The transformer will not emit assignment when there are no class decorators,\n      // so we don't have to return an empty array here.\n      var initializers: Function[] = [];\n      return (\n        classDecs.length && [\n          defineMetadata(\n            applyDec(\n              targetClass,\n              [classDecs],\n              classDecsHaveThis,\n              targetClass.name,\n              PROP_KIND.CLASS,\n              metadata,\n              initializers,\n            ),\n            metadata,\n          ),\n          runInitializers.bind(null, initializers, targetClass),\n        ]\n      );\n    },\n  };\n}\n"], "mappings": ";;;;;;AAIA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AA0LgC,SAASG,aAAaA,CACpDC,WAAgB,EAChBC,UAA2B,EAC3BC,SAAqB,EACrBC,iBAAyB,EACzBC,aAAuB,EACvBC,WAAgB,EAChB;EACA,SAASC,aAAaA,CAACC,GAAQ,EAAEC,IAAY,EAAEC,MAAiB,EAAE;IAChE,OAAO,UAAUC,KAAU,EAAEC,KAAW,EAAE;MACxC,IAAIF,MAAM,EAAE;QACVA,MAAM,CAACC,KAAK,CAAC;MACf;MACA,OAAOH,GAAG,CAACC,IAAI,CAAC,CAACI,IAAI,CAACF,KAAK,EAAEC,KAAK,CAAC;IACrC,CAAC;EACH;EAEA,SAASE,eAAeA,CAACC,YAAwB,EAAEH,KAAU,EAAE;IAC7D,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,YAAY,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5CD,YAAY,CAACC,CAAC,CAAC,CAACH,IAAI,CAACD,KAAK,CAAC;IAC7B;IACA,OAAOA,KAAK;EACd;EAEA,SAASM,cAAcA,CACrBC,EAAO,EACPC,KAAa,EACbC,KAAc,EACdC,cAAwB,EACxB;IACA,IAAI,OAAOH,EAAE,KAAK,UAAU,EAAE;MAC5B,IAAIG,cAAc,IAAIH,EAAE,KAAK,KAAK,CAAC,EAAE;QACnC,MAAM,IAAII,SAAS,CACjBH,KAAK,GACH,QAAQ,IACPC,KAAK,IAAI,IAAI,CAAC,GACf,aAAa,IACZC,cAAc,GAAG,EAAE,GAAG,eAAe,CAC1C,CAAC;MACH;IACF;IACA,OAAOH,EAAE;EACX;EAGA,SAASK,QAAQA,CACfC,KAAU,EACVC,OAAsB,EACtBC,kBAA0B,EAC1BlB,IAAY,EACZmB,IAAe,EACfC,QAAa,EACbd,YAAwB,EACxBe,GAAgB,EAChBC,QAAkB,EAClBC,SAAmB,EACnBC,OAAiB,EACjBC,UAAoB,EACpBC,eAA0B,EAC1B;IACA,SAASC,uBAAuBA,CAACC,MAAW,EAAE;MAC5C,IAAI,CAACF,eAAe,CAACE,MAAM,CAAC,EAAE;QAC5B,MAAM,IAAId,SAAS,CACjB,qDACF,CAAC;MACH;IACF;IAEA,IAAIe,IAAI,GAAGZ,OAAO,CAAC,CAAC,CAAC;MACnBa,MAAM,GAAGb,OAAO,CAAC,CAAC,CAAC;MACnBc,CAAM;MACNC,OAAO,GAAG,CAACX,GAAG;IAEhB,IAAI,CAACW,OAAO,EAAE;MACZ,IAAI,CAACd,kBAAkB,IAAI,CAACe,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,EAAE;QAC/CA,IAAI,GAAG,CAACA,IAAI,CAAC;MACf;MAEA,IAAIM,IAAwB,GAAG,CAAC,CAAC;QAC/BC,IAAgB,GAAG,EAAE;QACrBC,GAA4B,GAC1BlB,IAAI,MAAqB,GACrB,KAAK,GACLA,IAAI,MAAqB,IAAIM,UAAU,GACrC,KAAK,GACL,OAAO;MAEjB,IAAIF,SAAS,EAAE;QACb,IAAIC,OAAO,IAAIC,UAAU,EAAE;UACzBU,IAAI,GAAG;YACLG,GAAG,EAAE,IAAAC,wBAAe,EAClB,YAAqB;cACnB,OAAOT,MAAM,CAAC,IAAI,CAAC;YACrB,CAAC,EACD9B,IAAI,EACJ,KACF,CAAC;YACDwC,GAAG,EAAE,SAAAA,CAAqBrC,KAAU,EAAE;cACpCc,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,EAAEd,KAAK,CAAC;YACzB;UACF,CAAC;QACH,CAAC,MAAM;UACLgC,IAAI,CAACE,GAAG,CAAC,GAAGP,MAAM;QACpB;QAEA,IAAI,CAACN,OAAO,EAAE;UACZ,IAAAe,wBAAe,EACbJ,IAAI,CAACE,GAAG,CAAC,EACTrC,IAAI,EACJmB,IAAI,MAAqB,GAAG,EAAE,GAAGkB,GACnC,CAAC;QACH;MACF,CAAC,MAAM,IAAI,CAACb,OAAO,EAAE;QACnBW,IAAI,GAAGM,MAAM,CAACC,wBAAwB,CAAC1B,KAAK,EAAEhB,IAAI,CAAC;MACrD;IACF;IAEA,IAAI2C,QAAQ,GAAG3B,KAAK;IAEpB,KAAK,IAAIT,CAAC,GAAGsB,IAAI,CAACrB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAIW,kBAAkB,GAAG,CAAC,GAAG,CAAC,EAAE;MACrE,IAAI0B,GAAG,GAAIf,IAAI,CAAgBtB,CAAC,CAAC;QAC/BsC,OAAO,GAAG3B,kBAAkB,GAAIW,IAAI,CAAWtB,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;MAEhE,IAAIuC,oBAA0C,GAAG,CAAC,CAAC;MACnD,IAAIC,GAAqB,GAAG;QAC1B5B,IAAI,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAChEA,IAAI,CACE;QAERnB,IAAI,EAAEA,IAAI;QACVoB,QAAQ,EAAEA,QAAQ;QAClB4B,cAAc,EAAE,UACdF,oBAA0C,EAC1CG,WAAqB,EACrB;UACA,IAAIH,oBAAoB,CAACI,CAAC,EAAE;YAC1B,MAAM,IAAIC,KAAK,CACb,gEACF,CAAC;UACH;UACA1C,cAAc,CAACwC,WAAW,EAAE,gBAAgB,EAAE,IAAI,EAAE,IAAI,CAAC;UACzD3C,YAAY,CAAC8C,IAAI,CAACH,WAAW,CAAC;QAChC,CAAC,CAACI,IAAI,CAAC,IAAI,EAAEP,oBAAoB;MACnC,CAAC;MAED,IAAI;QACF,IAAId,OAAO,EAAE;UACX,IACGD,CAAC,GAAGtB,cAAc,CACjBmC,GAAG,CAACxC,IAAI,CAACyC,OAAO,EAAEF,QAAQ,EAAEI,GAAG,CAAC,EAChC,kBAAkB,EAClB,QACF,CAAC,EACD;YACAJ,QAAQ,GAAGZ,CAAC;UACd;QACF,CAAC,MAAM;UACLgB,GAAG,CAAC,QAAQ,CAAC,GAAGzB,QAAQ;UACxByB,GAAG,CAAC,SAAS,CAAC,GAAGxB,SAAS;UAE1B,IAAIe,GAAG,EAAEE,GAAG;UACZ,IAAI,CAACjB,SAAS,EAAE;YACde,GAAG,GAAG,SAAAA,CAAUV,MAAW,EAAE;cAC3B,OAAOA,MAAM,CAAC5B,IAAI,CAAC;YACrB,CAAC;YACD,IAAImB,IAAI,IAAmB,IAAIA,IAAI,MAAqB,EAAE;cACxDqB,GAAG,GAAG,SAAAA,CAAUZ,MAAW,EAAEsB,CAAM,EAAE;gBACnCtB,MAAM,CAAC5B,IAAI,CAAC,GAAGkD,CAAC;cAClB,CAAC;YACH;UACF,CAAC,MAAM,IAAI/B,IAAI,MAAqB,EAAE;YACpCmB,GAAG,GAAG,SAAAA,CAAUpC,KAAU,EAAE;cAC1ByB,uBAAuB,CAACzB,KAAK,CAAC;cAC9B,OAAOiC,IAAI,CAAChC,KAAK;YACnB,CAAC;UACH,CAAC,MAAM;YACL,IAAIgB,IAAI,IAAmB,EAAE;cAC3BmB,GAAG,GAAGxC,aAAa,CAACqC,IAAI,EAAE,KAAK,EAAER,uBAAuB,CAAC;YAC3D;YACA,IAAIR,IAAI,MAAqB,EAAE;cAC7BqB,GAAG,GAAG1C,aAAa,CAACqC,IAAI,EAAE,KAAK,EAAER,uBAAuB,CAAC;YAC3D;UACF;UAEA,IAAI2B,MAA8B,GAAIP,GAAG,CAACO,MAAM,GAAG;YACjDC,GAAG,EAAEhC,SAAS,GAEVG,eAAe,CAAC2B,IAAI,CAAC,CAAC,GACtB,UAAUzB,MAAc,EAAE;cACxB,OAAO5B,IAAI,IAAI4B,MAAM;YACvB;UACN,CAAE;UACF,IAAIU,GAAG,EAAEgB,MAAM,CAAChB,GAAG,GAAGA,GAAG;UACzB,IAAIE,GAAG,EAAEc,MAAM,CAACd,GAAG,GAAGA,GAAG;UAEzBG,QAAQ,GAAGC,GAAG,CAACxC,IAAI,CACjByC,OAAO,EACPpB,UAAU,GACN;YACEa,GAAG,EAAEH,IAAI,CAACG,GAAG;YACbE,GAAG,EAAEL,IAAI,CAACK;UACZ,CAAC,GACDL,IAAI,CAACE,GAAG,CAAC,EACbU,GACF,CAAC;UAED,IAAItB,UAAU,EAAE;YACd,IAAI,OAAOkB,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,EAAE;cAC5C,IAAKZ,CAAC,GAAGtB,cAAc,CAACkC,QAAQ,CAACL,GAAG,EAAE,cAAc,CAAC,EAAG;gBACtDH,IAAI,CAACG,GAAG,GAAGP,CAAC;cACd;cACA,IAAKA,CAAC,GAAGtB,cAAc,CAACkC,QAAQ,CAACH,GAAG,EAAE,cAAc,CAAC,EAAG;gBACtDL,IAAI,CAACK,GAAG,GAAGT,CAAC;cACd;cACA,IAAKA,CAAC,GAAGtB,cAAc,CAACkC,QAAQ,CAACP,IAAI,EAAE,eAAe,CAAC,EAAG;gBACxDA,IAAI,CAACgB,IAAI,CAACrB,CAAC,CAAC;cACd;YACF,CAAC,MAAM,IAAIY,QAAQ,KAAK,KAAK,CAAC,EAAE;cAC9B,MAAM,IAAI7B,SAAS,CACjB,uFACF,CAAC;YACH;UACF,CAAC,MAAM,IACLL,cAAc,CACZkC,QAAQ,EACR,CAACnB,OAAO,GAAG,OAAO,GAAG,QAAQ,IAAI,aAAa,EAC9C,QACF,CAAC,EACD;YACA,IAAIA,OAAO,EAAE;cACXY,IAAI,CAACgB,IAAI,CAACT,QAAQ,CAAC;YACrB,CAAC,MAAM;cACLR,IAAI,CAACE,GAAG,CAAC,GAAGM,QAAQ;YACtB;UACF;QACF;MACF,CAAC,SAAS;QACRG,oBAAoB,CAACI,CAAC,GAAG,IAAI;MAC/B;IACF;IAEA,IAAI1B,OAAO,IAAIC,UAAU,EAAE;MACzBJ,GAAG,CAAC+B,IAAI,CAAC,UAAUI,QAAa,EAAErD,KAAU,EAAE;QAC5C,KAAK,IAAII,CAAC,GAAG6B,IAAI,CAAC5B,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UACzCJ,KAAK,GAAGiC,IAAI,CAAC7B,CAAC,CAAC,CAACH,IAAI,CAACoD,QAAQ,EAAErD,KAAK,CAAC;QACvC;QACA,OAAOA,KAAK;MACd,CAAC,CAAC;IACJ;IAEA,IAAI,CAACqB,OAAO,IAAI,CAACQ,OAAO,EAAE;MACxB,IAAIT,SAAS,EAAE;QACb,IAAIE,UAAU,EAAE;UACdJ,GAAG,CAAC+B,IAAI,CAACtD,aAAa,CAACqC,IAAI,EAAE,KAAK,CAAC,EAAErC,aAAa,CAACqC,IAAI,EAAE,KAAK,CAAC,CAAC;QAClE,CAAC,MAAM;UACLd,GAAG,CAAC+B,IAAI,CACNjC,IAAI,MAAqB,GACrBgB,IAAI,CAACE,GAAG,CAAC,GACTvC,aAAa,CAACM,IAAI,CAACiD,IAAI,CAAClB,IAAI,CAACE,GAAG,CAAC,CACvC,CAAC;QACH;MACF,CAAC,MAAM;QACLI,MAAM,CAACgB,cAAc,CAACzC,KAAK,EAAEhB,IAAI,EAAEmC,IAAI,CAAC;MAC1C;IACF;IACA,OAAOQ,QAAQ;EACjB;EAGA,SAASe,eAAeA,CACtB1C,KAAU,EACV2C,QAAyB,EACzB/D,aAAuB,EACvBwB,QAAa,EACb;IACA,IAAIC,GAAe,GAAG,EAAE;IACxB,IAAIuC,iBAA6B;IACjC,IAAIC,kBAA8B;IAClC,IAAIC,WAAW,GAAG,SAAAA,CAAU/B,CAAM,EAAE;MAClC,OAAO,IAAAgC,mBAAU,EAAChC,CAAC,CAAC,KAAKf,KAAK;IAChC,CAAC;IAED,IAAIgD,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;IAEjC,SAASC,gBAAgBA,CAAC5D,YAAwB,EAAE;MAClD,IAAIA,YAAY,EAAE;QAChBe,GAAG,CAAC+B,IAAI,CAAC/C,eAAe,CAACgD,IAAI,CAAC,IAAI,EAAE/C,YAAY,CAAC,CAAC;MACpD;IACF;IAEA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoD,QAAQ,CAACnD,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,IAAIU,OAAO,GAAG0C,QAAQ,CAACpD,CAAC,CAAC;MAGzB,IAAI,CAAC0B,KAAK,CAACC,OAAO,CAACjB,OAAO,CAAC,EAAE;MAE7B,IAAIE,IAAI,GAAGF,OAAO,CAAC,CAAC,CAAC;MACrB,IAAIjB,IAAI,GAAGiB,OAAO,CAAC,CAAC,CAAC;MACrB,IAAIM,SAAS,GAAGN,OAAO,CAACT,MAAM,GAAG,CAAC;MAElC,IAAIU,kBAAkB,GAAGC,IAAI,KAAiC;MAC9D,IAAIG,QAAQ,GAAG,CAAC,EAAEH,IAAI,IAAmB,CAAC;MAE1CA,IAAI,IAAI,CAAC;MAET,IAAIK,OAAO,GAAGL,IAAI,MAAoB;MAEtC,IAAIkB,GAAG,GAAGrC,IAAI,GAAG,GAAG,GAAGsB,QAAQ;MAE/B,IAAI,CAACE,OAAO,IAAI,CAACD,SAAS,EAAE;QAC1B,IAAI4C,YAAY,GAAGH,iBAAiB,CAAC1B,GAAG,CAACD,GAAG,CAAC;QAE7C,IACE8B,YAAY,KAAK,IAAI,IACpBA,YAAY,MAAqB,IAAIhD,IAAI,MAAsB,IAC/DgD,YAAY,MAAqB,IAAIhD,IAAI,MAAsB,EAChE;UACA,MAAM,IAAIgC,KAAK,CACb,uMAAuM,GACrMnD,IACJ,CAAC;QACH;QACAgE,iBAAiB,CAACxB,GAAG,CAACH,GAAG,EAAElB,IAAI,IAAmB,GAAGA,IAAI,GAAG,IAAI,CAAC;MACnE;MAEAJ,QAAQ,CACNO,QAAQ,GAAGN,KAAK,GAAGA,KAAK,CAACoD,SAAS,EAClCnD,OAAO,EACPC,kBAAkB,EAClBK,SAAS,GAAG,GAAG,GAAGvB,IAAI,GAAI,IAAAqE,sBAAa,EAACrE,IAAI,CAAY,EACxDmB,IAAI,EACJC,QAAQ,EACRE,QAAQ,GACHuC,kBAAkB,GAAGA,kBAAkB,IAAI,EAAE,GAC7CD,iBAAiB,GAAGA,iBAAiB,IAAI,EAAG,EACjDvC,GAAG,EACHC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPL,IAAI,MAAuB,EAC3BG,QAAQ,IAAIC,SAAS,GAAGuC,WAAW,GAAGlE,aACxC,CAAC;IACH;IAEAsE,gBAAgB,CAACN,iBAAiB,CAAC;IACnCM,gBAAgB,CAACL,kBAAkB,CAAC;IACpC,OAAOxC,GAAG;EACZ;EAEA,SAASiD,cAAcA,CAACtD,KAAU,EAAEI,QAAa,EAAE;IACjD,OAAOqB,MAAM,CAACgB,cAAc,CAC1BzC,KAAK,EACLuD,MAAM,CAACnD,QAAQ,IAAImD,MAAM,CAAC,KAAK,CAAC,CAAC,iBAAiB,CAAC,EACnD;MAAEC,YAAY,EAAE,IAAI;MAAEC,UAAU,EAAE,IAAI;MAAEtE,KAAK,EAAEiB;IAAS,CAC1D,CAAC;EACH;EAEA,IAAIsD,SAAS,CAAClE,MAAM,IAAI,CAAC,EAAE;IACzB,IAAImE,cAAc,GAChB9E,WAAW,CAAC0E,MAAM,CAACnD,QAAQ,IAAImD,MAAM,CAAC,KAAK,CAAC,CAAC,iBAAiB,CAAC,CAAC;EACpE;EACA,IAAInD,QAAQ,GAAGqB,MAAM,CAACmC,MAAM,CAACD,cAAc,IAAI,IAAI,GAAG,IAAI,GAAGA,cAAc,CAAC;EAC5E,IAAIE,CAAC,GAAGnB,eAAe,CAAClE,WAAW,EAAEC,UAAU,EAAEG,aAAa,EAAEwB,QAAQ,CAAC;EACzE,IAAI,CAAC1B,SAAS,CAACc,MAAM,EAAE8D,cAAc,CAAC9E,WAAW,EAAE4B,QAAQ,CAAC;EAC5D,OAAO;IACLyD,CAAC,EAAEA,CAAC;IAEJ,IAAIC,CAACA,CAAA,EAAG;MAGN,IAAIxE,YAAwB,GAAG,EAAE;MACjC,OACEZ,SAAS,CAACc,MAAM,IAAI,CAClB8D,cAAc,CACZvD,QAAQ,CACNvB,WAAW,EACX,CAACE,SAAS,CAAC,EACXC,iBAAiB,EACjBH,WAAW,CAACQ,IAAI,KAEhBoB,QAAQ,EACRd,YACF,CAAC,EACDc,QACF,CAAC,EACDf,eAAe,CAACgD,IAAI,CAAC,IAAI,EAAE/C,YAAY,EAAEd,WAAW,CAAC,CACtD;IAEL;EACF,CAAC;AACH", "ignoreList": []}