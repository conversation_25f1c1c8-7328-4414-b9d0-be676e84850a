import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/article_category_model.dart';
import '../bloc/article_categories_bloc.dart';
import '../bloc/article_categories_event.dart';

class ArticleCategoryFormPage extends StatefulWidget {
  final ArticleCategoryModel? category;
  final bool isEditing;

  const ArticleCategoryFormPage({
    super.key,
    this.category,
    this.isEditing = false,
  });

  @override
  State<ArticleCategoryFormPage> createState() =>
      _ArticleCategoryFormPageState();
}

class _ArticleCategoryFormPageState extends State<ArticleCategoryFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _sortOrderController = TextEditingController();

  String _selectedIcon = 'article';
  String _selectedColor = '#4CAF50';
  bool _isActive = true;

  final List<Map<String, dynamic>> _availableIcons = [
    {'name': 'article', 'icon': Icons.article, 'label': 'مقال'},
    {
      'name': 'medical_services',
      'icon': Icons.medical_services,
      'label': 'طبي',
    },
    {'name': 'restaurant_menu', 'icon': Icons.restaurant_menu, 'label': 'حمية'},
    {
      'name': 'health_and_safety',
      'icon': Icons.health_and_safety,
      'label': 'صحة',
    },
    {'name': 'fitness_center', 'icon': Icons.fitness_center, 'label': 'رياضة'},
    {
      'name': 'tips_and_updates',
      'icon': Icons.tips_and_updates,
      'label': 'نصائح',
    },
    {'name': 'psychology', 'icon': Icons.psychology, 'label': 'نفسي'},
    {'name': 'science', 'icon': Icons.science, 'label': 'علمي'},
  ];

  final List<String> _availableColors = [
    '#4CAF50', // Green
    '#2196F3', // Blue
    '#FF9800', // Orange
    '#9C27B0', // Purple
    '#607D8B', // Blue Grey
    '#795548', // Brown
    '#E91E63', // Pink
    '#009688', // Teal
    '#FF5722', // Deep Orange
    '#3F51B5', // Indigo
  ];

  @override
  void initState() {
    super.initState();
    if (widget.isEditing && widget.category != null) {
      _nameController.text = widget.category!.name;
      _descriptionController.text = widget.category!.description ?? '';
      _sortOrderController.text = widget.category!.sortOrder.toString();
      _selectedIcon = widget.category!.icon ?? 'article';
      _selectedColor = widget.category!.color ?? '#4CAF50';
      _isActive = widget.category!.isActive;
    } else {
      _sortOrderController.text = '0';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _sortOrderController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(widget.isEditing ? 'تعديل القسم' : 'إضافة قسم جديد'),
        backgroundColor: AppColors.white,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _saveCategory,
            child: Text(
              widget.isEditing ? 'تحديث' : 'حفظ',
              style: TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
                fontSize: 16.sp,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(20.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Name Field
              _buildSectionTitle('اسم القسم'),
              SizedBox(height: 8.h),
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  hintText: 'أدخل اسم القسم',
                  prefixIcon: const Icon(Icons.title),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'يرجى إدخال اسم القسم';
                  }
                  return null;
                },
              ),

              SizedBox(height: 20.h),

              // Description Field
              _buildSectionTitle('الوصف'),
              SizedBox(height: 8.h),
              TextFormField(
                controller: _descriptionController,
                maxLines: 4,
                decoration: InputDecoration(
                  hintText: 'أدخل وصف القسم (اختياري)',
                  prefixIcon: const Icon(Icons.description),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
              ),

              SizedBox(height: 20.h),

              // Sort Order Field
              _buildSectionTitle('ترتيب القسم'),
              SizedBox(height: 8.h),
              TextFormField(
                controller: _sortOrderController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: 'أدخل رقم الترتيب (0 = الأول)',
                  prefixIcon: const Icon(Icons.sort),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  helperText: 'الأرقام الأصغر تظهر أولاً (0, 1, 2, ...)',
                ),
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'يرجى إدخال رقم الترتيب';
                  }
                  final number = int.tryParse(value!);
                  if (number == null || number < 0) {
                    return 'يرجى إدخال رقم صحيح أكبر من أو يساوي 0';
                  }
                  return null;
                },
              ),

              SizedBox(height: 20.h),

              // Icon Selection
              _buildSectionTitle('اختر الأيقونة'),
              SizedBox(height: 12.h),
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(color: AppColors.gray300),
                ),
                child: GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 4,
                    mainAxisSpacing: 12.w,
                    crossAxisSpacing: 12.h,
                    childAspectRatio: 1,
                  ),
                  itemCount: _availableIcons.length,
                  itemBuilder: (context, index) {
                    final iconData = _availableIcons[index];
                    final isSelected = _selectedIcon == iconData['name'];

                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedIcon = iconData['name'];
                        });
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color:
                              isSelected
                                  ? AppColors.primary.withValues(alpha: 0.1)
                                  : AppColors.gray100,
                          borderRadius: BorderRadius.circular(12.r),
                          border: Border.all(
                            color:
                                isSelected
                                    ? AppColors.primary
                                    : AppColors.gray300,
                            width: 2,
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              iconData['icon'],
                              color:
                                  isSelected
                                      ? AppColors.primary
                                      : AppColors.textSecondary,
                              size: 24.w,
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              iconData['label'],
                              style: TextStyle(
                                fontSize: 10.sp,
                                color:
                                    isSelected
                                        ? AppColors.primary
                                        : AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),

              SizedBox(height: 20.h),

              // Color Selection
              _buildSectionTitle('اختر اللون'),
              SizedBox(height: 12.h),
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(color: AppColors.gray300),
                ),
                child: Wrap(
                  spacing: 12.w,
                  runSpacing: 12.h,
                  children:
                      _availableColors.map((color) {
                        final isSelected = _selectedColor == color;

                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedColor = color;
                            });
                          },
                          child: Container(
                            width: 50.w,
                            height: 50.h,
                            decoration: BoxDecoration(
                              color: Color(
                                int.parse(color.replaceFirst('#', '0xff')),
                              ),
                              shape: BoxShape.circle,
                              border: Border.all(
                                color:
                                    isSelected
                                        ? AppColors.textPrimary
                                        : AppColors.gray300,
                                width: isSelected ? 3 : 1,
                              ),
                            ),
                            child:
                                isSelected
                                    ? Icon(
                                      Icons.check,
                                      color: AppColors.white,
                                      size: 20.w,
                                    )
                                    : null,
                          ),
                        );
                      }).toList(),
                ),
              ),

              SizedBox(height: 20.h),

              // Active Status
              Row(
                children: [
                  Switch(
                    value: _isActive,
                    onChanged: (value) {
                      setState(() {
                        _isActive = value;
                      });
                    },
                  ),
                  SizedBox(width: 12.w),
                  Text(
                    'قسم نشط',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ],
              ),

              SizedBox(height: 40.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
        color: AppColors.textPrimary,
      ),
    );
  }

  void _saveCategory() {
    if (_formKey.currentState!.validate()) {
      final category = ArticleCategoryModel(
        id: widget.category?.id ?? '',
        name: _nameController.text.trim(),
        description:
            _descriptionController.text.trim().isEmpty
                ? null
                : _descriptionController.text.trim(),
        icon: _selectedIcon,
        color: _selectedColor,
        sortOrder: int.parse(_sortOrderController.text.trim()),
        isActive: _isActive,
        createdAt: widget.category?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (widget.isEditing) {
        context.read<ArticleCategoriesBloc>().add(
          UpdateArticleCategory(category: category),
        );
      } else {
        context.read<ArticleCategoriesBloc>().add(
          CreateArticleCategory(category: category),
        );
      }

      Navigator.of(context).pop();
    }
  }
}
