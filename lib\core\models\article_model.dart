import 'package:equatable/equatable.dart';
import 'article_category_model.dart';

class ArticleModel extends Equatable {
  final String id;
  final String title;
  final String content;
  final String author;
  final String? categoryId;
  final ArticleCategoryModel? categoryModel;
  final String? imageUrl;
  final String? pdfUrl;
  final String? reference; // حقل المرجع الجديد
  final bool isPublished;
  final bool isFeatured;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ArticleModel({
    required this.id,
    required this.title,
    required this.content,
    required this.author,
    this.categoryId,
    this.categoryModel,
    this.imageUrl,
    this.pdfUrl,
    this.reference, // حقل المرجع اختياري
    this.isPublished = false,
    this.isFeatured = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ArticleModel.fromJson(Map<String, dynamic> json) {
    return ArticleModel(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      author: json['author'] as String? ?? 'غير محدد',
      categoryId: json['category_id'] as String?,
      categoryModel:
          json['article_categories'] != null
              ? ArticleCategoryModel.fromJson(
                json['article_categories'] as Map<String, dynamic>,
              )
              : null,
      imageUrl: json['image_url'] as String?,
      pdfUrl: json['pdf_url'] as String?,
      reference: json['reference'] as String?, // إضافة حقل المرجع
      isPublished: json['is_published'] as bool? ?? false,
      isFeatured: json['is_featured'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'title': title,
      'content': content,
      'author': author,
      'category_id': categoryId,
      'image_url': imageUrl,
      'pdf_url': pdfUrl,
      'reference': reference, // إضافة حقل المرجع
      'is_published': isPublished,
      'is_featured': isFeatured,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };

    // Only include id if it's not empty (for updates)
    if (id.isNotEmpty) {
      json['id'] = id;
    }

    return json;
  }

  ArticleModel copyWith({
    String? id,
    String? title,
    String? content,
    String? author,
    String? categoryId,
    ArticleCategoryModel? categoryModel,
    String? imageUrl,
    String? pdfUrl,
    String? reference,
    bool? isPublished,
    bool? isFeatured,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ArticleModel(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      author: author ?? this.author,
      categoryId: categoryId ?? this.categoryId,
      categoryModel: categoryModel ?? this.categoryModel,
      imageUrl: imageUrl ?? this.imageUrl,
      pdfUrl: pdfUrl ?? this.pdfUrl,
      reference: reference ?? this.reference,
      isPublished: isPublished ?? this.isPublished,
      isFeatured: isFeatured ?? this.isFeatured,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper getters
  String get categoryName => categoryModel?.name ?? 'غير محدد';

  String get displayImageUrl => imageUrl ?? '';
  String get displayPdfUrl => pdfUrl ?? '';
  bool get hasPdf => pdfUrl != null && pdfUrl!.isNotEmpty;

  @override
  List<Object?> get props => [
    id,
    title,
    content,
    author,
    categoryId,
    categoryModel,
    imageUrl,
    pdfUrl,
    reference,
    isPublished,
    isFeatured,
    createdAt,
    updatedAt,
  ];
}
