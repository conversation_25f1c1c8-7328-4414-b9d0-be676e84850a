import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/product_model.dart';
import '../../../../core/models/product_image_model.dart';

class ProductsRepository {
  Future<List<ProductModel>> getAllProducts() async {
    try {
      debugPrint('🔄 Starting to fetch all products...');

      final response = await SupabaseConfig.products
          .select('''
            *,
            categories(*)
          ''')
          .order('created_at', ascending: false);

      debugPrint('✅ Products fetched successfully: ${response.length} products');
      debugPrint('📊 Sample product data: ${response.isNotEmpty ? response.first.keys : "No products"}');

      // Process each product with detailed logging
      List<ProductModel> products = [];
      for (int i = 0; i < response.length; i++) {
        try {
          debugPrint('🔄 Processing product ${i + 1}/${response.length}');
          final json = response[i];
          debugPrint('📋 Product data: $json');

          final product = ProductModel.fromJson(json);
          products.add(product);
          debugPrint('✅ Product ${i + 1} processed successfully: ${product.name}');
        } catch (e) {
          debugPrint('❌ Error processing product ${i + 1}: $e');
          debugPrint('📋 Problematic data: ${response[i]}');
          rethrow;
        }
      }

      return products;
    } catch (e) {
      debugPrint('❌ Error fetching products: $e');
      debugPrint('📍 Stack trace: ${StackTrace.current}');
      throw Exception('فشل في جلب المنتجات: ${e.toString()}');
    }
  }

  Future<List<ProductModel>> getActiveProducts() async {
    try {
      final response = await SupabaseConfig.products
          .select('''
            *,
            categories(*)
          ''')
          .eq('is_active', true)
          .order('created_at', ascending: false);

      return response.map<ProductModel>((json) => ProductModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في جلب المنتجات النشطة: ${e.toString()}');
    }
  }



  Future<ProductModel> getProductById(String id) async {
    try {
      final response = await SupabaseConfig.products
          .select()
          .eq('id', id)
          .single();

      return ProductModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في جلب بيانات المنتج: ${e.toString()}');
    }
  }

  Future<ProductModel> createProduct(ProductModel product) async {
    try {
      // Check if product code already exists
      await _validateUniqueProductCode(product.productCode);

      final response = await SupabaseConfig.products
          .insert(product.toJson())
          .select()
          .single();

      return ProductModel.fromJson(response);
    } catch (e) {
      if (e.toString().contains('duplicate key') || e.toString().contains('unique constraint')) {
        throw Exception('كود المنتج "${product.productCode}" مستخدم بالفعل');
      }
      throw Exception('فشل في إنشاء المنتج: ${e.toString()}');
    }
  }

  Future<ProductModel> updateProduct(ProductModel product) async {
    try {
      debugPrint('🔄 Updating product: ${product.name}');

      // Check if product code already exists (excluding current product)
      await _validateUniqueProductCode(product.productCode, excludeId: product.id);

      // Create update data WITHOUT images field to preserve existing images
      final updateData = {
        'name': product.name,
        'description': product.description,
        'product_code': product.productCode,
        'price': product.price,
        'discount_percentage': product.discountPercentage,
        'stock': product.stock,
        'category_id': product.categoryId,
        'is_active': product.isActive,
        'updated_at': product.updatedAt.toIso8601String(),
      };

      debugPrint('📝 Update data prepared (excluding images): ${updateData.keys}');

      final response = await SupabaseConfig.products
          .update(updateData)
          .eq('id', product.id)
          .select()
          .single();

      debugPrint('✅ Product updated successfully');
      return ProductModel.fromJson(response);
    } catch (e) {
      debugPrint('❌ Error updating product: $e');
      if (e.toString().contains('duplicate key') || e.toString().contains('unique constraint')) {
        throw Exception('كود المنتج "${product.productCode}" مستخدم بالفعل');
      }
      throw Exception('فشل في تحديث المنتج: ${e.toString()}');
    }
  }

  Future<void> deleteProduct(String id) async {
    try {
      debugPrint('🔄 Deleting product: $id');

      // أولاً، جلب بيانات المنتج للحصول على الصور
      final productResponse = await SupabaseConfig.products
          .select('images')
          .eq('id', id)
          .maybeSingle();

      if (productResponse != null) {
        // استخراج الصور من JSONB
        final imagesJson = productResponse['images'] as List?;
        List<String> imageUrls = [];

        if (imagesJson != null) {
          for (final imageData in imagesJson) {
            final imageUrl = imageData['image_url'] as String?;
            if (imageUrl != null && imageUrl.isNotEmpty) {
              imageUrls.add(imageUrl);
            }
          }
        }

        // حذف المنتج من قاعدة البيانات
        await SupabaseConfig.products
            .delete()
            .eq('id', id);

        debugPrint('✅ Product deleted from database: $id');

        // حذف جميع الصور من Storage
        for (final imageUrl in imageUrls) {
          await _deleteImageFromStorage(imageUrl);
        }

        debugPrint('✅ Product and associated images deleted successfully: $id');
      }
    } catch (e) {
      debugPrint('❌ Error deleting product: $e');
      throw Exception('فشل في حذف المنتج: ${e.toString()}');
    }
  }

  // دالة لاستخراج مسار الصورة من URL وحذفها
  Future<void> _deleteImageFromStorage(String imageUrl) async {
    try {
      debugPrint('🔄 Extracting image path from URL: $imageUrl');

      // استخراج مسار الصورة من URL
      // مثال URL: https://xwxeauofbzedfzaogzzy.supabase.co/storage/v1/object/public/product-images/products/filename.jpg
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;

      // البحث عن مؤشر 'product-images' في المسار
      int bucketIndex = -1;
      for (int i = 0; i < pathSegments.length; i++) {
        if (pathSegments[i] == 'product-images') {
          bucketIndex = i;
          break;
        }
      }

      if (bucketIndex != -1 && bucketIndex < pathSegments.length - 1) {
        // استخراج المسار النسبي بعد اسم الـ bucket
        final relativePath = pathSegments.sublist(bucketIndex + 1).join('/');
        debugPrint('📁 Extracted relative path: $relativePath');

        // حذف الصورة من Storage
        await SupabaseConfig.storage
            .from('product-images')
            .remove([relativePath]);

        debugPrint('✅ Image deleted from storage: $relativePath');
      } else {
        debugPrint('⚠️ Could not extract valid path from URL: $imageUrl');
      }
    } catch (e) {
      debugPrint('❌ Error deleting image from storage: $e');
      // لا نرمي exception هنا لأن حذف الصورة ليس أساسي
      // المهم أن المنتج تم حذفه من قاعدة البيانات
    }
  }

  Future<ProductModel> toggleProductStatus(String id) async {
    try {
      // First get the current status
      final current = await getProductById(id);

      // Toggle the status
      final response = await SupabaseConfig.products
          .update({'is_active': !current.isActive})
          .eq('id', id)
          .select()
          .single();

      return ProductModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في تغيير حالة المنتج: ${e.toString()}');
    }
  }



  Future<List<ProductModel>> searchProducts(String query) async {
    try {
      debugPrint('🔍 Searching products with query: $query');

      final response = await SupabaseConfig.products
          .select()
          .or('name.ilike.%$query%,product_code.ilike.%$query%,description.ilike.%$query%')
          .order('created_at', ascending: false);

      final products = response.map<ProductModel>((json) => ProductModel.fromJson(json)).toList();
      debugPrint('✅ Found ${products.length} products matching query: $query');

      return products;
    } catch (e) {
      debugPrint('❌ Error searching products: $e');
      throw Exception('فشل في البحث عن المنتجات: ${e.toString()}');
    }
  }

  Future<List<String>> getProductCategories() async {
    try {
      debugPrint('🔄 Fetching product categories...');

      // الآن نجلب الفئات من جدول categories مباشرة
      final response = await SupabaseConfig.categories
          .select('name')
          .eq('is_active', true);

      debugPrint('📊 Raw categories response: ${response.length} items');

      final categories = response
          .map<String>((item) => item['name'] as String)
          .toList();

      categories.sort();
      debugPrint('✅ Product categories fetched: ${categories.length} unique categories');
      debugPrint('📋 Categories: $categories');
      return categories;
    } catch (e) {
      debugPrint('❌ Error fetching product categories: $e');
      throw Exception('فشل في جلب فئات المنتجات: ${e.toString()}');
    }
  }

  // Alias for getProductCategories to match Bloc expectations
  Future<List<String>> getCategories() async {
    return getProductCategories();
  }

  Future<ProductModel> updateProductStock(String productId, int newStock) async {
    try {
      final response = await SupabaseConfig.products
          .update({'stock': newStock})
          .eq('id', productId)
          .select()
          .single();

      return ProductModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في تحديث مخزون المنتج: ${e.toString()}');
    }
  }

  // Image upload functions
  Future<String> uploadProductImage(String productId, XFile imageFile, int sortOrder) async {
    try {
      final fileName = '${productId}_${DateTime.now().millisecondsSinceEpoch}_$sortOrder.${imageFile.path.split('.').last}';
      final filePath = 'products/$productId/$fileName';

      debugPrint('📤 Uploading image to: $filePath');

      // Upload to Supabase Storage
      final bytes = await imageFile.readAsBytes();
      debugPrint('📊 Image size: ${bytes.length} bytes');

      await SupabaseConfig.storage
          .from('product-images')
          .uploadBinary(filePath, bytes);

      debugPrint('✅ Image uploaded to storage');

      // Get public URL
      final imageUrl = SupabaseConfig.storage
          .from('product-images')
          .getPublicUrl(filePath);

      debugPrint('🔗 Public URL generated: $imageUrl');
      return imageUrl;
    } catch (e) {
      debugPrint('❌ Error uploading image: $e');
      throw Exception('فشل في رفع الصورة: ${e.toString()}');
    }
  }

  // Validate unique product code
  Future<void> _validateUniqueProductCode(String productCode, {String? excludeId}) async {
    try {
      var query = SupabaseConfig.products
          .select('id')
          .eq('product_code', productCode);

      if (excludeId != null) {
        query = query.neq('id', excludeId);
      }

      final response = await query.maybeSingle();

      if (response != null) {
        throw Exception('كود المنتج "$productCode" مستخدم بالفعل');
      }
    } catch (e) {
      if (e.toString().contains('مستخدم بالفعل')) {
        rethrow;
      }
      throw Exception('فشل في التحقق من كود المنتج: ${e.toString()}');
    }
  }

  // New method to create product with images
  Future<ProductModel> createProductWithImages(ProductModel product, List<XFile> imageFiles) async {
    try {
      debugPrint('🚀 Creating product: ${product.name}');

      // Check if product code already exists
      await _validateUniqueProductCode(product.productCode);

      // Create product data with empty images array first
      final productData = product.toJson();
      productData['images'] = <Map<String, dynamic>>[];

      debugPrint('📝 Product data prepared: ${productData.keys}');

      // Create the product
      final response = await SupabaseConfig.products
          .insert(productData)
          .select()
          .single();

      debugPrint('✅ Product inserted to database');
      final createdProduct = ProductModel.fromJson(response);
      debugPrint('📦 Created product ID: ${createdProduct.id}');

      // If no images, return the product as is
      if (imageFiles.isEmpty) {
        debugPrint('📷 No images to upload, returning product');
        return createdProduct;
      }

      debugPrint('📷 Starting image upload for ${imageFiles.length} images');

      // Upload images and create image objects
      List<Map<String, dynamic>> uploadedImages = [];
      for (int i = 0; i < imageFiles.length; i++) {
        try {
          debugPrint('📤 Uploading image ${i + 1}/${imageFiles.length}');
          final imageUrl = await uploadProductImage(createdProduct.id, imageFiles[i], i);
          final fileName = '${createdProduct.id}_${DateTime.now().millisecondsSinceEpoch}_$i.${imageFiles[i].path.split('.').last}';
          final imagePath = 'products/${createdProduct.id}/$fileName';

          debugPrint('✅ Image ${i + 1} uploaded: $imageUrl');

          uploadedImages.add({
            'id': '${createdProduct.id}_$i',
            'product_id': createdProduct.id,
            'image_url': imageUrl,
            'image_path': imagePath,
            'sort_order': i,
            'is_primary': i == 0,
            'created_at': DateTime.now().toIso8601String(),
          });
        } catch (e) {
          // Log error but continue with other images
          // In production, use proper logging framework
          debugPrint('Failed to upload image $i: $e');
        }
      }

      // Update product with images if any were uploaded
      if (uploadedImages.isNotEmpty) {
        debugPrint('💾 Updating product with ${uploadedImages.length} images');
        final updatedResponse = await SupabaseConfig.products
            .update({'images': uploadedImages})
            .eq('id', createdProduct.id)
            .select()
            .single();

        debugPrint('✅ Product updated with images successfully');
        return ProductModel.fromJson(updatedResponse);
      }

      debugPrint('✅ Product creation completed');
      return createdProduct;
    } catch (e) {
      debugPrint('❌ Error in createProductWithImages: $e');
      debugPrint('📍 Stack trace: ${StackTrace.current}');
      throw Exception('فشل في إنشاء المنتج مع الصور: ${e.toString()}');
    }
  }

  Future<void> deleteProductImage(String productId, String imageId) async {
    try {
      // Get current product
      final product = await getProductById(productId);

      // Find and remove the image
      final updatedImages = product.images.where((img) => img.id != imageId).toList();

      // Find the image to delete from storage
      final imageToDelete = product.images.firstWhere((img) => img.id == imageId);

      // Delete from storage
      if (imageToDelete.imagePath != null && imageToDelete.imagePath!.isNotEmpty) {
        await SupabaseConfig.storage
            .from('product-images')
            .remove([imageToDelete.imagePath!]);
      }

      // Update product with new images array
      await SupabaseConfig.products
          .update({'images': updatedImages.map((img) => img.toJson()).toList()})
          .eq('id', productId);
    } catch (e) {
      throw Exception('فشل في حذف الصورة: ${e.toString()}');
    }
  }

  Future<List<ProductImageModel>> getProductImages(String productId) async {
    try {
      final product = await getProductById(productId);
      return product.images;
    } catch (e) {
      throw Exception('فشل في جلب صور المنتج: ${e.toString()}');
    }
  }

  Future<void> updateImageOrder(String productId, String imageId, int newOrder) async {
    try {
      // Get current product
      final product = await getProductById(productId);

      // Update the specific image's sort order
      final updatedImages = product.images.map((img) {
        if (img.id == imageId) {
          return img.copyWith(sortOrder: newOrder);
        }
        return img;
      }).toList();

      // Sort by new order
      updatedImages.sort((a, b) => a.sortOrder.compareTo(b.sortOrder));

      // Update product with new images array
      await SupabaseConfig.products
          .update({'images': updatedImages.map((img) => img.toJson()).toList()})
          .eq('id', productId);
    } catch (e) {
      throw Exception('فشل في تحديث ترتيب الصورة: ${e.toString()}');
    }
  }

  Future<void> setPrimaryImage(String productId, String imageId) async {
    try {
      // Get current product
      final product = await getProductById(productId);

      // Update images: set all to non-primary, then set the selected one as primary
      final updatedImages = product.images.map((img) {
        return img.copyWith(isPrimary: img.id == imageId);
      }).toList();

      // Update product with new images array
      await SupabaseConfig.products
          .update({'images': updatedImages.map((img) => img.toJson()).toList()})
          .eq('id', productId);
    } catch (e) {
      throw Exception('فشل في تحديد الصورة الرئيسية: ${e.toString()}');
    }
  }

  // Add new images to existing product
  Future<ProductModel> addProductImages(String productId, List<XFile> imageFiles) async {
    try {
      debugPrint('📷 Adding ${imageFiles.length} new images to product: $productId');

      // Get current product
      final product = await getProductById(productId);
      debugPrint('📦 Current product has ${product.images.length} existing images');

      // Upload new images
      List<Map<String, dynamic>> newImages = [];
      final currentImageCount = product.images.length;

      for (int i = 0; i < imageFiles.length; i++) {
        try {
          debugPrint('📤 Uploading new image ${i + 1}/${imageFiles.length}');
          final imageUrl = await uploadProductImage(productId, imageFiles[i], currentImageCount + i);
          final fileName = '${productId}_${DateTime.now().millisecondsSinceEpoch}_${currentImageCount + i}.${imageFiles[i].path.split('.').last}';
          final imagePath = 'products/$productId/$fileName';

          debugPrint('✅ New image ${i + 1} uploaded: $imageUrl');

          newImages.add({
            'id': '${productId}_${currentImageCount + i}',
            'product_id': productId,
            'image_url': imageUrl,
            'image_path': imagePath,
            'sort_order': currentImageCount + i,
            'is_primary': product.images.isEmpty && i == 0, // First image is primary if no existing images
            'created_at': DateTime.now().toIso8601String(),
          });
        } catch (e) {
          debugPrint('❌ Failed to upload new image $i: $e');
        }
      }

      if (newImages.isNotEmpty) {
        // Combine existing images with new images
        final allImages = [
          ...product.images.map((img) => img.toJson()),
          ...newImages,
        ];

        debugPrint('💾 Updating product with ${allImages.length} total images');
        final response = await SupabaseConfig.products
            .update({'images': allImages})
            .eq('id', productId)
            .select()
            .single();

        debugPrint('✅ Product updated with new images successfully');
        return ProductModel.fromJson(response);
      }

      debugPrint('⚠️ No new images were uploaded successfully');
      return product;
    } catch (e) {
      debugPrint('❌ Error adding product images: $e');
      throw Exception('فشل في إضافة الصور للمنتج: ${e.toString()}');
    }
  }
}
