{"version": 3, "file": "gax.js", "sourceRoot": "", "sources": ["../../src/gax.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAsTH,kDAiGC;AAaD,gDAYC;AA2BD,sDAkBC;AAED,oEAEC;AA2BD,0EAkBC;AAUD,kDAkCC;AA2MD,8CAsEC;AAED,4DAeC;AAr1BD,yCAAgC;AAGhC,iCAAwC;AACxC,qCAAgC;AAGhC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwCG;AAEH;;;;;;;;GAQG;AACH,MAAa,YAAY;IAKvB,YACE,UAAoB,EACpB,eAAgC,EAChC,aAA+C,EAC/C,sBAA8D;QAE9D,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAC;IACvD,CAAC;CACF;AAhBD,oCAgBC;AA+ED,MAAa,YAAY;IAevB;;;;;;;;;;;;;;;;;OAiBG;IACH,YAAY,QAAsB;;QAChC,QAAQ,GAAG,QAAQ,IAAI,EAAE,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,IAAI,EAAE,GAAG,IAAI,CAAC;QAC7C,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAqB,CAAC;QAC5C,IAAI,CAAC,YAAY;YACf,cAAc,IAAI,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5D,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;QACtC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,IAAI,EAAE,CAAC;QAC1C,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;QAC5C,IAAI,CAAC,UAAU,GAAG,YAAY,IAAI,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAW,CAAC,CAAC,CAAC,IAAI,CAAC;QACzE,IAAI,CAAC,WAAW;YACd,aAAa,IAAI,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;QAC/D,IAAI,CAAC,OAAO,GAAG,MAAA,QAAQ,CAAC,OAAO,mCAAI,SAAS,CAAC;QAC7C,IAAI,CAAC,mBAAmB,GAAG,QAAQ,CAAC,mBAAmB,CAAC;IAC1D,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,OAA4B;QAChC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;QAChC,CAAC;QACD,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3B,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACvB,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACrC,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjC,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAC/B,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACjC,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACnC,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3B,IAAI,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAEnD,gFAAgF;QAChF,oCAAoC;QACpC,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;YACzB,OAAO,GAAG,OAAO,CAAC,OAAQ,CAAC;QAC7B,CAAC;QACD,0FAA0F;QAC1F,+DAA+D;QAC/D,6BAA6B;QAC7B,IAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,UAAU,EAAE,CAAC;YACtB,KAAM,CAAC,eAAe,CAAC,uBAAuB,GAAG,OAAO,CAAC;YACzD,KAAM,CAAC,eAAe,CAAC,mBAAmB,GAAG,OAAO,CAAC;YACrD,KAAM,CAAC,eAAe,CAAC,kBAAkB,GAAG,OAAO,CAAC;QACtD,CAAC;QAED,IAAI,OAAO,IAAI,OAAO,EAAE,CAAC;YACvB,KAAK,GAAG,iBAAiB,CAAC,KAAK,IAAK,EAAmB,EAAE,OAAO,CAAC,KAAM,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,cAAc,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YACvD,YAAY,GAAG,KAAK,CAAC;QACvB,CAAC;QAED,IAAI,YAAY,IAAI,OAAO,EAAE,CAAC;YAC5B,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QAClC,CAAC;QAED,IAAI,WAAW,IAAI,OAAO,EAAE,CAAC;YAC3B,SAAS,GAAG,EAAE,CAAC;YACf,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACvC,CAAC;YACD,KAAK,MAAM,UAAU,IAAI,OAAO,CAAC,SAAU,EAAE,CAAC;gBAC5C,SAAS,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC,UAAU,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;QAED,IAAI,YAAY,IAAI,OAAO,EAAE,CAAC;YAC5B,UAAU,GAAG,OAAO,CAAC,UAAW,CAAC;QACnC,CAAC;QAED,IAAI,YAAY,IAAI,OAAO,IAAI,OAAO,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YAChE,KAAM,CAAC,eAAgB,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;YACxD,OAAO,KAAM,CAAC,eAAgB,CAAC,kBAAkB,CAAC;QACpD,CAAC;QAED,IAAI,aAAa,IAAI,OAAO,EAAE,CAAC;YAC7B,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACpC,CAAC;QACD,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;YACzB,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC5B,CAAC;QACD,IAAI,qBAAqB,IAAI,OAAO,EAAE,CAAC;YACrC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC;QACpD,CAAC;QAED,OAAO,IAAI,YAAY,CAAC;YACtB,OAAO;YACP,KAAK;YACL,aAAa,EAAE,IAAI,CAAC,aAAa;YACjC,WAAW;YACX,YAAY;YACZ,UAAU;YACV,SAAS;YACT,UAAU;YACV,OAAO;YACP,mBAAmB;SACpB,CAAC,CAAC;IACL,CAAC;CACF;AA3ID,oCA2IC;AAED;;;;;;;;GAQG;AACH,SAAgB,mBAAmB,CACjC,OAAqB,EACrB,mBAA6B;;IAE7B,4EAA4E;IAC5E,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,gFAAgF;IAChF,0CAA0C;IAC1C,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACzB,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;QACjD,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;IACzE,CAAC,CAAC,wEAAwE;IAC1E,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;QAChC,IAAI,OAAO,CAAC,mBAAmB,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACzD,IAAA,eAAI,EACF,uBAAuB,EACvB,uFAAuF,EACvF,6BAA6B,CAC9B,CAAC;QACJ,CAAC;QACD,IAAI,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;YAChE,IAAA,eAAI,EACF,uBAAuB,EACvB,uHAAuH,EACvH,6BAA6B,CAC9B,CAAC;QACJ,CAAC;QACD,IAAI,OAAO,CAAC,mBAAmB,CAAC,mBAAmB,KAAK,SAAS,EAAE,CAAC;YAClE,IAAA,eAAI,EACF,uBAAuB,EACvB,uFAAuF,EACvF,6BAA6B,CAC9B,CAAC;QACJ,CAAC;QAED,IAAI,UAAU,GAAG,CAAC,eAAM,CAAC,WAAW,CAAC,CAAC;QACtC,IAAI,aAAa,CAAC;QAClB,IAAI,OAAO,CAAC,mBAAmB,CAAC,aAAa,EAAE,CAAC;YAC9C,UAAU,GAAG,EAAE,CAAC;YAChB,aAAa,GAAG,OAAO,CAAC,mBAAmB,CAAC,aAAa,CAAC;QAC5D,CAAC;QAED,kBAAkB;QAClB,OAAO,CAAC,UAAU;YAChB,MAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,mBAAmB,0CAAE,OAAO,mCAAI,OAAO,CAAC,UAAU,CAAC;QAC9D,oGAAoG;QACpG,MAAM,eAAe,GAAG,4BAA4B,EAAE,CAAC;QACvD,IAAI,mBAAmB,CAAC;QACxB,IAAI,kBAAkB,CAAC;QACvB,sEAAsE;QACtE,IAAI,OAAO,CAAC,mBAAmB,CAAC,aAAa,KAAK,SAAS,EAAE,CAAC;YAC5D,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC;QACzE,CAAC;QACD,gFAAgF;QAChF,MAAM,oBAAoB,GACxB,MAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,mBAAmB,0CAAE,oBAAoB,mCAClD,eAAe,CAAC,oBAAoB,CAAC;QACvC,oGAAoG;QACpG,IAAI,OAAO,CAAC,mBAAmB,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;YAC3D,kBAAkB,GAAG,OAAO,CAAC,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC;QACvE,CAAC;aAAM,CAAC;YACN,IAAI,OAAO,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;gBACrC,kBAAkB,GAAG,KAAK,CAAC;gBAC3B,IAAA,eAAI,EACF,8CAA8C,EAC9C,yFAAyF,EACzF,yBAAyB,CAC1B,CAAC;YACJ,CAAC;QACH,CAAC;QAED,gGAAgG;QAChG,eAAe,CAAC,mBAAmB;YACjC,mBAAmB,aAAnB,mBAAmB,cAAnB,mBAAmB,GAAI,eAAe,CAAC,mBAAmB,CAAC;QAC7D,eAAe,CAAC,oBAAoB;YAClC,oBAAoB,aAApB,oBAAoB,cAApB,oBAAoB,GAAI,eAAe,CAAC,oBAAoB,CAAC;QAC/D,eAAe,CAAC,kBAAkB;YAChC,kBAAkB,aAAlB,kBAAkB,cAAlB,kBAAkB,GAAI,eAAe,CAAC,kBAAkB,CAAC;QAE3D,MAAM,qBAAqB,GAAG,kBAAkB,CAC9C,UAAU,EACV,eAAe,EACf,aAAa,CACd,CAAC;QACF,OAAO,CAAC,KAAK,GAAG,qBAAqB,CAAC;QACtC,OAAO,OAAO,CAAC,mBAAmB,CAAC,CAAC,wDAAwD;QAC5F,IAAA,eAAI,EACF,uBAAuB,EACvB,wHAAwH,EACxH,oBAAoB,CACrB,CAAC;IACJ,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAgB,kBAAkB,CAChC,UAAoB,EACpB,eAAgC,EAChC,aAA+C,EAC/C,sBAA8D;IAE9D,OAAO;QACL,UAAU;QACV,eAAe;QACf,aAAa;QACb,sBAAsB;KACvB,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,SAAgB,qBAAqB,CACnC,uBAA+B,EAC/B,oBAA4B,EAC5B,mBAA2B,EAC3B,uBAAsC,EACtC,oBAAmC,EACnC,mBAAkC,EAClC,kBAAiC;IAEjC,OAAO;QACL,uBAAuB;QACvB,oBAAoB;QACpB,mBAAmB;QACnB,uBAAuB;QACvB,oBAAoB;QACpB,mBAAmB;QACnB,kBAAkB;KACnB,CAAC;AACJ,CAAC;AAED,SAAgB,4BAA4B;IAC1C,OAAO,qBAAqB,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AACxE,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,SAAgB,+BAA+B,CAC7C,uBAA+B,EAC/B,oBAA4B,EAC5B,mBAA2B,EAC3B,uBAA+B,EAC/B,oBAA4B,EAC5B,mBAA2B,EAC3B,UAAkB;IAElB,OAAO;QACL,uBAAuB;QACvB,oBAAoB;QACpB,mBAAmB;QACnB,uBAAuB;QACvB,oBAAoB;QACpB,mBAAmB;QACnB,UAAU;KACX,CAAC;AACJ,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,mBAAmB,CAAC,OAAuB;IACzD,MAAM,MAAM,GAAgC;QAC1C,yBAAyB;QACzB,qBAAqB;QACrB,wBAAwB;QACxB,oBAAoB;QACpB,wBAAwB;KACzB,CAAC;IACF,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QACrB,IAAI,KAAK,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE,CAAC;YAC3D,MAAM,IAAI,KAAK,CAAC,GAAG,KAAK,qBAAqB,CAAC,CAAC;QACjD,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,qBAAqB,GAAG,OAAO,CAAC,uBAAuB,IAAI,CAAC,CAAC;IACnE,MAAM,iBAAiB,GAAG,OAAO,CAAC,mBAAmB,IAAI,CAAC,CAAC;IAC3D,MAAM,oBAAoB,GAAG,OAAO,CAAC,sBAAsB,IAAI,CAAC,CAAC;IACjE,MAAM,gBAAgB,GAAG,OAAO,CAAC,kBAAkB,IAAI,CAAC,CAAC;IACzD,MAAM,cAAc,GAAG,OAAO,CAAC,sBAAsB,IAAI,CAAC,CAAC;IAE3D,IACE,qBAAqB,KAAK,CAAC;QAC3B,oBAAoB,KAAK,CAAC;QAC1B,cAAc,KAAK,CAAC,EACpB,CAAC;QACD,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACjD,CAAC;IACD,OAAO;QACL,qBAAqB;QACrB,iBAAiB;QACjB,oBAAoB;QACpB,gBAAgB;QAChB,cAAc;KACf,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACH,SAAS,cAAc,CACrB,YAAiC,EACjC,UAAmD,EACnD,WAA8C,EAC9C,UAAiC;IAEjC,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,KAAK,GAAoB,IAAI,CAAC,CAAC,uHAAuH;IAC1J,IAAI,UAAU,IAAI,kBAAkB,IAAI,YAAY,EAAE,CAAC;QACrD,MAAM,cAAc,GAAG,YAAY,CAAC,kBAAkB,CAAC,CAAC;QACxD,KAAK,GAAG,CAAC,UAAU,CAAC,cAAe,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACrD,OAAO,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,eAAe,GAA2B,IAAI,CAAC;IACnD,IAAI,WAAW,IAAI,mBAAmB,IAAI,YAAY,EAAE,CAAC;QACvD,MAAM,MAAM,GAAG,WAAW,CACxB,YAAY,CAAC,iBAAkB,CACX,CAAC;QACvB,eAAe,GAAG,qBAAqB,CACrC,MAAM,CAAC,0BAA0B,EACjC,MAAM,CAAC,sBAAsB,EAC7B,MAAM,CAAC,sBAAsB,EAC7B,MAAM,CAAC,0BAA0B,EACjC,MAAM,CAAC,sBAAsB,EAC7B,MAAM,CAAC,sBAAsB,EAC7B,MAAM,CAAC,oBAAoB,CAC5B,CAAC;IACJ,CAAC;IACD,OAAO,kBAAkB,CAAC,KAAM,EAAE,eAAgB,CAAC,CAAC;AACtD,CAAC;AAED;;;;;;;;;;;;GAYG;AACH,SAAS,iBAAiB,CACxB,KAAmB,EACnB,SAAgC;IAEhC,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IACE,CAAC,SAAS,CAAC,UAAU;QACrB,CAAC,SAAS,CAAC,eAAe;QAC1B,CAAC,SAAS,CAAC,aAAa;QACxB,CAAC,SAAS,CAAC,sBAAsB,EACjC,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,UAAU,GAAG,SAAS,CAAC,UAAU;QACrC,CAAC,CAAC,SAAS,CAAC,UAAU;QACtB,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC;IAErB,MAAM,eAAe,GAAG,SAAS,CAAC,eAAe;QAC/C,CAAC,CAAC,SAAS,CAAC,eAAe;QAC3B,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC;IAC1B,MAAM,aAAa,GAAG,SAAS,CAAC,aAAa;QAC3C,CAAC,CAAC,SAAS,CAAC,aAAa;QACzB,CAAC,CAAC,KAAK,CAAC,aAAa,CAAC;IACxB,MAAM,sBAAsB,GAAG,SAAS,CAAC,sBAAsB;QAC7D,CAAC,CAAC,SAAS,CAAC,sBAAsB;QAClC,CAAC,CAAC,KAAK,CAAC,sBAAsB,CAAC;IACjC,OAAO,kBAAkB,CACvB,UAAW,EACX,eAAgB,EAChB,aAAc,EACd,sBAAuB,CACxB,CAAC;AACJ,CAAC;AAqCD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2DG;AACH,SAAgB,iBAAiB,CAC/B,WAAmB,EACnB,YAA0B,EAC1B,eAA6B,EAC7B,UAAc,EACd,SAAc;IAEd,SAAS,GAAG,SAAS,IAAI,EAAE,CAAC;IAC5B,8DAA8D;IAC9D,MAAM,QAAQ,GAAQ,EAAE,CAAC;IAEzB,MAAM,aAAa,GAAG,CAAC,YAAY,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,WAAW,CAAC,CAAC;IACnE,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,yEAAyE;IACzE,mIAAmI;IACnI,wCAAwC;IACxC,EAAE;IACF,oGAAoG;IACpG,0DAA0D;IAE1D,MAAM,SAAS,GAAG,CAAC,eAAe,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;IACxE,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;IACtC,MAAM,iBAAiB,GAAG,SAAS,CAAC,OAAO,IAAI,EAAE,CAAC;IAClD,KAAK,MAAM,UAAU,IAAI,OAAO,EAAE,CAAC;QACjC,MAAM,YAAY,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;QACzC,MAAM,MAAM,GAAG,IAAA,uBAAgB,EAAC,UAAU,CAAC,CAAC;QAE5C,IAAI,KAAK,GAAG,cAAc,CACxB,YAAY,EACZ,aAAa,CAAC,WAAW,EACzB,aAAa,CAAC,YAAY,EAC1B,UAAU,CACX,CAAC;QACF,IAAI,cAAc,GAAG,YAAa,CAAC,QAAQ,CAAC;QAC5C,IAAI,OAAO,GAAG,YAAa,CAAC,cAAc,CAAC;QAC3C,IAAI,UAAU,IAAI,iBAAiB,EAAE,CAAC;YACpC,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,UAAU,CAAC,CAAC;YACvD,IAAI,gBAAgB,EAAE,CAAC;gBACrB,IAAI,UAAU,IAAI,gBAAgB,EAAE,CAAC;oBACnC,cAAc,GAAG,gBAAgB,CAAC,QAAQ,CAAC;gBAC7C,CAAC;gBACD,IAAI,gBAAgB,IAAI,gBAAgB,EAAE,CAAC;oBACzC,OAAO,GAAG,gBAAgB,CAAC,cAAc,CAAC;gBAC5C,CAAC;YACH,CAAC;YACD,KAAK,GAAG,iBAAiB,CACvB,KAAM,EACN,cAAc,CACZ,gBAAgB,EAChB,SAAS,CAAC,WAAW,EACrB,SAAS,CAAC,YAAY,EACtB,UAAU,CACV,CACH,CAAC;QACJ,CAAC;QACD,MAAM,OAAO,GAAG,WAAW,CAAC;QAC5B,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,YAAY,CAAC;YAClC,OAAO;YACP,KAAK;YACL,aAAa,EAAE,cAAc;gBAC3B,CAAC,CAAC,mBAAmB,CAAC,cAAc,CAAC;gBACrC,CAAC,CAAC,IAAI;YACR,SAAS;YACT,OAAO;SACR,CAAC,CAAC;IACL,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAgB,wBAAwB,CAAC,OAAuB;IAC9D,OAAO,SAAS,aAAa,CAAC,GAAO;QACnC,IAAI,CAAC;YACH,OAAO,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC;QAC7C,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACxC,IAAA,eAAI,EACF,kCAAkC,EAClC,qCAAqC,WAAW,KAAK,GAAG,EAAE,CAC3D,CAAC;YACF,8FAA8F;YAC9F,mGAAmG;YACnG,OAAO,WAAW,CAAC,MAAM,CAAC;QAC5B,CAAC;IACH,CAAC,CAAC;AACJ,CAAC"}