# دليل نظام الإشعارات Firebase - Diet Rx

## 📋 نظرة عامة

تم تطوير نظام إشعارات شامل لتطبيق Diet Rx يتيح إرسال إشعارات مخصصة لكل مريض في أوقات محددة. النظام يدعم أربعة أنواع من التذكيرات:

- 🍽️ **تذكيرات الوجبات**
- 🏃‍♂️ **تذكيرات النشاط البدني**
- 💊 **تذكيرات الأدوية**
- 💧 **تذكيرات شرب الماء**

## 🏗️ بنية النظام

### 1. قواعد البيانات (Supabase)

#### جدول `user_fcm_tokens`
```sql
CREATE TABLE user_fcm_tokens (
  id UUID PRIMARY KEY,
  user_id UUID NOT NULL,
  fcm_token TEXT UNIQUE NOT NULL,
  device_info JSONB,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### جدول `scheduled_notifications`
```sql
CREATE TABLE scheduled_notifications (
  id UUID PRIMARY KEY,
  patient_id UUID NOT NULL,
  reminder_id UUID NOT NULL,
  notification_type VARCHAR(50) NOT NULL,
  title TEXT NOT NULL,
  body TEXT NOT NULL,
  scheduled_time TIME NOT NULL,
  days_of_week INTEGER[] NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### جدول `notification_logs`
```sql
CREATE TABLE notification_logs (
  id UUID PRIMARY KEY,
  scheduled_notification_id UUID,
  patient_id UUID NOT NULL,
  fcm_token TEXT NOT NULL,
  title TEXT NOT NULL,
  body TEXT NOT NULL,
  sent_at TIMESTAMP DEFAULT NOW(),
  status VARCHAR(20) DEFAULT 'sent',
  firebase_response JSONB,
  error_message TEXT
);
```

### 2. الخدمات الأساسية

#### `FirebaseMessagingService`
- إرسال الإشعارات عبر Firebase Cloud Messaging API V1
- دعم الإشعارات المخصصة لكل نوع تذكير
- معالجة أخطاء الإرسال

#### `NotificationSchedulerService`
- إدارة FCM tokens للمستخدمين
- إنشاء وتحديث وحذف الإشعارات المجدولة
- إرسال الإشعارات الفورية
- تسجيل سجل الإشعارات

#### `NotificationAutomationService`
- تشغيل خدمة الإشعارات التلقائية
- فحص الإشعارات المستحقة كل دقيقة
- إرسال الإشعارات في الأوقات المحددة

## 🚀 كيفية الاستخدام

### 1. إعداد Firebase

1. **إضافة ملف المفاتيح:**
   ```
   assets/key/deit-rx-30741-5972f5ebce39.json
   ```

2. **معرف المشروع:**
   ```
   deit-rx-30741
   ```

### 2. حفظ FCM Token للمستخدم

```dart
await NotificationSchedulerService.saveFCMToken(
  userId: 'user_id_here',
  fcmToken: 'fcm_token_here',
  deviceInfo: {
    'platform': 'android',
    'model': 'Samsung Galaxy S21',
    'version': '12.0',
  },
);
```

### 3. إنشاء إشعار مجدول

```dart
final notificationId = await NotificationSchedulerService.createScheduledNotification(
  patientId: 'patient_id',
  reminderId: 'reminder_id',
  notificationType: 'meal', // meal, exercise, medication, water
  title: 'تذكير الإفطار',
  body: 'حان وقت تناول وجبة الإفطار الصحية',
  scheduledTime: '08:00', // HH:mm format
  daysOfWeek: [1, 2, 3, 4, 5], // Monday to Friday
);
```

### 4. إرسال إشعار فوري

```dart
final success = await NotificationSchedulerService.sendNotificationNow(
  patientId: 'patient_id',
  notificationType: 'meal',
  title: 'تذكير عاجل',
  body: 'لا تنس تناول وجبتك',
);
```

## 🎯 واجهة المستخدم

### 1. صفحة إدارة الإشعارات
- عرض جميع الإشعارات المجدولة للمريض
- فلترة حسب نوع التذكير
- إنشاء وتعديل وحذف الإشعارات
- تفعيل/إيقاف الإشعارات

### 2. حوار إنشاء الإشعار
- اختيار نوع التذكير
- تحديد العنوان والمحتوى
- اختيار الوقت
- تحديد أيام الأسبوع

### 3. بطاقة الإشعار
- عرض معلومات الإشعار
- أزرار التعديل والحذف
- مفتاح التفعيل/الإيقاف

## ⚙️ الإعدادات المطلوبة

### 1. إضافة الحزم في `pubspec.yaml`

```yaml
dependencies:
  http: ^1.2.2
  googleapis_auth: ^1.6.0

flutter:
  assets:
    - assets/key/
```

### 2. بدء خدمة الإشعارات في `main.dart`

```dart
void main() async {
  // ... other initialization
  
  // Start notification automation service
  NotificationAutomationService.startAutomation();
  
  runApp(const DietRxApp());
}
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **فشل في إرسال الإشعار:**
   - تحقق من صحة FCM token
   - تأكد من وجود ملف المفاتيح
   - تحقق من اتصال الإنترنت

2. **عدم وصول الإشعارات:**
   - تأكد من تفعيل الإشعارات في التطبيق
   - تحقق من إعدادات النظام
   - تأكد من أن التطبيق ليس في وضع توفير البطارية

3. **مشاكل في التوقيت:**
   - تحقق من المنطقة الزمنية
   - تأكد من تشغيل خدمة الإشعارات التلقائية

## 📊 مراقبة النظام

### سجلات الإشعارات
يتم تسجيل جميع الإشعارات المرسلة في جدول `notification_logs` مع:
- حالة الإرسال (sent/failed)
- رد Firebase
- رسائل الخطأ (إن وجدت)

### إحصائيات الأداء
- عدد الإشعارات المرسلة يومياً
- معدل نجاح الإرسال
- أكثر أنواع التذكيرات استخداماً

## 🔐 الأمان

- استخدام Firebase Cloud Messaging API V1
- تشفير ملف المفاتيح
- التحقق من صحة البيانات قبل الإرسال
- تسجيل جميع العمليات للمراجعة

## 📱 دعم المنصات

- ✅ Android
- ✅ iOS
- ✅ Web (محدود)

## 🚀 التطوير المستقبلي

- إضافة إشعارات غنية (Rich Notifications)
- دعم الصور في الإشعارات
- إحصائيات متقدمة
- إشعارات جماعية
- جدولة متقدمة (شهرية، سنوية)

## 📞 الدعم

للمساعدة والاستفسارات:
- البريد الإلكتروني: <EMAIL>
- المطور: Khwass Tech
