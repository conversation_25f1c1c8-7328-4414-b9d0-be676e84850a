{"name": "slash", "version": "3.0.0", "description": "Convert Windows backslash paths to slash paths", "license": "MIT", "repository": "sindresorhus/slash", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["path", "seperator", "slash", "backslash", "windows", "convert"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}