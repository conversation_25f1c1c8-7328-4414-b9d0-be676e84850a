import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/article_category_model.dart';

class ArticleCategoriesRepository {
  Future<List<ArticleCategoryModel>> getAllCategories() async {
    try {
      final response = await SupabaseConfig.client
          .from('article_categories')
          .select()
          .eq('is_active', true)
          .order('sort_order', ascending: true)
          .order('name', ascending: true);

      return response
          .map<ArticleCategoryModel>(
            (json) => ArticleCategoryModel.fromJson(json),
          )
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب أقسام المقالات: ${e.toString()}');
    }
  }

  Future<ArticleCategoryModel> getCategoryById(String id) async {
    try {
      final response =
          await SupabaseConfig.client
              .from('article_categories')
              .select()
              .eq('id', id)
              .single();

      return ArticleCategoryModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في جلب القسم: ${e.toString()}');
    }
  }

  Future<ArticleCategoryModel> createCategory(
    ArticleCategoryModel category,
  ) async {
    try {
      final response =
          await SupabaseConfig.client
              .from('article_categories')
              .insert({
                'name': category.name,
                'description': category.description,
                'icon': category.icon,
                'color': category.color,
                'sort_order': category.sortOrder,
                'is_active': category.isActive,
              })
              .select()
              .single();

      return ArticleCategoryModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في إضافة القسم: ${e.toString()}');
    }
  }

  Future<ArticleCategoryModel> updateCategory(
    ArticleCategoryModel category,
  ) async {
    try {
      final response =
          await SupabaseConfig.client
              .from('article_categories')
              .update({
                'name': category.name,
                'description': category.description,
                'icon': category.icon,
                'color': category.color,
                'sort_order': category.sortOrder,
                'is_active': category.isActive,
              })
              .eq('id', category.id)
              .select()
              .single();

      return ArticleCategoryModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في تحديث القسم: ${e.toString()}');
    }
  }

  Future<void> deleteCategory(String categoryId) async {
    try {
      await SupabaseConfig.client
          .from('article_categories')
          .delete()
          .eq('id', categoryId);
    } catch (e) {
      throw Exception('فشل في حذف القسم: ${e.toString()}');
    }
  }

  Future<ArticleCategoryModel> toggleCategoryStatus(String id) async {
    try {
      // Get current category
      final current = await getCategoryById(id);

      // Toggle the status
      final response =
          await SupabaseConfig.client
              .from('article_categories')
              .update({'is_active': !current.isActive})
              .eq('id', id)
              .select()
              .single();

      return ArticleCategoryModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في تغيير حالة القسم: ${e.toString()}');
    }
  }

  Future<List<ArticleCategoryModel>> searchCategories(String query) async {
    try {
      final response = await SupabaseConfig.client
          .from('article_categories')
          .select()
          .or('name.ilike.%$query%,description.ilike.%$query%')
          .eq('is_active', true)
          .order('sort_order', ascending: true)
          .order('name', ascending: true);

      return response
          .map<ArticleCategoryModel>(
            (json) => ArticleCategoryModel.fromJson(json),
          )
          .toList();
    } catch (e) {
      throw Exception('فشل في البحث في الأقسام: ${e.toString()}');
    }
  }

  Future<int> getCategoryArticlesCount(String categoryId) async {
    try {
      final response = await SupabaseConfig.client
          .from('articles')
          .select('id')
          .eq('category_id', categoryId)
          .eq('is_published', true);

      return response.length;
    } catch (e) {
      throw Exception('فشل في جلب عدد المقالات: ${e.toString()}');
    }
  }
}
