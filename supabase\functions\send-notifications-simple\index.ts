import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    console.log('🔍 Checking for due notifications...')
    
    const startTime = Date.now()
    
    // Get current time in Riyadh timezone
    const now = new Date()
    // Convert to Riyadh time (UTC+3)
    const riyadhTime = new Date(now.getTime() + (3 * 60 * 60 * 1000))
    const currentTime = `${riyadhTime.getHours().toString().padStart(2, '0')}:${riyadhTime.getMinutes().toString().padStart(2, '0')}`
    const currentDayOfWeek = riyadhTime.getDay() === 0 ? 7 : riyadhTime.getDay()
    
    console.log(`Current Riyadh time: ${currentTime}, Day: ${currentDayOfWeek}`)
    
    // Get due notifications
    const { data: notifications, error } = await supabase
      .from('scheduled_notifications')
      .select('*')
      .eq('is_active', true)
      .eq('scheduled_time', currentTime)
    
    if (error) {
      console.error('❌ Error fetching notifications:', error)
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    
    // Filter by day of week
    const dueNotifications = notifications.filter(notification => {
      const daysOfWeek = notification.days_of_week || []
      return daysOfWeek.includes(currentDayOfWeek)
    })
    
    console.log(`📊 Found ${dueNotifications.length} due notifications`)
    
    // Process each notification
    let successCount = 0
    for (const notification of dueNotifications) {
      const success = await processNotification(notification, supabase)
      if (success) successCount++
    }
    
    const endTime = Date.now()
    const duration = endTime - startTime
    
    const result = {
      success: true,
      processed: dueNotifications.length,
      successful: successCount,
      failed: dueNotifications.length - successCount,
      time: currentTime,
      day: currentDayOfWeek,
      duration: `${duration}ms`
    }
    
    if (dueNotifications.length > 0) {
      console.log(`✅ Processed ${dueNotifications.length} notifications in ${duration}ms`)
    } else {
      console.log(`ℹ️ No due notifications found (${duration}ms)`)
    }
    
    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    console.error('❌ Error in scheduled notifications:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

async function processNotification(notification: any, supabase: any): Promise<boolean> {
  try {
    const patientId = notification.patient_id
    
    // Get patient info
    const { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('auth_id, name')
      .eq('id', patientId)
      .single()
    
    if (patientError || !patient) {
      console.error(`❌ Patient not found: ${patientId}`)
      return false
    }
    
    // Get FCM tokens
    const { data: tokens, error: tokensError } = await supabase
      .from('user_fcm_tokens')
      .select('fcm_token')
      .eq('user_id', patient.auth_id)
      .eq('is_active', true)
    
    if (tokensError || !tokens || tokens.length === 0) {
      console.error(`❌ No FCM tokens found for patient: ${patientId} (auth_id: ${patient.auth_id})`)
      return false
    }
    
    console.log(`📱 Found ${tokens.length} FCM token(s) for patient: ${patient.name}`)
    
    // Prepare notification message
    const title = getNotificationTitle(notification.notification_type)
    const body = `مرحباً ${patient.name}، ${notification.body}`
    
    // Send real FCM notifications
    console.log(`📧 Sending FCM notification for ${patient.name}: ${title} - ${body}`)

    // Log all notifications and send FCM
    let allSuccess = true
    for (const tokenData of tokens) {
      try {
        // Send FCM notification
        const fcmSuccess = await sendFCMNotification(tokenData.fcm_token, title, body, {
          type: notification.notification_type,
          patient_id: patientId,
          reminder_id: notification.reminder_id || '',
          scheduled_notification_id: notification.id,
        })

        // Log notification with actual FCM result
        await supabase.from('notification_logs').insert({
          scheduled_notification_id: notification.id,
          patient_id: patientId,
          fcm_token: tokenData.fcm_token,
          title: title,
          body: body,
          status: fcmSuccess ? 'sent' : 'failed',
          firebase_response: fcmSuccess ? { messageId: `fcm-${Date.now()}`, success: true } : { error: 'FCM send failed' }
        })

        if (fcmSuccess) {
          console.log(`✅ FCM sent successfully for token: ${tokenData.fcm_token.substring(0, 20)}...`)
        } else {
          console.error(`❌ FCM failed for token: ${tokenData.fcm_token.substring(0, 20)}...`)
          allSuccess = false
        }

      } catch (logError) {
        console.error(`❌ Failed to send/log notification:`, logError)
        allSuccess = false

        // Log the error
        await supabase.from('notification_logs').insert({
          scheduled_notification_id: notification.id,
          patient_id: patientId,
          fcm_token: tokenData.fcm_token,
          title: title,
          body: body,
          status: 'failed',
          error_message: logError.message
        })
      }
    }
    
    return allSuccess
    
  } catch (error) {
    console.error('❌ Error processing notification:', error)
    return false
  }
}

// Helper function to get notification title
function getNotificationTitle(notificationType: string): string {
  switch (notificationType) {
    case 'meal':
      return '🍽️ تذكير الوجبة'
    case 'exercise':
      return '🏃‍♂️ تذكير النشاط البدني'
    case 'medication':
      return '💊 تذكير الدواء'
    case 'water':
      return '💧 تذكير شرب الماء'
    default:
      return '🔔 تذكير من Diet Rx'
  }
}

// Send FCM notification using HTTP v1 API (Modern approach)
async function sendFCMNotification(
  fcmToken: string,
  title: string,
  body: string,
  data: any
): Promise<boolean> {
  try {
    // Get Firebase Service Account JSON from environment
    const serviceAccountJson = Deno.env.get('FIREBASE_SERVICE_ACCOUNT_JSON')
    if (!serviceAccountJson) {
      console.error('❌ Firebase service account not configured')
      return false
    }

    const serviceAccount = JSON.parse(serviceAccountJson)

    // Get OAuth2 access token
    const accessToken = await getFirebaseAccessToken(serviceAccount)
    if (!accessToken) {
      console.error('❌ Failed to get Firebase access token')
      return false
    }

    // Prepare FCM v1 message
    const message = {
      message: {
        token: fcmToken,
        notification: {
          title: title,
          body: body
        },
        data: {
          type: data.type || 'notification',
          patient_id: data.patient_id || '',
          reminder_id: data.reminder_id || '',
          scheduled_notification_id: data.scheduled_notification_id || '',
          click_action: 'FLUTTER_NOTIFICATION_CLICK'
        },
        android: {
          priority: 'high',
          notification: {
            channel_id: 'diet_rx_notifications',
            sound: 'default',
            priority: 'high',
            default_sound: true,
            default_vibrate: true,
            click_action: 'FLUTTER_NOTIFICATION_CLICK'
          }
        },
        apns: {
          headers: {
            'apns-priority': '10'
          },
          payload: {
            aps: {
              alert: {
                title: title,
                body: body
              },
              sound: 'default',
              badge: 1,
              category: 'DIET_RX_NOTIFICATION'
            }
          }
        }
      }
    }

    // Send via FCM HTTP v1 API
    const response = await fetch(
      `https://fcm.googleapis.com/v1/projects/deit-rx-30741/messages:send`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        },
        body: JSON.stringify(message)
      }
    )

    const result = await response.json()

    if (response.ok) {
      console.log(`✅ FCM v1 notification sent successfully: ${result.name}`)
      return true
    } else {
      console.error(`❌ FCM v1 error: ${response.status}`, result)
      return false
    }

  } catch (error) {
    console.error('❌ Error sending FCM v1 notification:', error)
    return false
  }
}

// Get Firebase OAuth2 access token using Service Account
async function getFirebaseAccessToken(serviceAccount: any): Promise<string | null> {
  try {
    // Create JWT for Google OAuth2
    const now = Math.floor(Date.now() / 1000)
    const payload = {
      iss: serviceAccount.client_email,
      scope: 'https://www.googleapis.com/auth/firebase.messaging',
      aud: 'https://oauth2.googleapis.com/token',
      exp: now + 3600,
      iat: now
    }

    // Sign JWT (simplified - in production use proper JWT library)
    const jwt = await createJWT(payload, serviceAccount.private_key)

    // Exchange JWT for access token
    const response = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        assertion: jwt
      })
    })

    const result = await response.json()

    if (response.ok && result.access_token) {
      return result.access_token
    } else {
      console.error('❌ Failed to get access token:', result)
      return null
    }

  } catch (error) {
    console.error('❌ Error getting Firebase access token:', error)
    return null
  }
}

// Simple JWT creation (for demonstration - use proper library in production)
async function createJWT(payload: any, privateKey: string): Promise<string> {
  // This is a simplified JWT implementation
  // In production, use a proper JWT library
  const header = {
    alg: 'RS256',
    typ: 'JWT'
  }

  const encodedHeader = btoa(JSON.stringify(header)).replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_')
  const encodedPayload = btoa(JSON.stringify(payload)).replace(/=/g, '').replace(/\+/g, '-').replace(/\//g, '_')

  // For now, return a placeholder - you'll need to implement proper RSA signing
  // or use the existing Firebase Admin SDK approach
  throw new Error('JWT signing not implemented - use Firebase Admin SDK or proper JWT library')
}
