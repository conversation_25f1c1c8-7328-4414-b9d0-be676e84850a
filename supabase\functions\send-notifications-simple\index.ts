import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    console.log('🔍 Checking for due notifications...')
    
    const startTime = Date.now()
    
    // Get current time in Riyadh timezone
    const now = new Date()
    // Convert to Riyadh time (UTC+3)
    const riyadhTime = new Date(now.getTime() + (3 * 60 * 60 * 1000))
    const currentTime = `${riyadhTime.getHours().toString().padStart(2, '0')}:${riyadhTime.getMinutes().toString().padStart(2, '0')}`
    const currentDayOfWeek = riyadhTime.getDay() === 0 ? 7 : riyadhTime.getDay()
    
    console.log(`Current Riyadh time: ${currentTime}, Day: ${currentDayOfWeek}`)
    
    // Get due notifications
    const { data: notifications, error } = await supabase
      .from('scheduled_notifications')
      .select('*')
      .eq('is_active', true)
      .eq('scheduled_time', currentTime)
    
    if (error) {
      console.error('❌ Error fetching notifications:', error)
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    
    // Filter by day of week
    const dueNotifications = notifications.filter(notification => {
      const daysOfWeek = notification.days_of_week || []
      return daysOfWeek.includes(currentDayOfWeek)
    })
    
    console.log(`📊 Found ${dueNotifications.length} due notifications`)
    
    // Process each notification
    let successCount = 0
    for (const notification of dueNotifications) {
      const success = await processNotification(notification, supabase)
      if (success) successCount++
    }
    
    const endTime = Date.now()
    const duration = endTime - startTime
    
    const result = {
      success: true,
      processed: dueNotifications.length,
      successful: successCount,
      failed: dueNotifications.length - successCount,
      time: currentTime,
      day: currentDayOfWeek,
      duration: `${duration}ms`
    }
    
    if (dueNotifications.length > 0) {
      console.log(`✅ Processed ${dueNotifications.length} notifications in ${duration}ms`)
    } else {
      console.log(`ℹ️ No due notifications found (${duration}ms)`)
    }
    
    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    console.error('❌ Error in scheduled notifications:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

async function processNotification(notification: any, supabase: any): Promise<boolean> {
  try {
    const patientId = notification.patient_id
    
    // Get patient info
    const { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('auth_id, name')
      .eq('id', patientId)
      .single()
    
    if (patientError || !patient) {
      console.error(`❌ Patient not found: ${patientId}`)
      return false
    }
    
    // Get FCM tokens
    const { data: tokens, error: tokensError } = await supabase
      .from('user_fcm_tokens')
      .select('fcm_token')
      .eq('user_id', patient.auth_id)
      .eq('is_active', true)
    
    if (tokensError || !tokens || tokens.length === 0) {
      console.error(`❌ No FCM tokens found for patient: ${patientId} (auth_id: ${patient.auth_id})`)
      return false
    }
    
    console.log(`📱 Found ${tokens.length} FCM token(s) for patient: ${patient.name}`)
    
    // Prepare notification message
    const title = getNotificationTitle(notification.notification_type)
    const body = `مرحباً ${patient.name}، ${notification.body}`
    
    // Send real FCM notifications
    console.log(`📧 Sending FCM notification for ${patient.name}: ${title} - ${body}`)

    // Log all notifications and send FCM
    let allSuccess = true
    for (const tokenData of tokens) {
      try {
        // Send FCM notification
        const fcmSuccess = await sendFCMNotification(tokenData.fcm_token, title, body, {
          type: notification.notification_type,
          patient_id: patientId,
          reminder_id: notification.reminder_id || '',
          scheduled_notification_id: notification.id,
        })

        // Log notification with actual FCM result
        await supabase.from('notification_logs').insert({
          scheduled_notification_id: notification.id,
          patient_id: patientId,
          fcm_token: tokenData.fcm_token,
          title: title,
          body: body,
          status: fcmSuccess ? 'sent' : 'failed',
          firebase_response: fcmSuccess ? { messageId: `fcm-${Date.now()}`, success: true } : { error: 'FCM send failed' }
        })

        if (fcmSuccess) {
          console.log(`✅ FCM sent successfully for token: ${tokenData.fcm_token.substring(0, 20)}...`)
        } else {
          console.error(`❌ FCM failed for token: ${tokenData.fcm_token.substring(0, 20)}...`)
          allSuccess = false
        }

      } catch (logError) {
        console.error(`❌ Failed to send/log notification:`, logError)
        allSuccess = false

        // Log the error
        await supabase.from('notification_logs').insert({
          scheduled_notification_id: notification.id,
          patient_id: patientId,
          fcm_token: tokenData.fcm_token,
          title: title,
          body: body,
          status: 'failed',
          error_message: logError.message
        })
      }
    }
    
    return allSuccess
    
  } catch (error) {
    console.error('❌ Error processing notification:', error)
    return false
  }
}

// Helper function to get notification title
function getNotificationTitle(notificationType: string): string {
  switch (notificationType) {
    case 'meal':
      return '🍽️ تذكير الوجبة'
    case 'exercise':
      return '🏃‍♂️ تذكير النشاط البدني'
    case 'medication':
      return '💊 تذكير الدواء'
    case 'water':
      return '💧 تذكير شرب الماء'
    default:
      return '🔔 تذكير من Diet Rx'
  }
}

// Send FCM notification using Firebase Admin SDK approach
async function sendFCMNotification(
  fcmToken: string,
  title: string,
  body: string,
  data: any
): Promise<boolean> {
  try {
    // Firebase Server Key (Legacy) - استخدم Server Key من Firebase Console
    const serverKey = Deno.env.get('FIREBASE_SERVER_KEY')
    if (!serverKey) {
      console.error('❌ Firebase server key not configured')
      return false
    }

    const message = {
      to: fcmToken,
      notification: {
        title: title,
        body: body,
        sound: 'default',
        badge: '1'
      },
      data: {
        type: data.type || 'notification',
        patient_id: data.patient_id || '',
        reminder_id: data.reminder_id || '',
        scheduled_notification_id: data.scheduled_notification_id || '',
        click_action: 'FLUTTER_NOTIFICATION_CLICK'
      },
      android: {
        priority: 'high',
        notification: {
          channel_id: 'diet_rx_notifications',
          sound: 'default',
          priority: 'high',
          default_sound: true,
          default_vibrate: true
        }
      },
      apns: {
        headers: {
          'apns-priority': '10'
        },
        payload: {
          aps: {
            alert: {
              title: title,
              body: body
            },
            sound: 'default',
            badge: 1
          }
        }
      }
    }

    const response = await fetch('https://fcm.googleapis.com/fcm/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `key=${serverKey}`
      },
      body: JSON.stringify(message)
    })

    const result = await response.json()

    if (response.ok && result.success === 1) {
      console.log(`✅ FCM notification sent successfully: ${result.results?.[0]?.message_id}`)
      return true
    } else {
      console.error(`❌ FCM error: ${response.status}`, result)
      return false
    }

  } catch (error) {
    console.error('❌ Error sending FCM notification:', error)
    return false
  }
}
