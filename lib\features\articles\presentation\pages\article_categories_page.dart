import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/article_category_model.dart';
import '../../../../core/utils/debouncer.dart';
import '../bloc/article_categories_bloc.dart';
import '../bloc/article_categories_event.dart';
import '../bloc/article_categories_state.dart';
import 'article_category_form_page.dart';

class ArticleCategoriesPage extends StatefulWidget {
  const ArticleCategoriesPage({super.key});

  @override
  State<ArticleCategoriesPage> createState() => _ArticleCategoriesPageState();
}

class _ArticleCategoriesPageState extends State<ArticleCategoriesPage> {
  final TextEditingController _searchController = TextEditingController();
  final Debouncer _debouncer = Debouncer(milliseconds: 500);

  @override
  void initState() {
    super.initState();
    context.read<ArticleCategoriesBloc>().add(LoadAllArticleCategories());
  }

  @override
  void dispose() {
    _searchController.dispose();
    _debouncer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('أقسام المقالات'),
        backgroundColor: AppColors.white,
        elevation: 0,
      ),
      body: BlocConsumer<ArticleCategoriesBloc, ArticleCategoriesState>(
        listener: (context, state) {
          if (state is ArticleCategoriesError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          } else if (state is ArticleCategoryCreated) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إضافة القسم بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is ArticleCategoryUpdated) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم تحديث القسم بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          } else if (state is ArticleCategoryDeleted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حذف القسم بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is ArticleCategoriesLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is ArticleCategoriesLoaded) {
            return _buildCategoriesView(state);
          }

          return const Center(
            child: Text('لا توجد بيانات'),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddCategoryDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildCategoriesView(ArticleCategoriesLoaded state) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          // Search Bar
          Container(
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(12.r),
              boxShadow: [
                BoxShadow(
                  color: AppColors.gray300.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              controller: _searchController,
              onChanged: (query) {
                _debouncer.run(() {
                  if (query.isNotEmpty) {
                    context.read<ArticleCategoriesBloc>().add(SearchArticleCategories(query: query));
                  } else {
                    context.read<ArticleCategoriesBloc>().add(LoadAllArticleCategories());
                  }
                });
              },
              decoration: InputDecoration(
                hintText: 'البحث في الأقسام...',
                prefixIcon: Icon(
                  Icons.search,
                  color: AppColors.textSecondary,
                  size: 20.w,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 12.h,
                ),
              ),
            ),
          ),
          
          SizedBox(height: 16.h),
          
          // Categories List
          Expanded(
            child: state.filteredCategories.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.category_outlined,
                          size: 64.w,
                          color: AppColors.gray400,
                        ),
                        SizedBox(height: 16.h),
                        Text(
                          'لا توجد أقسام',
                          style: TextStyle(
                            fontSize: 18.sp,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    itemCount: state.filteredCategories.length,
                    itemBuilder: (context, index) {
                      final category = state.filteredCategories[index];
                      return _buildCategoryCard(category);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(ArticleCategoryModel category) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.gray300.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            // Icon with color
            Container(
              width: 48.w,
              height: 48.h,
              decoration: BoxDecoration(
                color: Color(int.parse(category.color?.replaceFirst('#', '0xff') ?? '0xff4CAF50'))
                    .withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Icon(
                _getIconData(category.icon),
                color: Color(int.parse(category.color?.replaceFirst('#', '0xff') ?? '0xff4CAF50')),
                size: 24.w,
              ),
            ),
            SizedBox(width: 16.w),
            
            // Category Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    category.name,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  if (category.description != null) ...[
                    SizedBox(height: 4.h),
                    Text(
                      category.description!,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  SizedBox(height: 8.h),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: category.isActive ? AppColors.success : AppColors.error,
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      category.isActive ? 'نشط' : 'غير نشط',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 8.w),
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _showEditCategoryDialog(category);
                    break;
                  case 'delete':
                    _deleteCategory(category.id);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit),
                      SizedBox(width: 8),
                      Text('تعديل'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: AppColors.error),
                      SizedBox(width: 8),
                      Text('حذف', style: TextStyle(color: AppColors.error)),
                    ],
                  ),
                ),
              ],
              icon: Icon(
                Icons.more_vert,
                color: AppColors.textSecondary,
                size: 20.w,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconData(String? iconName) {
    switch (iconName) {
      case 'article':
        return Icons.article;
      case 'medical_services':
        return Icons.medical_services;
      case 'restaurant_menu':
        return Icons.restaurant_menu;
      case 'health_and_safety':
        return Icons.health_and_safety;
      case 'fitness_center':
        return Icons.fitness_center;
      case 'tips_and_updates':
        return Icons.tips_and_updates;
      case 'psychology':
        return Icons.psychology;
      case 'science':
        return Icons.science;
      default:
        return Icons.article;
    }
  }

  void _showAddCategoryDialog() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: context.read<ArticleCategoriesBloc>(),
          child: const ArticleCategoryFormPage(),
        ),
      ),
    );
  }

  void _showEditCategoryDialog(ArticleCategoryModel category) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: context.read<ArticleCategoriesBloc>(),
          child: ArticleCategoryFormPage(
            category: category,
            isEditing: true,
          ),
        ),
      ),
    );
  }

  void _deleteCategory(String categoryId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا القسم؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              context.read<ArticleCategoriesBloc>().add(DeleteArticleCategory(categoryId: categoryId));
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
