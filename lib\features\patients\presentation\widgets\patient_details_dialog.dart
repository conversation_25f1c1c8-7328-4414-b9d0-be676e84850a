import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/patient_model.dart';

class PatientDetailsDialog extends StatelessWidget {
  final PatientModel patient;

  const PatientDetailsDialog({
    super.key,
    required this.patient,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        padding: EdgeInsets.all(20.w),
        constraints: BoxConstraints(
          maxWidth: 400.w,
          maxHeight: 600.h,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                CircleAvatar(
                  radius: 30.r,
                  backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                  child: Icon(
                    Icons.person,
                    size: 30.w,
                    color: AppColors.primary,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        patient.name,
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      if (patient.isPremium)
                        Container(
                          margin: EdgeInsets.only(top: 4.h),
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.w,
                            vertical: 2.h,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                          child: Text(
                            'مميز',
                            style: TextStyle(
                              fontSize: 10.sp,
                              fontWeight: FontWeight.w600,
                              color: AppColors.white,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(
                    Icons.close,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),

            // Patient Details
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDetailRow('رقم الهوية', patient.id),
                    if (patient.phone != null && patient.phone!.isNotEmpty)
                      _buildPhoneRow(patient.phone!),
                    if (patient.email != null && patient.email!.isNotEmpty)
                      _buildDetailRow('البريد الإلكتروني', patient.email!),
                    if (patient.age != null)
                      _buildDetailRow('العمر', '${patient.age} سنة'),
                    if (patient.gender != null)
                      _buildDetailRow('النوع', patient.gender!),
                    if (patient.birthDate != null)
                      _buildDetailRow('تاريخ الميلاد',
                        '${patient.birthDate!.day}/${patient.birthDate!.month}/${patient.birthDate!.year}'),
                    if (patient.height != null)
                      _buildDetailRow('الطول', '${patient.height} سم'),
                    if (patient.weight != null)
                      _buildDetailRow('الوزن', '${patient.weight} كجم'),
                    if (patient.medicalConditions != null && patient.medicalConditions!.isNotEmpty)
                      _buildDetailRow('الحالات الطبية', patient.medicalConditions!),
                    if (patient.allergies != null && patient.allergies!.isNotEmpty)
                      _buildDetailRow('الحساسية', patient.allergies!),
                    if (patient.medications != null && patient.medications!.isNotEmpty)
                      _buildDetailRow('الأدوية', patient.medications!),
                    if (patient.supplements != null && patient.supplements!.isNotEmpty)
                      _buildDetailRow('المكملات', patient.supplements!),
                    if (patient.physicalActivity != null && patient.physicalActivity!.isNotEmpty)
                      _buildDetailRow('النشاط البدني', patient.physicalActivity!),
                    if (patient.notes != null && patient.notes!.isNotEmpty)
                      _buildDetailRow('ملاحظات', patient.notes!),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.w,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhoneRow(String phone) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.w,
            child: Text(
              'رقم الهاتف:',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    phone,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                // Call button
                InkWell(
                  onTap: () => _makePhoneCall(phone),
                  child: Container(
                    padding: EdgeInsets.all(6.w),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6.r),
                    ),
                    child: Icon(
                      Icons.phone,
                      size: 16.w,
                      color: AppColors.primary,
                    ),
                  ),
                ),
                SizedBox(width: 4.w),
                // Copy button
                InkWell(
                  onTap: () => _copyToClipboard(phone),
                  child: Container(
                    padding: EdgeInsets.all(6.w),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6.r),
                    ),
                    child: Icon(
                      Icons.copy,
                      size: 16.w,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _makePhoneCall(String phoneNumber) async {
    try {
      // تنظيف رقم الهاتف من المسافات والرموز الخاصة
      final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

      // استخدام tel: scheme مع DIAL mode
      final Uri phoneUri = Uri.parse('tel:$cleanNumber');

      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(
          phoneUri,
          mode: LaunchMode.externalApplication,
        );
      }
    } catch (e) {
      debugPrint('❌ Error making phone call: $e');
    }
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
  }
}
