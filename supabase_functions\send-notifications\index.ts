import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    console.log('🔍 Checking for due notifications...')
    
    // Get current time
    const now = new Date()
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
    const currentDayOfWeek = now.getDay() === 0 ? 7 : now.getDay()
    
    console.log(`Current time: ${currentTime}, Day: ${currentDayOfWeek}`)
    
    // Get due notifications
    const { data: notifications, error } = await supabase
      .from('scheduled_notifications')
      .select('*')
      .eq('is_active', true)
      .eq('scheduled_time', currentTime)
    
    if (error) {
      console.error('❌ Error fetching notifications:', error)
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    
    // Filter by day of week
    const dueNotifications = notifications.filter(notification => {
      const daysOfWeek = notification.days_of_week || []
      return daysOfWeek.includes(currentDayOfWeek)
    })
    
    console.log(`📊 Found ${dueNotifications.length} due notifications`)
    
    // Send each notification
    for (const notification of dueNotifications) {
      await sendNotificationToPatient(notification, supabase)
    }
    
    return new Response(
      JSON.stringify({ 
        success: true, 
        processed: dueNotifications.length,
        time: currentTime,
        day: currentDayOfWeek
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
    
  } catch (error) {
    console.error('❌ Error in scheduled notifications:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

async function sendNotificationToPatient(notification: any, supabase: any) {
  try {
    const patientId = notification.patient_id
    
    // Get patient info
    const { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('auth_id, name')
      .eq('id', patientId)
      .single()
    
    if (patientError || !patient) {
      console.error(`❌ Patient not found: ${patientId}`)
      return
    }
    
    // Get FCM tokens
    const { data: tokens, error: tokensError } = await supabase
      .from('user_fcm_tokens')
      .select('fcm_token')
      .eq('user_id', patient.auth_id)
      .eq('is_active', true)
    
    if (tokensError || !tokens || tokens.length === 0) {
      console.error(`❌ No FCM tokens found for patient: ${patientId}`)
      return
    }
    
    console.log(`📱 Found ${tokens.length} FCM token(s) for patient: ${patient.name}`)
    
    // Send notifications via FCM (you'll need to implement FCM sending here)
    // For now, just log
    console.log(`✅ Would send notification: ${notification.title} to ${patient.name}`)
    
    // Log notification
    await supabase.from('notification_logs').insert({
      scheduled_notification_id: notification.id,
      patient_id: patientId,
      fcm_token: tokens[0].fcm_token,
      title: notification.title,
      body: notification.body,
      status: 'sent'
    })
    
  } catch (error) {
    console.error('❌ Error sending notification to patient:', error)
  }
}
