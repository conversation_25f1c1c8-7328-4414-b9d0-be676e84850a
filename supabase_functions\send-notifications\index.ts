import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    console.log('🔍 Checking for due notifications...')
    
    // Get current time
    const now = new Date()
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`
    const currentDayOfWeek = now.getDay() === 0 ? 7 : now.getDay()
    
    console.log(`Current time: ${currentTime}, Day: ${currentDayOfWeek}`)
    
    // Get due notifications
    const { data: notifications, error } = await supabase
      .from('scheduled_notifications')
      .select('*')
      .eq('is_active', true)
      .eq('scheduled_time', currentTime)
    
    if (error) {
      console.error('❌ Error fetching notifications:', error)
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    
    // Filter by day of week
    const dueNotifications = notifications.filter(notification => {
      const daysOfWeek = notification.days_of_week || []
      return daysOfWeek.includes(currentDayOfWeek)
    })
    
    console.log(`📊 Found ${dueNotifications.length} due notifications`)
    
    // Send each notification
    for (const notification of dueNotifications) {
      await sendNotificationToPatient(notification, supabase)
    }
    
    return new Response(
      JSON.stringify({ 
        success: true, 
        processed: dueNotifications.length,
        time: currentTime,
        day: currentDayOfWeek
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
    
  } catch (error) {
    console.error('❌ Error in scheduled notifications:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
})

async function sendNotificationToPatient(notification: any, supabase: any) {
  try {
    const patientId = notification.patient_id
    
    // Get patient info
    const { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('auth_id, name')
      .eq('id', patientId)
      .single()
    
    if (patientError || !patient) {
      console.error(`❌ Patient not found: ${patientId}`)
      return
    }
    
    // Get FCM tokens
    const { data: tokens, error: tokensError } = await supabase
      .from('user_fcm_tokens')
      .select('fcm_token')
      .eq('user_id', patient.auth_id)
      .eq('is_active', true)
    
    if (tokensError || !tokens || tokens.length === 0) {
      console.error(`❌ No FCM tokens found for patient: ${patientId}`)
      return
    }
    
    console.log(`📱 Found ${tokens.length} FCM token(s) for patient: ${patient.name}`)
    
    // Prepare notification message
    const title = getNotificationTitle(notification.notification_type)
    const body = `مرحباً ${patient.name}، ${notification.body}`

    // Send to all FCM tokens
    for (const tokenData of tokens) {
      try {
        const success = await sendFCMNotification(tokenData.fcm_token, title, body, {
          type: notification.notification_type,
          patient_id: patientId,
          reminder_id: notification.reminder_id || '',
          scheduled_notification_id: notification.id,
        })

        // Log notification
        await supabase.from('notification_logs').insert({
          scheduled_notification_id: notification.id,
          patient_id: patientId,
          fcm_token: tokenData.fcm_token,
          title: title,
          body: body,
          status: success ? 'sent' : 'failed'
        })

        console.log(`✅ Notification sent to ${patient.name}: ${success}`)

      } catch (error) {
        console.error(`❌ Failed to send to ${tokenData.fcm_token}:`, error)

        // Log error
        await supabase.from('notification_logs').insert({
          scheduled_notification_id: notification.id,
          patient_id: patientId,
          fcm_token: tokenData.fcm_token,
          title: title,
          body: body,
          status: 'failed',
          error_message: error.message
        })
      }
    }
    
  } catch (error) {
    console.error('❌ Error sending notification to patient:', error)
  }
}

// Helper function to get notification title
function getNotificationTitle(notificationType: string): string {
  switch (notificationType) {
    case 'meal':
      return '🍽️ تذكير الوجبة'
    case 'exercise':
      return '🏃‍♂️ تذكير النشاط البدني'
    case 'medication':
      return '💊 تذكير الدواء'
    case 'water':
      return '💧 تذكير شرب الماء'
    default:
      return '🔔 تذكير من Diet Rx'
  }
}

// Send FCM notification
async function sendFCMNotification(
  fcmToken: string,
  title: string,
  body: string,
  data: any
): Promise<boolean> {
  try {
    // Get Firebase service account from environment
    const serviceAccountJson = Deno.env.get('FIREBASE_SERVICE_ACCOUNT')
    if (!serviceAccountJson) {
      console.error('❌ Firebase service account not configured')
      return false
    }

    const serviceAccount = JSON.parse(serviceAccountJson)

    // Get access token (simplified - you might want to cache this)
    const accessToken = await getFirebaseAccessToken(serviceAccount)

    const message = {
      message: {
        token: fcmToken,
        notification: {
          title: title,
          body: body,
        },
        data: data,
        android: {
          priority: 'high',
          notification: {
            channel_id: 'diet_rx_notifications',
            sound: 'default',
            priority: 'high',
          },
        },
        apns: {
          headers: {
            'apns-priority': '10',
          },
          payload: {
            aps: {
              alert: {
                title: title,
                body: body,
              },
              sound: 'default',
              badge: 1,
            },
          },
        },
      },
    }

    const response = await fetch(
      `https://fcm.googleapis.com/v1/projects/deit-rx-30741/messages:send`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
        body: JSON.stringify(message),
      }
    )

    if (response.ok) {
      console.log(`✅ FCM notification sent successfully`)
      return true
    } else {
      const errorText = await response.text()
      console.error(`❌ FCM error: ${response.status} - ${errorText}`)
      return false
    }

  } catch (error) {
    console.error('❌ Error sending FCM notification:', error)
    return false
  }
}

// Get Firebase access token (simplified version)
async function getFirebaseAccessToken(serviceAccount: any): Promise<string> {
  // This is a simplified version - in production you'd want to use proper JWT signing
  // For now, return a placeholder - you'll need to implement proper OAuth2 flow
  throw new Error('Firebase access token generation not implemented yet')
}
